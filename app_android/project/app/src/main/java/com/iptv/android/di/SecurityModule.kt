package com.iptv.android.di

import android.content.Context
import com.iptv.android.core.network.BackendHandler
import com.iptv.android.core.security.DynamicUserAgentManager
import com.iptv.android.core.security.NetworkSecurityManager
import com.iptv.android.core.security.UserAgentRenewalManager
import com.iptv.android.core.security.UserAgentMonitorService
import com.iptv.android.core.security.UserAgentRenewalService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Singleton

/**
 * Security Module for Dependency Injection
 *
 * Provides security-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object SecurityModule {

    @Provides
    @Singleton
    fun provideNetworkSecurityManager(
        @ApplicationContext context: Context
    ): NetworkSecurityManager {
        return NetworkSecurityManager(context)
    }

    @Provides
    @Singleton
    fun provideApplicationCoroutineScope(): CoroutineScope {
        return CoroutineScope(SupervisorJob() + Dispatchers.IO)
    }

    @Provides
    @Singleton
    fun provideDynamicUserAgentManager(
        @ApplicationContext context: Context,
        coroutineScope: CoroutineScope
    ): DynamicUserAgentManager {
        return DynamicUserAgentManager(context, coroutineScope)
    }

    @Provides
    @Singleton
    fun provideBackendHandler(
        @ApplicationContext context: Context
    ): BackendHandler {
        return BackendHandler(context)
    }

    @Provides
    @Singleton
    fun provideUserAgentRenewalService(
        tokenManager: com.iptv.android.data.local.datastore.TokenManager,
        backendHandler: BackendHandler,
        dynamicUserAgentManager: DynamicUserAgentManager
    ): UserAgentRenewalService {
        return UserAgentRenewalService(tokenManager, backendHandler, dynamicUserAgentManager)
    }

    @Provides
    @Singleton
    fun provideUserAgentMonitorService(
        dynamicUserAgentManager: DynamicUserAgentManager,
        authRepository: com.iptv.android.domain.repository.AuthRepository,
        tokenManager: com.iptv.android.data.local.datastore.TokenManager
    ): UserAgentMonitorService {
        return UserAgentMonitorService(dynamicUserAgentManager, authRepository, tokenManager)
    }
}
