package com.iptv.android.data.remote.interceptor

import com.iptv.android.core.network.BackendHandler
import kotlinx.coroutines.runBlocking
import okhttp3.HttpUrl.Companion.toHttpUrl
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Dynamic Base URL Interceptor
 * 
 * Dynamically changes the base URL of requests based on BackendHandler configuration
 */
@Singleton
class DynamicBaseUrlInterceptor @Inject constructor(
    private val backendHandler: BackendHandler
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        return try {
            // Get current base URL from BackendHandler
            val newBaseUrl = runBlocking {
                backendHandler.getBaseUrl()
            }

            // Parse the new base URL
            val newHttpUrl = newBaseUrl.toHttpUrl()

            // Build new URL with the dynamic base URL
            val newUrl = originalRequest.url.newBuilder()
                .scheme(newHttpUrl.scheme)
                .host(newHttpUrl.host)
                .port(newHttpUrl.port)
                .build()

            // Create new request with updated URL
            val newRequest = originalRequest.newBuilder()
                .url(newUrl)
                .build()

            chain.proceed(newRequest)
        } catch (e: Exception) {
            // Check if the request was cancelled (normal when user exits)
            if (e is java.io.IOException && e.message?.contains("Canceled") == true) {
                Timber.d("🔄 Request cancelled in DynamicBaseUrlInterceptor (user exit)")
                throw e // Re-throw cancellation to maintain proper flow
            } else {
                Timber.e(e, "❌ Error in DynamicBaseUrlInterceptor, using default backend URL")

                // Fallback to default backend URL instead of placeholder
                try {
                    // Use the same default values as BackendHandler
                    val defaultBaseUrl = "http://**************:3000/api/"
                    val defaultHttpUrl = defaultBaseUrl.toHttpUrl()

                    val fallbackUrl = originalRequest.url.newBuilder()
                        .scheme(defaultHttpUrl.scheme)
                        .host(defaultHttpUrl.host)
                        .port(defaultHttpUrl.port)
                        .build()

                    val fallbackRequest = originalRequest.newBuilder()
                        .url(fallbackUrl)
                        .build()

                    Timber.i("🔄 Using fallback URL: ${fallbackRequest.url}")
                    chain.proceed(fallbackRequest)
                } catch (fallbackError: Exception) {
                    Timber.e(fallbackError, "❌ Fallback URL construction failed")
                    // Last resort: proceed with original request
                    chain.proceed(originalRequest)
                }
            }
        }
    }
}
