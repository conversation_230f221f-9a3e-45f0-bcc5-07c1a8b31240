package com.iptv.android.data.repository

import com.iptv.android.data.local.dao.ContentDao
import com.iptv.android.data.local.dao.UserDao
import com.iptv.android.data.local.datastore.UserPreferencesManager
import com.iptv.android.data.mapper.*
import com.iptv.android.data.remote.api.IPTVApiService
import com.iptv.android.domain.model.*
import com.iptv.android.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of UserRepository
 * 
 * Handles user operations with both remote API and local storage
 */
@Singleton
class UserRepositoryImpl @Inject constructor(
    private val apiService: IPTVApiService,
    private val userDao: UserDao,
    private val contentDao: ContentDao,
    private val userPreferencesManager: UserPreferencesManager
) : UserRepository {
    
    // ========================================
    // USER PROFILE
    // ========================================
    
    override suspend fun getUserProfile(): Result<UserProfile> {
        return try {
            Timber.d("Fetching user profile")
            
            val response = apiService.getUserProfile()
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    val userProfile = body.toDomainModel()
                    
                    // Cache user profile locally
                    userDao.insertUser(userProfile.toEntity())
                    
                    Timber.d("User profile fetched successfully")
                    Result.Success(userProfile)
                } else {
                    val errorMessage = "User profile not found"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("User profile API error: $errorMessage")
                
                // Try to return cached data
                @Suppress("UNUSED_VARIABLE")
                val cachedUser = userDao.getUserProfile()
                // TODO: Convert Flow to single value for fallback
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "User profile fetch exception")
            Result.Error(e)
        }
    }
    
    override suspend fun updateUserProfile(profile: UserProfile): Result<UserProfile> {
        return try {
            Timber.d("Updating user profile")
            
            val response = apiService.updateUserProfile(profile.toDto())
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    val updatedProfile = body.toDomainModel()
                    
                    // Update local cache
                    userDao.insertUser(updatedProfile.toEntity())
                    
                    Timber.d("User profile updated successfully")
                    Result.Success(updatedProfile)
                } else {
                    val errorMessage = "Failed to update user profile"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Update user profile API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Update user profile exception")
            Result.Error(e)
        }
    }
    
    override fun getUserPreferences(): Flow<UserPreferences> {
        return userPreferencesManager.getAllPreferences().map { prefs ->
            UserPreferences(
                language = prefs.language,
                adultContent = prefs.adultContent,
                autoplay = prefs.autoplay,
                quality = VideoQuality.valueOf(prefs.videoQuality.uppercase()),
                subtitles = prefs.subtitles
            )
        }
    }
    
    override suspend fun updateUserPreferences(preferences: UserPreferences): Result<Unit> {
        return try {
            Timber.d("Updating user preferences")
            
            // Update local preferences
            userPreferencesManager.saveLanguage(preferences.language)
            userPreferencesManager.saveAdultContent(preferences.adultContent)
            userPreferencesManager.saveAutoplay(preferences.autoplay)
            userPreferencesManager.saveVideoQuality(preferences.quality.name)
            userPreferencesManager.saveSubtitles(preferences.subtitles)
            
            // TODO: Sync with API if needed
            
            Timber.d("User preferences updated successfully")
            Result.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Update user preferences exception")
            Result.Error(e)
        }
    }
    
    // ========================================
    // FAVORITES
    // ========================================
    
    override suspend fun getFavorites(): Result<List<FavoriteItem>> {
        return try {
            Timber.d("Fetching favorites")
            
            val response = apiService.getFavorites()
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.success == true) {
                    val favorites = body.favorites.map { contentDto ->
                        FavoriteItem(
                            contentId = contentDto.id.toString(),
                            content = contentDto.toDomainModel(),
                            addedAt = System.currentTimeMillis().toString()
                        )
                    }
                    
                    Timber.d("Fetched ${favorites.size} favorites")
                    Result.Success(favorites)
                } else {
                    val errorMessage = "Failed to fetch favorites"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Favorites API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Favorites fetch exception")
            Result.Error(e)
        }
    }
    
    override suspend fun addToFavorites(contentId: String): Result<Unit> {
        return try {
            Timber.d("Adding to favorites: $contentId")
            
            val response = apiService.addToFavorites(contentId)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.success == true) {
                    // Add to local cache
                    contentDao.insertFavorite(
                        com.iptv.android.data.local.entity.FavoriteEntity(
                            contentId = contentId,
                            addedAt = System.currentTimeMillis().toString()
                        )
                    )
                    
                    Timber.d("Added to favorites successfully")
                    Result.Success(Unit)
                } else {
                    val errorMessage = body?.message ?: "Failed to add to favorites"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Add to favorites API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Add to favorites exception")
            Result.Error(e)
        }
    }
    
    override suspend fun removeFromFavorites(contentId: String): Result<Unit> {
        return try {
            Timber.d("Removing from favorites: $contentId")
            
            val response = apiService.removeFromFavorites(contentId)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.success == true) {
                    // Remove from local cache
                    contentDao.deleteFavoriteByContentId(contentId)
                    
                    Timber.d("Removed from favorites successfully")
                    Result.Success(Unit)
                } else {
                    val errorMessage = body?.message ?: "Failed to remove from favorites"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Remove from favorites API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Remove from favorites exception")
            Result.Error(e)
        }
    }
    
    override suspend fun isFavorite(contentId: String): Result<Boolean> {
        return try {
            val favorite = contentDao.getFavorite(contentId)
            Result.Success(favorite != null)
        } catch (e: Exception) {
            Timber.e(e, "Check favorite exception")
            Result.Error(e)
        }
    }
    
    override fun getFavoritesFlow(): Flow<List<FavoriteItem>> {
        return contentDao.getFavoriteContent().map { entities ->
            entities.map { entity ->
                FavoriteItem(
                    contentId = entity.id,
                    content = entity.toDomainModel(),
                    addedAt = System.currentTimeMillis().toString()
                )
            }
        }
    }
    
    // ========================================
    // WATCH HISTORY
    // ========================================
    
    override suspend fun getWatchHistory(): Result<List<WatchHistoryItem>> {
        return try {
            Timber.d("Fetching watch history")
            
            val response = apiService.getWatchHistory()
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.success == true) {
                    val history = body.history.toWatchHistoryItems()
                    
                    Timber.d("Fetched ${history.size} watch history items")
                    Result.Success(history)
                } else {
                    val errorMessage = "Failed to fetch watch history"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Watch history API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Watch history fetch exception")
            Result.Error(e)
        }
    }
    
    override suspend fun updateWatchProgress(
        contentId: String,
        progress: Float,
        duration: Long
    ): Result<Unit> {
        return try {
            Timber.d("Updating watch progress: $contentId, progress: $progress")
            
            val request = com.iptv.android.data.remote.dto.UpdateWatchProgressRequestDto(
                contentId = contentId,
                progress = progress,
                duration = duration
            )
            
            val response = apiService.updateWatchProgress(request)
            
            if (response.isSuccessful) {
                // Update local cache
                contentDao.insertWatchHistory(
                    com.iptv.android.data.local.entity.WatchHistoryEntity(
                        contentId = contentId,
                        watchedAt = System.currentTimeMillis().toString(),
                        progress = progress,
                        duration = duration
                    )
                )
                
                Timber.d("Watch progress updated successfully")
                Result.Success(Unit)
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Update watch progress API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            // Handle cancellation gracefully (when user exits player)
            if (e is kotlinx.coroutines.CancellationException ||
                (e is java.io.IOException && e.message?.contains("Canceled") == true)) {
                Timber.d("Update watch progress cancelled (user exit)")
                // Still save to local cache even if network request was cancelled
                try {
                    contentDao.insertWatchHistory(
                        com.iptv.android.data.local.entity.WatchHistoryEntity(
                            contentId = contentId,
                            watchedAt = System.currentTimeMillis().toString(),
                            progress = progress,
                            duration = duration
                        )
                    )
                    Timber.d("Watch progress saved locally despite cancellation")
                } catch (localE: Exception) {
                    Timber.w(localE, "Failed to save progress locally")
                }
                Result.Error(e)
            } else {
                Timber.e(e, "Update watch progress exception")
                Result.Error(e)
            }
        }
    }
    
    override suspend fun getWatchProgress(contentId: String): Result<WatchProgress?> {
        return try {
            val watchHistory = contentDao.getWatchHistory(contentId)
            val progress = watchHistory?.let {
                WatchProgress(
                    contentId = it.contentId,
                    progress = it.progress,
                    duration = it.duration,
                    lastWatched = it.watchedAt
                )
            }
            Result.Success(progress)
        } catch (e: Exception) {
            Timber.e(e, "Get watch progress exception")
            Result.Error(e)
        }
    }
    
    override suspend fun getContinueWatching(): Result<List<WatchHistoryItem>> {
        return try {
            // Get from local cache for now
            // TODO: Implement API endpoint
            Result.Success(emptyList())
        } catch (e: Exception) {
            Timber.e(e, "Get continue watching exception")
            Result.Error(e)
        }
    }
    
    override suspend fun clearWatchHistory(): Result<Unit> {
        return try {
            contentDao.clearAllWatchHistory()
            Timber.d("Watch history cleared")
            Result.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Clear watch history exception")
            Result.Error(e)
        }
    }
    
    // ========================================
    // DEVICE MANAGEMENT
    // ========================================
    
    override suspend fun getUserDevices(): Result<List<DeviceInfo>> {
        return try {
            Timber.d("Fetching user devices")
            
            val response = apiService.getUserDevices()
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.success == true && body.data != null) {
                    val devices = body.data.toDeviceInfoList()
                    
                    Timber.d("Fetched ${devices.size} devices")
                    Result.Success(devices)
                } else {
                    val errorMessage = "Failed to fetch devices"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("User devices API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "User devices fetch exception")
            Result.Error(e)
        }
    }
    
    override suspend fun removeDevice(deviceId: String): Result<Unit> {
        return try {
            Timber.d("Removing device: $deviceId")

            val response = apiService.removeDevice(deviceId)

            if (response.isSuccessful) {
                val body = response.body()
                if (body?.success == true) {
                    Timber.d("Device removed successfully: ${body.message}")
                    Result.Success(Unit)
                } else {
                    val errorMessage = body?.message ?: "Failed to remove device"
                    Timber.w("Device removal failed: $errorMessage")
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Remove device API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Remove device exception")
            Result.Error(e)
        }
    }
    
    override suspend fun updateDeviceName(deviceId: String, name: String): Result<Unit> {
        return try {
            // TODO: Implement update device name API
            Timber.d("Update device name not implemented: $deviceId, $name")
            Result.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Update device name exception")
            Result.Error(e)
        }
    }
    
    // ========================================
    // PARENTAL CONTROL
    // ========================================

    override suspend fun setParentalPin(request: ParentalPinRequest): Result<ParentalPinResponse> {
        return try {
            Timber.d("Setting parental PIN")

            val response = apiService.setParentalPin(request.toDto())

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    val result = body.toDomainModel()
                    Timber.d("Parental PIN set successfully")
                    Result.Success(result)
                } else {
                    val errorMessage = "Failed to set parental PIN"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Set parental PIN API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Set parental PIN exception")
            Result.Error(e)
        }
    }

    override suspend fun verifyParentalPin(request: ParentalPinRequest): Result<ParentalPinResponse> {
        return try {
            Timber.d("Verifying parental PIN")

            val response = apiService.verifyParentalPin(request.toDto())

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    val result = body.toDomainModel()
                    Timber.d("Parental PIN verification result: ${result.valid}")
                    Result.Success(result)
                } else {
                    val errorMessage = "Failed to verify parental PIN"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Verify parental PIN API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Verify parental PIN exception")
            Result.Error(e)
        }
    }

    override suspend fun removeParentalPin(): Result<ParentalPinResponse> {
        return try {
            Timber.d("Removing parental PIN")

            val response = apiService.removeParentalPin()

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    val result = body.toDomainModel()
                    Timber.d("Parental PIN removed successfully")
                    Result.Success(result)
                } else {
                    val errorMessage = "Failed to remove parental PIN"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Remove parental PIN API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Remove parental PIN exception")
            Result.Error(e)
        }
    }

    override suspend fun hasParentalPin(): Result<ParentalPinResponse> {
        return try {
            Timber.d("Checking parental PIN status")

            val response = apiService.hasParentalPin()

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    val result = body.toDomainModel()
                    Timber.d("Parental PIN status: ${result.valid}")
                    Result.Success(result)
                } else {
                    val errorMessage = "Failed to check parental PIN status"
                    Timber.w(errorMessage)
                    Result.Error(Exception(errorMessage))
                }
            } else {
                val errorMessage = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("Check parental PIN status API error: $errorMessage")
                Result.Error(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Timber.e(e, "Check parental PIN status exception")
            Result.Error(e)
        }
    }

    // ========================================
    // LOCAL CACHE
    // ========================================

    override suspend fun cacheUserData(profile: UserProfile): Result<Unit> {
        return try {
            userDao.insertUser(profile.toEntity())
            Timber.d("User data cached")
            Result.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Cache user data exception")
            Result.Error(e)
        }
    }
    
    override fun getCachedUserData(): Flow<UserProfile?> {
        return userDao.getUserProfile().map { entity ->
            entity?.toDomainModel()
        }
    }
    
    override suspend fun clearUserCache(): Result<Unit> {
        return try {
            userDao.clearUserProfile()
            Timber.d("User cache cleared")
            Result.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "Clear user cache exception")
            Result.Error(e)
        }
    }
}
