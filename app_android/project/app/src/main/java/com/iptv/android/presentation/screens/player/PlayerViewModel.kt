package com.iptv.android.presentation.screens.player

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.iptv.android.core.error.ErrorHandler
import com.iptv.android.core.utils.logd
import com.iptv.android.core.utils.loge
import com.iptv.android.domain.model.Result
import com.iptv.android.domain.model.*
import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase
import com.iptv.android.domain.usecase.content.GetContentStreamUseCase
import com.iptv.android.domain.usecase.series.GetEpisodeStreamUseCase
import com.iptv.android.domain.usecase.user.UpdateWatchProgressUseCase
import com.iptv.android.domain.usecase.content.GetContentByCategoryUseCase
import com.iptv.android.domain.usecase.content.GetLiveTVChannelsUseCase
import com.iptv.android.presentation.player.ExoPlayerManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * ViewModel for Player Screen
 * 
 * Handles video playback and progress tracking
 */
@HiltViewModel
class PlayerViewModel @Inject constructor(
    private val getContentDetailsUseCase: GetContentDetailsUseCase,
    private val getContentStreamUseCase: GetContentStreamUseCase,
    private val getEpisodeStreamUseCase: GetEpisodeStreamUseCase,
    private val updateWatchProgressUseCase: UpdateWatchProgressUseCase,
    private val getContentByCategoryUseCase: GetContentByCategoryUseCase,
    private val getLiveTVChannelsUseCase: GetLiveTVChannelsUseCase,
    private val exoPlayerManager: ExoPlayerManager,
    private val errorHandler: ErrorHandler
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(PlayerUiState())
    val uiState: StateFlow<PlayerUiState> = _uiState.asStateFlow()

    // Live TV channels state
    private val _liveTVChannels = MutableStateFlow<List<ContentStream>>(emptyList())
    val liveTVChannels: StateFlow<List<ContentStream>> = _liveTVChannels.asStateFlow()

    private val _isChannelSidebarVisible = MutableStateFlow(false)
    val isChannelSidebarVisible: StateFlow<Boolean> = _isChannelSidebarVisible.asStateFlow()

    private val _isAudioSelectorVisible = MutableStateFlow(false)
    val isAudioSelectorVisible: StateFlow<Boolean> = _isAudioSelectorVisible.asStateFlow()
    
    init {
        logd("PlayerViewModel initialized")
    }
    
    /**
     * Load content for playback
     */
    fun loadContent(contentId: String) {
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    contentState = UiState.Loading,
                    error = null
                )
            }

            try {
                logd("Loading content for playback: $contentId")

                when (val result = getContentDetailsUseCase(contentId)) {
                    is Result.Success -> {
                        val content = result.data
                        logd("Content loaded successfully: ${content.title}")

                        _uiState.update {
                            it.copy(
                                contentState = UiState.Success(content),
                                duration = content.duration ?: 0L,
                                error = null
                            )
                        }

                        // Validate stream URL for non-series content
                        if (content.type != ContentType.SERIES && content.streamUrl.isNullOrBlank()) {
                            _uiState.update {
                                it.copy(
                                    contentState = UiState.Error("URL de transmisión no disponible"),
                                    error = "URL de transmisión no disponible"
                                )
                            }
                        }
                    }

                    is Result.Error -> {
                        val errorMessage = errorHandler.getErrorMessage(result.exception)
                        loge("Failed to load content: $errorMessage", result.exception)
                        _uiState.update {
                            it.copy(
                                contentState = UiState.Error(errorMessage),
                                error = errorMessage
                            )
                        }
                    }

                    is Result.Loading -> {
                        // Should not happen in this use case
                        logd("Unexpected loading state in content loading")
                    }
                }
            } catch (e: Exception) {
                val errorMessage = errorHandler.getErrorMessage(e)
                loge("Content loading exception: $errorMessage", e)
                _uiState.update {
                    it.copy(
                        contentState = UiState.Error(errorMessage),
                        error = errorMessage
                    )
                }
            }
        }
    }

    /**
     * Load content stream for playback (with DRM support for Live TV/Events)
     */
    fun loadContentStream(contentId: String) {
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    contentState = UiState.Loading,
                    error = null
                )
            }

            try {
                logd("Loading content stream for playback: $contentId")

                _uiState.update { it.copy(contentState = UiState.Loading) }

                val result = getContentStreamUseCase(contentId)
                when (result) {
                    is Result.Loading -> {
                        _uiState.update {
                            it.copy(contentState = UiState.Loading)
                        }
                    }

                    is Result.Success -> {
                            val contentStream = result.data
                            logd("Content stream loaded successfully: ${contentStream.title}")

                            _uiState.update {
                                it.copy(
                                    contentState = UiState.Success(contentStream),
                                    error = null
                                )
                            }

                            // Setup player with DRM support if needed (use hex keys like working implementation)
                            setupPlayerForStream(contentStream.streamUrl, contentStream.drmKeys)
                        }

                    is Result.Error -> {
                        val errorMessage = result.exception.message ?: "Failed to load content stream"
                        loge("Failed to load content stream: $errorMessage")
                        _uiState.update {
                            it.copy(
                                contentState = UiState.Error(errorMessage),
                                error = errorMessage
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                val errorMessage = errorHandler.getErrorMessage(e)
                loge("Content stream loading exception: $errorMessage", e)
                _uiState.update {
                    it.copy(
                        contentState = UiState.Error(errorMessage),
                        error = errorMessage
                    )
                }
            }
        }
    }

    /**
     * Get current content from UI state
     */
    private fun getCurrentContent(): Any? {
        return when (val state = _uiState.value.contentState) {
            is UiState.Success -> state.data
            else -> null
        }
    }

    /**
     * Navigate to previous episode
     */
    fun navigateToPreviousEpisode() {
        val currentContent = getCurrentContent()
        if (currentContent is ContentDetails && currentContent.type == ContentType.SERIES) {
            val episodes = currentContent.episodes ?: return
            val currentEpisodeId = getCurrentEpisodeId() ?: return
            val currentIndex = episodes.indexOfFirst { episode -> episode.id.toString() == currentEpisodeId }

            if (currentIndex > 0) {
                val previousEpisode = episodes[currentIndex - 1]
                loadEpisodeStream(previousEpisode.id.toString())
                logd("Navigating to previous episode: ${previousEpisode.title}")
            }
        }
    }

    /**
     * Navigate to next episode
     */
    fun navigateToNextEpisode() {
        val currentContent = getCurrentContent()
        if (currentContent is ContentDetails && currentContent.type == ContentType.SERIES) {
            val episodes = currentContent.episodes ?: return
            val currentEpisodeId = getCurrentEpisodeId() ?: return
            val currentIndex = episodes.indexOfFirst { episode -> episode.id.toString() == currentEpisodeId }

            if (currentIndex < episodes.size - 1) {
                val nextEpisode = episodes[currentIndex + 1]
                loadEpisodeStream(nextEpisode.id.toString())
                logd("Navigating to next episode: ${nextEpisode.title}")
            }
        }
    }

    /**
     * Navigate to specific episode
     */
    fun navigateToEpisode(episode: Episode) {
        loadEpisodeStream(episode.id.toString())
        logd("Navigating to episode: ${episode.title}")
    }

    /**
     * Get current episode ID if playing an episode
     */
    fun getCurrentEpisodeId(): String? {
        return when (val content = getCurrentContent()) {
            is EpisodeStream -> content.id.toString()
            else -> null
        }
    }

    /**
     * Load episode stream for playback
     */
    fun loadEpisodeStream(episodeId: String) {
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    contentState = UiState.Loading,
                    error = null
                )
            }

            try {
                logd("Loading episode stream for playback: $episodeId")

                _uiState.update { it.copy(contentState = UiState.Loading) }

                val result = getEpisodeStreamUseCase(episodeId)
                when (result) {
                    is Result.Loading -> {
                        _uiState.update {
                            it.copy(contentState = UiState.Loading)
                        }
                    }

                    is Result.Success -> {
                            val episodeStream = result.data
                            logd("Episode stream loaded successfully: ${episodeStream.title}")

                            _uiState.update {
                                it.copy(
                                    contentState = UiState.Success(episodeStream),
                                    duration = episodeStream.duration ?: 0L,
                                    error = null
                                )
                            }

                            // Setup player (episodes don't have DRM)
                            setupPlayerForStream(episodeStream.streamUrl, null)
                        }

                    is Result.Error -> {
                        val errorMessage = result.exception.message ?: "Failed to load episode stream"
                        loge("Failed to load episode stream: $errorMessage")
                        _uiState.update {
                            it.copy(
                                contentState = UiState.Error(errorMessage),
                                error = errorMessage
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                val errorMessage = errorHandler.getErrorMessage(e)
                loge("Episode stream loading exception: $errorMessage", e)
                _uiState.update {
                    it.copy(
                        contentState = UiState.Error(errorMessage),
                        error = errorMessage
                    )
                }
            }
        }
    }

    /**
     * Setup ExoPlayer for streaming with optional DRM support
     */
    private fun setupPlayerForStream(streamUrl: String, drmKeysHex: String?) {
        try {
            logd("Setting up player for stream: $streamUrl")
            logd("DRM Keys available: ${!drmKeysHex.isNullOrEmpty()}")

            // Validate stream URL
            if (streamUrl.isBlank()) {
                val errorMessage = "Stream URL is empty or invalid"
                loge(errorMessage)
                _uiState.update {
                    it.copy(
                        error = errorMessage,
                        isPlaying = false
                    )
                }
                return
            }

            // Validate URL format
            if (!isValidStreamUrl(streamUrl)) {
                val errorMessage = "Invalid stream URL format: $streamUrl"
                loge(errorMessage)
                _uiState.update {
                    it.copy(
                        error = errorMessage,
                        isPlaying = false
                    )
                }
                return
            }

            if (!drmKeysHex.isNullOrEmpty()) {
                logd("Setting up player with DRM support")
                logd("DRM Keys hex format: $drmKeysHex")
                exoPlayerManager.prepareAndPlayWithDrm(streamUrl, drmKeysHex)
            } else {
                logd("Setting up player with automatic M3U8 analysis for potential AES-128 encryption")
                // Use the new method that automatically detects AES-128 in M3U8 files
                exoPlayerManager.prepareAndPlayWithAnalysis(streamUrl)
            }

            // Setup player callbacks
            exoPlayerManager.onError = { exception ->
                val errorMessage = "Playback error: ${exception.message}"
                loge(errorMessage, exception)
                _uiState.update {
                    it.copy(
                        error = errorMessage,
                        isPlaying = false
                    )
                }
            }

            exoPlayerManager.onPositionUpdate = { position ->
                _uiState.update {
                    it.copy(currentPosition = position)
                }
            }

            exoPlayerManager.onDurationUpdate = { duration ->
                _uiState.update {
                    it.copy(duration = duration)
                }
            }

            exoPlayerManager.onPlayingStateChanged = { playing ->
                _uiState.update {
                    it.copy(isPlaying = playing)
                }
            }

            // Update audio tracks info after a short delay to allow player initialization
            viewModelScope.launch {
                delay(3000) // Wait for player to fully initialize
                updateAudioTracksInfo()
                logd("Audio tracks info updated")

                // Try again after more time if no tracks found
                delay(3000)
                updateAudioTracksInfo()
                logd("Audio tracks info updated (second attempt)")
            }

        } catch (e: Exception) {
            val errorMessage = "Failed to setup player: ${e.message}"
            loge(errorMessage, e)
            _uiState.update {
                it.copy(
                    error = errorMessage,
                    isPlaying = false
                )
            }
        }
    }

    /**
     * Validate if the stream URL is in a valid format
     */
    private fun isValidStreamUrl(url: String): Boolean {
        return try {
            when {
                url.startsWith("http://") || url.startsWith("https://") -> {
                    // Valid HTTP/HTTPS URL
                    true
                }
                url.startsWith("rtmp://") || url.startsWith("rtmps://") -> {
                    // Valid RTMP URL
                    true
                }
                else -> {
                    // Invalid URL format
                    false
                }
            }
        } catch (e: Exception) {
            logd("URL validation failed: ${e.message}")
            false
        }
    }

    /**
     * Get ExoPlayer instance
     */
    fun getExoPlayer() = exoPlayerManager.exoPlayer

    /**
     * Toggle play/pause
     */
    fun togglePlayPause() {
        // Use ExoPlayerManager's togglePlayPause which will automatically
        // update the UI state through the onPlayingStateChanged callback
        exoPlayerManager.togglePlayPause()
        logd("Play/pause toggled")
    }
    
    /**
     * Seek to position
     */
    fun seekTo(position: Long) {
        // Actually seek in ExoPlayer
        exoPlayerManager.seekTo(position)

        // Update UI state
        _uiState.update { it.copy(currentPosition = position) }
        logd("Seeked to position: $position")

        // Update watch progress
        updateWatchProgress()
    }
    
    /**
     * Update current playback position
     */
    fun updatePosition(position: Long) {
        _uiState.update { it.copy(currentPosition = position) }
        
        // Periodically update watch progress
        if (position % 30000 == 0L) { // Every 30 seconds
            updateWatchProgress()
        }
    }
    
    /**
     * Set playback duration
     */
    fun setDuration(duration: Long) {
        _uiState.update { it.copy(duration = duration) }
        logd("Duration set: $duration")
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    /**
     * Handle player error
     */
    fun onPlayerError(error: Throwable) {
        val errorMessage = errorHandler.getErrorMessage(error)
        loge("Player error: $errorMessage", error)
        _uiState.update { 
            it.copy(
                contentState = UiState.Error(errorMessage),
                error = errorMessage,
                isPlaying = false
            ) 
        }
    }
    
    /**
     * Update watch progress in background
     */
    private fun updateWatchProgress() {
        val currentState = _uiState.value
        val content = (currentState.contentState as? UiState.Success)?.data ?: return
        
        if (currentState.duration > 0) {
            val progress = currentState.currentPosition.toFloat() / currentState.duration.toFloat()
            
            viewModelScope.launch {
                try {
                    val contentId = when (content) {
                        is ContentStream -> content.id.toString()
                        is EpisodeStream -> content.id.toString()
                        else -> "0"
                    }

                    updateWatchProgressUseCase(
                        contentId = contentId,
                        progress = progress,
                        duration = currentState.currentPosition
                    )
                    logd("Watch progress updated: $progress")
                } catch (e: Exception) {
                    loge("Failed to update watch progress", e)
                    // Don't show error to user for background operation
                }
            }
        }
    }
    
    /**
     * Called when player is destroyed
     */
    fun onPlayerDestroyed() {
        logd("Player destroyed, saving final progress")
        updateWatchProgress()
    }

    /**
     * Clean up resources when ViewModel is cleared
     */
    override fun onCleared() {
        super.onCleared()
        logd("Player destroyed, saving final progress")

        // Use a separate scope for final progress save to avoid cancellation
        try {
            runBlocking {
                updateWatchProgress() // Save final progress before clearing
            }
        } catch (e: Exception) {
            logd("Final progress save completed with cancellation (normal)")
        }

        logd("PlayerViewModel cleared, releasing player resources")
        exoPlayerManager.release()
    }

    /**
     * Check if current content is Live TV (category_id = 2)
     */
    fun isLiveTV(): Boolean {
        val currentContent = when (val state = _uiState.value.contentState) {
            is UiState.Success -> state.data
            else -> return false
        }

        return when (currentContent) {
            is ContentStream -> currentContent.categoryId == "2" // Live TV category
            else -> false
        }
    }

    /**
     * Load Live TV channels for sidebar
     */
    fun loadLiveTVChannels() {
        if (!isLiveTV()) return

        viewModelScope.launch {
            try {
                logd("Loading Live TV channels for sidebar")
                // Use the same UseCase that works correctly (returns 93 channels)
                val result = getLiveTVChannelsUseCase()

                when (result) {
                    is Result.Success -> {
                        val channels = result.data.map { content ->
                            logd("Mapping channel: ID=${content.id}, Title=${content.title}")
                            ContentStream(
                                id = content.id,
                                categoryId = "2",
                                title = content.title,
                                description = content.description,
                                imageUrl = content.imageUrl,
                                streamUrl = content.streamUrl ?: "",
                                tmdbId = null, // ContentItem doesn't have tmdbId
                                year = content.year,
                                channelNumber = null, // ContentItem doesn't have channelNumber, will be set from backend
                                createdAt = content.createdAt,
                                drmKeys = content.drmKeys,
                                drmKeysExo = null
                            )
                        }
                        _liveTVChannels.value = channels
                        logd("Loaded ${channels.size} Live TV channels for sidebar")
                        channels.forEachIndexed { index, channel ->
                            logd("Channel $index: ID=${channel.id}, Title=${channel.title}")
                        }
                    }

                    is Result.Error -> {
                        loge("Failed to load Live TV channels", result.exception)
                    }

                    is Result.Loading -> {
                        // Handle loading state if needed
                    }
                }
            } catch (e: Exception) {
                loge("Exception loading Live TV channels", e)
            }
        }
    }

    /**
     * Toggle channel sidebar visibility
     */
    fun toggleChannelSidebar() {
        if (!isLiveTV()) return

        val newVisibility = !_isChannelSidebarVisible.value
        _isChannelSidebarVisible.value = newVisibility

        if (newVisibility && _liveTVChannels.value.isEmpty()) {
            loadLiveTVChannels()
        }

        logd("Channel sidebar toggled: $newVisibility")
    }

    /**
     * Hide channel sidebar
     */
    fun hideChannelSidebar() {
        _isChannelSidebarVisible.value = false
    }

    /**
     * Toggle audio track selector
     */
    fun toggleAudioSelector() {
        val newVisibility = !_isAudioSelectorVisible.value
        _isAudioSelectorVisible.value = newVisibility

        if (newVisibility) {
            // Update audio tracks info when showing selector
            updateAudioTracksInfo()
        }

        logd("Audio selector toggled: $newVisibility")
    }

    /**
     * Hide audio track selector
     */
    fun hideAudioSelector() {
        _isAudioSelectorVisible.value = false
    }

    /**
     * Navigate to channel by number
     */
    fun navigateToChannelByNumber(channelNumber: String) {
        if (!isLiveTV()) return

        viewModelScope.launch {
            try {
                val channels = _liveTVChannels.value
                val targetChannel = channels.find { channel ->
                    // Try to match by channel number or name
                    channel.title.contains(channelNumber, ignoreCase = true) ||
                    channel.id == channelNumber
                }

                targetChannel?.let { channel ->
                    logd("Navigating to channel by number: $channelNumber -> ${channel.title}")
                    switchToChannel(channel)
                } ?: run {
                    logd("Channel not found for number: $channelNumber")
                }
            } catch (e: Exception) {
                loge("Error navigating to channel by number: $channelNumber", e)
            }
        }
    }

    /**
     * Get channel suggestions for search
     */
    fun getChannelSuggestions(query: String): List<String> {
        if (query.isEmpty()) return emptyList()

        return _liveTVChannels.value
            .filter { channel ->
                channel.title.contains(query, ignoreCase = true)
            }
            .map { it.title }
            .take(5)
    }

    /**
     * Switch to a different Live TV channel
     */
    fun switchToChannel(channel: ContentStream) {
        if (!isLiveTV()) return

        viewModelScope.launch {
            try {
                logd("Switching to channel: ${channel.title} (ID: ${channel.id})")

                // Show loading state
                _uiState.update {
                    it.copy(
                        contentState = UiState.Loading,
                        error = null
                    )
                }

                // Get the complete stream data from API (like loadContentStream does)
                val result = getContentStreamUseCase(channel.id)
                when (result) {
                    is Result.Success -> {
                        val fullContentStream = result.data
                        logd("Got full stream data for channel: ${fullContentStream.title}")
                        logd("Stream URL: ${fullContentStream.streamUrl}")
                        logd("DRM Keys: ${fullContentStream.drmKeys}")

                        // Update UI state with complete channel data
                        _uiState.update {
                            it.copy(
                                contentState = UiState.Success(fullContentStream),
                                error = null
                            )
                        }

                        // Setup player for new channel with complete data
                        setupPlayerForStream(fullContentStream.streamUrl, fullContentStream.drmKeys)

                        logd("Successfully switched to channel: ${fullContentStream.title}")
                    }

                    is Result.Error -> {
                        val errorMessage = "Failed to get stream for channel: ${result.exception.message}"
                        loge(errorMessage, result.exception)
                        _uiState.update {
                            it.copy(
                                contentState = UiState.Error(errorMessage),
                                error = errorMessage
                            )
                        }
                    }

                    is Result.Loading -> {
                        // Keep loading state
                    }
                }

                // Hide sidebar
                hideChannelSidebar()

            } catch (e: Exception) {
                val errorMessage = "Failed to switch channel: ${e.message}"
                loge(errorMessage, e)
                _uiState.update {
                    it.copy(
                        contentState = UiState.Error(errorMessage),
                        error = errorMessage
                    )
                }
            }
        }
    }

    /**
     * Get current channel ID for sidebar highlighting
     */
    fun getCurrentChannelId(): String? {
        val currentContent = when (val state = _uiState.value.contentState) {
            is UiState.Success -> state.data
            else -> {
                logd("getCurrentChannelId: No content loaded yet")
                return null
            }
        }

        val channelId = when (currentContent) {
            is ContentStream -> {
                logd("getCurrentChannelId: Current channel ID=${currentContent.id}, Title=${currentContent.title}")
                currentContent.id
            }
            else -> {
                logd("getCurrentChannelId: Current content is not ContentStream: ${currentContent::class.simpleName}")
                null
            }
        }

        logd("getCurrentChannelId: Returning $channelId")
        return channelId
    }

    /**
     * Get available audio tracks for current content
     */
    fun getAvailableAudioTracks(): List<AudioTrack> {
        return exoPlayerManager.getAvailableAudioTracks()
    }

    /**
     * Select audio track by language
     */
    fun selectAudioTrack(audioTrack: AudioTrack) {
        exoPlayerManager.selectAudioTrack(audioTrack)

        // Update UI state
        val availableTracks = getAvailableAudioTracks()
        _uiState.update { currentState ->
            currentState.copy(
                availableAudioTracks = availableTracks,
                selectedAudioTrack = availableTracks.find { it.isSelected }
            )
        }

        logd("Audio track selected: ${audioTrack.label} (${audioTrack.language})")
    }

    /**
     * Reset audio track to automatic selection
     */
    fun resetAudioTrack() {
        exoPlayerManager.resetAudioTrack()

        // Update UI state
        val availableTracks = getAvailableAudioTracks()
        _uiState.update { currentState ->
            currentState.copy(
                availableAudioTracks = availableTracks,
                selectedAudioTrack = null
            )
        }

        logd("Audio track reset to automatic")
    }

    /**
     * Update audio tracks info in UI state
     */
    fun updateAudioTracksInfo() {
        logd("DEBUG: Starting updateAudioTracksInfo()")
        logd("DEBUG: ExoPlayerManager instance: ${exoPlayerManager}")
        val availableTracks = getAvailableAudioTracks()
        logd("DEBUG: Available audio tracks: ${availableTracks.size}")
        availableTracks.forEach { track ->
            logd("DEBUG: Track - ${track.label} (${track.language})")
        }

        _uiState.update { currentState ->
            currentState.copy(
                availableAudioTracks = availableTracks,
                selectedAudioTrack = availableTracks.find { it.isSelected },
                hasMultipleAudioTracks = availableTracks.size > 1
            )
        }

        logd("DEBUG: hasMultipleAudioTracks = ${availableTracks.size > 1}")
    }
}

/**
 * UI State for Player Screen
 */
data class PlayerUiState(
    val contentState: UiState<Any> = UiState.Loading, // Can be ContentDetails, ContentStream, or EpisodeStream
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val duration: Long = 0L,
    val error: String? = null,
    val availableAudioTracks: List<AudioTrack> = emptyList(),
    val selectedAudioTrack: AudioTrack? = null,
    val hasMultipleAudioTracks: Boolean = false
)
