package com.iptv.android.data.remote.interceptor

import com.iptv.android.core.security.DynamicUserAgentManager
import com.iptv.android.core.security.UserAgentRenewalService
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Dynamic User-Agent Interceptor with Automatic Renewal
 *
 * Automatically adds the current dynamic User-Agent to all HTTP requests
 * and renews it automatically when it expires to prevent 401 errors
 */
@Singleton
class DynamicUserAgentInterceptor @Inject constructor(
    private val userAgentManager: DynamicUserAgentManager,
    private val userAgentRenewalService: UserAgentRenewalService
) : Interceptor {

    companion object {
        private const val USER_AGENT_HEADER = "User-Agent"
        private const val DEFAULT_USER_AGENT = "IPTVApp/2.1.0 (Android 14; SM-G998B)"
        private const val TAG = "DynamicUserAgentInterceptor"
    }

    // Mutex to prevent concurrent renewals
    private val renewalMutex = Mutex()
    private val isRenewing = AtomicBoolean(false)
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // Get current dynamic User-Agent
        var dynamicUserAgent = userAgentManager.getCurrentUserAgent()
        var needsRenewal = userAgentManager.needsRenewal()

        // If User-Agent needs renewal, try to renew it automatically
        if (dynamicUserAgent != null && needsRenewal) {
            Timber.d("$TAG: 🔄 User-Agent needs renewal for ${originalRequest.url}")

            val renewedUserAgent = renewUserAgentIfNeeded()
            if (renewedUserAgent != null) {
                dynamicUserAgent = renewedUserAgent
                needsRenewal = false
                Timber.i("$TAG: ✅ User-Agent renewed successfully")
            } else {
                Timber.w("$TAG: ⚠️ Failed to renew User-Agent, using default")
            }
        }

        val requestBuilder = originalRequest.newBuilder()

        if (dynamicUserAgent != null && !needsRenewal) {
            // Use dynamic User-Agent if it's valid
            requestBuilder.header(USER_AGENT_HEADER, dynamicUserAgent)
            Timber.v("$TAG: 🔐 Using dynamic User-Agent for ${originalRequest.url}")
        } else {
            // Use default User-Agent (for login, public endpoints, or when renewal failed)
            requestBuilder.header(USER_AGENT_HEADER, DEFAULT_USER_AGENT)
            if (dynamicUserAgent != null && needsRenewal) {
                Timber.d("$TAG: 🔄 Using default User-Agent (renewal needed) for ${originalRequest.url}")
            } else {
                Timber.v("$TAG: 📱 Using default User-Agent for ${originalRequest.url}")
            }
        }

        val request = requestBuilder.build()
        val response = chain.proceed(request)

        // Handle 401 errors that might be due to expired User-Agent
        if (response.code == 401 && dynamicUserAgent != null) {
            Timber.w("$TAG: 🚨 Received 401 error, checking if User-Agent expired")

            // Check if this might be a User-Agent expiration issue
            if (isUserAgentRelatedError(response)) {
                Timber.w("$TAG: 🔄 Attempting User-Agent renewal due to 401 error")

                // Close the response before attempting renewal
                try {
                    response.close()
                } catch (e: Exception) {
                    Timber.w(e, "$TAG: Error closing response")
                }

                val renewedUserAgent = renewUserAgentIfNeeded()
                if (renewedUserAgent != null) {
                    Timber.i("$TAG: ✅ User-Agent renewed after 401, retrying request")

                    val retryRequest = originalRequest.newBuilder()
                        .header(USER_AGENT_HEADER, renewedUserAgent)
                        .build()

                    return chain.proceed(retryRequest)
                } else {
                    Timber.e("$TAG: ❌ Failed to renew User-Agent after 401 error")
                }
            }
        }

        return response
    }

    /**
     * Renew User-Agent if needed, with thread safety
     */
    private fun renewUserAgentIfNeeded(): String? {
        return runBlocking {
            renewalMutex.withLock {
                // Double-check if renewal is still needed (another thread might have renewed it)
                if (!userAgentManager.needsRenewal()) {
                    Timber.d("$TAG: 🔄 User-Agent was already renewed by another thread")
                    return@withLock userAgentManager.getCurrentUserAgent()
                }

                if (isRenewing.get()) {
                    Timber.d("$TAG: 🔄 User-Agent renewal already in progress")
                    return@withLock null
                }

                try {
                    isRenewing.set(true)
                    Timber.d("$TAG: 🔄 Starting User-Agent renewal...")

                    if (!userAgentRenewalService.canRenew()) {
                        Timber.w("$TAG: ⚠️ Cannot renew User-Agent - missing dependencies")
                        Timber.d("$TAG: ${userAgentRenewalService.getRenewalStatus()}")
                        return@withLock null
                    }

                    val newUserAgent = userAgentRenewalService.renewUserAgent()
                    if (newUserAgent != null) {
                        Timber.i("$TAG: ✅ User-Agent renewed successfully")
                        newUserAgent
                    } else {
                        Timber.e("$TAG: ❌ Failed to renew User-Agent")
                        null
                    }
                } catch (e: Exception) {
                    Timber.e(e, "$TAG: 🚨 Exception during User-Agent renewal")
                    null
                } finally {
                    isRenewing.set(false)
                }
            }
        }
    }

    /**
     * Check if a 401 error might be related to User-Agent expiration
     */
    private fun isUserAgentRelatedError(response: Response): Boolean {
        return try {
            // Check if the request URL suggests it's a protected endpoint
            val url = response.request.url.toString()
            val isProtectedEndpoint = url.contains("/movies") ||
                                    url.contains("/series") ||
                                    url.contains("/channels") ||
                                    url.contains("/anime") ||
                                    url.contains("/vod") ||
                                    url.contains("/adults") ||
                                    url.contains("/novelas") ||
                                    url.contains("/stream") ||
                                    url.contains("/content") ||
                                    url.contains("/tv/") ||
                                    url.contains("/delivery/")

            if (isProtectedEndpoint) {
                Timber.d("$TAG: 🔍 401 error on protected endpoint: $url")
                return true
            }

            // Check response body for User-Agent related errors (safely)
            try {
                val responseBody = response.peekBody(1024).string()
                val hasUserAgentError = responseBody.contains("user_agent", ignoreCase = true) ||
                                      responseBody.contains("invalid_user_agent", ignoreCase = true) ||
                                      responseBody.contains("expired", ignoreCase = true) ||
                                      responseBody.contains("Authentication failed", ignoreCase = true)

                if (hasUserAgentError) {
                    Timber.d("$TAG: 🔍 Response body suggests User-Agent issue: $responseBody")
                    return true
                }
            } catch (e: Exception) {
                Timber.w(e, "$TAG: Error reading response body for User-Agent check")
                // If we can't read the body, assume it might be User-Agent related for protected endpoints
                return isProtectedEndpoint
            }

            false
        } catch (e: Exception) {
            Timber.w(e, "$TAG: Error checking if 401 is User-Agent related")
            false
        }
    }
}
