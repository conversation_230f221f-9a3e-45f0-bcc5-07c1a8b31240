package com.iptv.android.core.security

import com.iptv.android.core.network.BackendHandler
import com.iptv.android.data.local.datastore.TokenManager
import com.iptv.android.data.remote.dto.RenewUserAgentRequestDto
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * User-Agent Renewal Service
 * 
 * Standalone service for renewing User-Agent without causing dependency cycles.
 * Uses a separate OkHttpClient to avoid circular dependencies with interceptors.
 */
@Singleton
class UserAgentRenewalService @Inject constructor(
    private val tokenManager: TokenManager,
    private val backendHandler: BackendHandler,
    private val dynamicUserAgentManager: DynamicUserAgentManager
) {
    
    companion object {
        private const val TAG = "UserAgentRenewalService"
    }
    
    // Simple OkHttpClient without interceptors to avoid cycles
    private val simpleHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    /**
     * Renew User-Agent using direct HTTP call
     */
    suspend fun renewUserAgent(): String? {
        return try {
            Timber.d("$TAG: 🔄 Attempting User-Agent renewal")
            
            // Get current access token
            val accessToken = tokenManager.getAccessToken().first()
            if (accessToken.isNullOrEmpty()) {
                Timber.w("$TAG: No access token available for User-Agent renewal")
                return null
            }
            
            // Get app secret
            val appSecret = backendHandler.getAppSecret()

            // Get base URL
            val baseUrl = backendHandler.getBaseUrl()
            if (baseUrl.isNullOrEmpty()) {
                Timber.w("$TAG: No base URL available for User-Agent renewal")
                return null
            }

            // Prepare request - baseUrl already includes /api/
            val url = "${baseUrl.trimEnd('/')}/auth/renew-user-agent"

            // Request body with app_secret for the with_secret endpoint
            val requestBody = JSONObject().apply {
                put("app_secret", appSecret)
            }
            
            val request = Request.Builder()
                .url(url)
                .post(requestBody.toString().toRequestBody("application/json".toMediaType()))
                .header("Authorization", "Bearer $accessToken")
                .header("Content-Type", "application/json")
                .header("User-Agent", "IPTVApp/2.1.0 (Android 14; SM-G998B)")
                .build()
            
            Timber.d("$TAG: Making renewal request to: $url")
            
            // Execute request
            val response = simpleHttpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val jsonResponse = JSONObject(responseBody)
                    val newUserAgent = jsonResponse.optString("user_agent")
                    
                    if (newUserAgent.isNotEmpty()) {
                        // Update the User-Agent manager
                        dynamicUserAgentManager.updateUserAgent(newUserAgent)
                        Timber.i("$TAG: ✅ User-Agent renewed successfully")
                        return newUserAgent
                    } else {
                        Timber.w("$TAG: ⚠️ Response missing user_agent field")
                    }
                } else {
                    Timber.w("$TAG: ⚠️ Empty response body")
                }
            } else {
                Timber.w("$TAG: ❌ Renewal failed with HTTP ${response.code}: ${response.message}")
                
                // Log response body for debugging
                val errorBody = response.body?.string()
                if (!errorBody.isNullOrEmpty()) {
                    Timber.w("$TAG: Error response: $errorBody")
                }
            }
            
            response.close()
            null
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 🚨 Exception during User-Agent renewal")
            null
        }
    }
    
    /**
     * Check if renewal is possible (has required dependencies)
     */
    fun canRenew(): Boolean {
        return try {
            val hasToken = runBlocking { 
                !tokenManager.getAccessToken().first().isNullOrEmpty() 
            }
            val hasBaseUrl = runBlocking { !backendHandler.getBaseUrl().isNullOrEmpty() }
            
            hasToken && hasBaseUrl
        } catch (e: Exception) {
            Timber.w(e, "$TAG: Error checking renewal capability")
            false
        }
    }
    
    /**
     * Get renewal status information for debugging
     */
    fun getRenewalStatus(): String {
        return try {
            val hasToken = runBlocking { 
                !tokenManager.getAccessToken().first().isNullOrEmpty() 
            }
            val hasBaseUrl = runBlocking { !backendHandler.getBaseUrl().isNullOrEmpty() }
            val needsRenewal = dynamicUserAgentManager.needsRenewal()
            val currentUserAgent = dynamicUserAgentManager.getCurrentUserAgent()
            
            """
            User-Agent Renewal Status:
            - Has Token: $hasToken
            - Has Base URL: $hasBaseUrl
            - Needs Renewal: $needsRenewal
            - Current User-Agent: ${if (currentUserAgent != null) "Present" else "None"}
            - Can Renew: ${canRenew()}
            """.trimIndent()
        } catch (e: Exception) {
            "Error getting renewal status: ${e.message}"
        }
    }
}
