)com.iptv.android.core.error.IPTVException#com.iptv.android.core.utils.UiStateokhttp3.Interceptor$com.iptv.android.domain.model.Result%com.iptv.android.domain.model.UiState8com.iptv.android.domain.usecase.device.DeviceLimitAction4com.iptv.android.presentation.screens.player.UiStateandroidx.lifecycle.ViewModelkotlin.Enumandroid.app.Applicationjava.lang.Exception=com.google.android.exoplayer2.upstream.HttpDataSource.Factory5com.google.android.exoplayer2.upstream.HttpDataSourceandroidx.room.RoomDatabase1com.iptv.android.domain.repository.AuthRepository5com.iptv.android.domain.repository.ChannelsRepository4com.iptv.android.domain.repository.ContentRepository1com.iptv.android.domain.repository.UserRepository#androidx.activity.ComponentActivity8com.iptv.android.presentation.theme.GoogleTVFocusManager1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              