5app/src/main/java/com/iptv/android/IPTVApplication.ktAapp/src/main/java/com/iptv/android/core/constants/ApiConstants.ktAapp/src/main/java/com/iptv/android/core/constants/AppConstants.ktIapp/src/main/java/com/iptv/android/core/device/DeviceIdentifierManager.kt=app/src/main/java/com/iptv/android/core/error/ErrorHandler.ktIapp/src/main/java/com/iptv/android/core/media/CodecCompatibilityHelper.ktAapp/src/main/java/com/iptv/android/core/network/BackendHandler.ktJapp/src/main/java/com/iptv/android/core/network/CleartextHttpDataSource.ktHapp/src/main/java/com/iptv/android/core/network/NetworkSecurityHelper.ktKapp/src/main/java/com/iptv/android/core/security/DynamicUserAgentManager.ktJapp/src/main/java/com/iptv/android/core/security/NetworkSecurityManager.ktKapp/src/main/java/com/iptv/android/core/security/UserAgentRenewalManager.kt=app/src/main/java/com/iptv/android/core/utils/ErrorHandler.kt;app/src/main/java/com/iptv/android/core/utils/Extensions.kt@app/src/main/java/com/iptv/android/core/utils/LanguageManager.kt8app/src/main/java/com/iptv/android/core/utils/UiState.kt?app/src/main/java/com/iptv/android/data/local/dao/ChannelDao.kt?app/src/main/java/com/iptv/android/data/local/dao/ContentDao.kt<app/src/main/java/com/iptv/android/data/local/dao/UserDao.ktFapp/src/main/java/com/iptv/android/data/local/database/IPTVDatabase.ktGapp/src/main/java/com/iptv/android/data/local/datastore/TokenManager.ktQapp/src/main/java/com/iptv/android/data/local/datastore/UserPreferencesManager.ktEapp/src/main/java/com/iptv/android/data/local/entity/ContentEntity.ktBapp/src/main/java/com/iptv/android/data/local/entity/UserEntity.kt?app/src/main/java/com/iptv/android/data/mapper/ContentMapper.kt<app/src/main/java/com/iptv/android/data/mapper/UserMapper.ktDapp/src/main/java/com/iptv/android/data/remote/api/IPTVApiService.ktIapp/src/main/java/com/iptv/android/data/remote/api/RefreshTokenService.kt>app/src/main/java/com/iptv/android/data/remote/dto/AuthDtos.ktIapp/src/main/java/com/iptv/android/data/remote/dto/ChannelsResponseDto.ktAapp/src/main/java/com/iptv/android/data/remote/dto/ContentDtos.ktDapp/src/main/java/com/iptv/android/data/remote/dto/EPGResponseDto.ktOapp/src/main/java/com/iptv/android/data/remote/dto/RenewUserAgentResponseDto.kt>app/src/main/java/com/iptv/android/data/remote/dto/UserDtos.ktMapp/src/main/java/com/iptv/android/data/remote/interceptor/AuthInterceptor.ktWapp/src/main/java/com/iptv/android/data/remote/interceptor/DynamicBaseUrlInterceptor.ktYapp/src/main/java/com/iptv/android/data/remote/interceptor/DynamicUserAgentInterceptor.ktNapp/src/main/java/com/iptv/android/data/remote/interceptor/ErrorInterceptor.ktHapp/src/main/java/com/iptv/android/data/repository/AuthRepositoryImpl.ktLapp/src/main/java/com/iptv/android/data/repository/ChannelsRepositoryImpl.ktKapp/src/main/java/com/iptv/android/data/repository/ContentRepositoryImpl.ktKapp/src/main/java/com/iptv/android/data/repository/UserProfileRepository.ktHapp/src/main/java/com/iptv/android/data/repository/UserRepositoryImpl.kt8app/src/main/java/com/iptv/android/di/DataStoreModule.kt7app/src/main/java/com/iptv/android/di/DatabaseModule.kt6app/src/main/java/com/iptv/android/di/NetworkModule.kt5app/src/main/java/com/iptv/android/di/PlayerModule.kt9app/src/main/java/com/iptv/android/di/RepositoryModule.kt7app/src/main/java/com/iptv/android/di/SecurityModule.kt6app/src/main/java/com/iptv/android/di/UseCaseModule.kt=app/src/main/java/com/iptv/android/domain/model/AudioTrack.kt@app/src/main/java/com/iptv/android/domain/model/ChannelModels.kt@app/src/main/java/com/iptv/android/domain/model/ContentModels.kt=app/src/main/java/com/iptv/android/domain/model/UserModels.ktFapp/src/main/java/com/iptv/android/domain/repository/AuthRepository.ktJapp/src/main/java/com/iptv/android/domain/repository/ChannelsRepository.ktIapp/src/main/java/com/iptv/android/domain/repository/ContentRepository.ktFapp/src/main/java/com/iptv/android/domain/repository/UserRepository.ktFapp/src/main/java/com/iptv/android/domain/usecase/auth/AuthUseCases.ktJapp/src/main/java/com/iptv/android/domain/usecase/auth/LoginUserUseCase.ktLapp/src/main/java/com/iptv/android/domain/usecase/content/ContentUseCases.ktTapp/src/main/java/com/iptv/android/domain/usecase/content/GetContentStreamUseCase.ktUapp/src/main/java/com/iptv/android/domain/usecase/content/GetLiveTVChannelsUseCase.ktKapp/src/main/java/com/iptv/android/domain/usecase/content/NovelaUseCases.ktNapp/src/main/java/com/iptv/android/domain/usecase/device/DeviceLimitHandler.ktSapp/src/main/java/com/iptv/android/domain/usecase/series/GetEpisodeStreamUseCase.ktTapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeasonEpisodesUseCase.ktSapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeriesDetailsUseCase.ktSapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeriesSeasonsUseCase.ktQapp/src/main/java/com/iptv/android/domain/usecase/user/ParentalControlUseCases.ktFapp/src/main/java/com/iptv/android/domain/usecase/user/UserUseCases.kt?app/src/main/java/com/iptv/android/presentation/MainActivity.kt@app/src/main/java/com/iptv/android/presentation/MainViewModel.ktQapp/src/main/java/com/iptv/android/presentation/components/ActivationCodeInput.ktPapp/src/main/java/com/iptv/android/presentation/components/CompactSearchField.ktOapp/src/main/java/com/iptv/android/presentation/components/DeviceLimitDialog.ktJapp/src/main/java/com/iptv/android/presentation/components/ErrorMessage.ktIapp/src/main/java/com/iptv/android/presentation/components/ErrorScreen.ktNapp/src/main/java/com/iptv/android/presentation/components/GoogleTVCarousel.ktPapp/src/main/java/com/iptv/android/presentation/components/GoogleTVNavigation.ktHapp/src/main/java/com/iptv/android/presentation/components/IPTVButton.ktKapp/src/main/java/com/iptv/android/presentation/components/IPTVTextField.ktLapp/src/main/java/com/iptv/android/presentation/components/LoadingOverlay.ktKapp/src/main/java/com/iptv/android/presentation/components/LoadingScreen.ktOapp/src/main/java/com/iptv/android/presentation/components/ModernContentCard.ktQapp/src/main/java/com/iptv/android/presentation/components/ModernNavigationBar.ktOapp/src/main/java/com/iptv/android/presentation/components/ParentalPinDialog.ktTapp/src/main/java/com/iptv/android/presentation/components/ParentalPinSetupDialog.ktOapp/src/main/java/com/iptv/android/presentation/components/StreamflixEffects.ktRapp/src/main/java/com/iptv/android/presentation/components/StreamflixTestScreen.kt[app/src/main/java/com/iptv/android/presentation/components/common/GoogleTVFocusIndicator.ktWapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelCategoryRow.ktSapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelEPGInfo.ktPapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelGrid.ktWapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelInfoOverlay.ktWapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelNumberInput.ktSapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelPreview.ktUapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelSearchBar.ktLapp/src/main/java/com/iptv/android/presentation/components/livetv/EPGGrid.ktUapp/src/main/java/com/iptv/android/presentation/components/livetv/FavoritesManager.ktXapp/src/main/java/com/iptv/android/presentation/components/livetv/GoogleTVChannelCard.ktZapp/src/main/java/com/iptv/android/presentation/components/livetv/ModernChannelSelector.ktaapp/src/main/java/com/iptv/android/presentation/components/livetv/ModernChannelSelectorPreview.ktTapp/src/main/java/com/iptv/android/presentation/components/livetv/ProgramInfoCard.ktUapp/src/main/java/com/iptv/android/presentation/components/livetv/QuickAccessPanel.ktWapp/src/main/java/com/iptv/android/presentation/components/player/AudioTrackSelector.ktXapp/src/main/java/com/iptv/android/presentation/components/transitions/FadeTransition.ktDapp/src/main/java/com/iptv/android/presentation/login/LoginScreen.ktRapp/src/main/java/com/iptv/android/presentation/navigation/GoogleTVFocusManager.ktLapp/src/main/java/com/iptv/android/presentation/navigation/IPTVNavigation.ktSapp/src/main/java/com/iptv/android/presentation/navigation/NavigationTransitions.ktQapp/src/main/java/com/iptv/android/presentation/navigation/NavigationViewModel.ktNapp/src/main/java/com/iptv/android/presentation/player/AdvancedPlayerScreen.ktQapp/src/main/java/com/iptv/android/presentation/player/AdvancedPlayerViewModel.ktCapp/src/main/java/com/iptv/android/presentation/player/DrmHelper.ktJapp/src/main/java/com/iptv/android/presentation/player/ExoPlayerManager.kt[app/src/main/java/com/iptv/android/presentation/player/components/AdvancedPlayerControls.ktPapp/src/main/java/com/iptv/android/presentation/screens/auth/ActivationScreen.ktSapp/src/main/java/com/iptv/android/presentation/screens/auth/ActivationViewModel.ktXapp/src/main/java/com/iptv/android/presentation/screens/auth/DeviceRegistrationRouter.ktUapp/src/main/java/com/iptv/android/presentation/screens/auth/DeviceSelectionScreen.ktKapp/src/main/java/com/iptv/android/presentation/screens/auth/LoginScreen.ktNapp/src/main/java/com/iptv/android/presentation/screens/auth/LoginViewModel.kt^app/src/main/java/com/iptv/android/presentation/screens/auth/ModernDeviceRegistrationScreen.ktOapp/src/main/java/com/iptv/android/presentation/screens/auth/UserLoginScreen.ktRapp/src/main/java/com/iptv/android/presentation/screens/auth/UserLoginViewModel.kt\app/src/main/java/com/iptv/android/presentation/screens/auth/components/DeviceLimitScreen.ktRapp/src/main/java/com/iptv/android/presentation/screens/category/CategoryScreen.ktUapp/src/main/java/com/iptv/android/presentation/screens/category/CategoryViewModel.ktVapp/src/main/java/com/iptv/android/presentation/screens/category/ModernCategoryDemo.ktaapp/src/main/java/com/iptv/android/presentation/screens/category/components/CategoryComponents.ktPapp/src/main/java/com/iptv/android/presentation/screens/demo/ModernUIShowcase.ktUapp/src/main/java/com/iptv/android/presentation/screens/detail/ContentDetailScreen.ktXapp/src/main/java/com/iptv/android/presentation/screens/detail/ContentDetailViewModel.ktNapp/src/main/java/com/iptv/android/presentation/screens/detail/DetailScreen.ktQapp/src/main/java/com/iptv/android/presentation/screens/detail/DetailViewModel.ktdapp/src/main/java/com/iptv/android/presentation/screens/detail/components/ContentDetailComponents.ktJapp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/src/main/java/com/iptv/android/presentation/screens/home/<USER>/src/main/java/com/iptv/android/presentation/screens/home/<USER>/HeroBanner.ktdapp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/HorizontalContentCarousel.kt\app/src/main/java/com/iptv/android/presentation/screens/home/<USER>/NavigationSidebar.ktKapp/src/main/java/com/iptv/android/presentation/screens/livetv/EPGScreen.ktTapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVDirectScreen.ktNapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVScreen.ktQapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVViewModel.ktNapp/src/main/java/com/iptv/android/presentation/screens/movies/MoviesScreen.ktVapp/src/main/java/com/iptv/android/presentation/screens/player/EpisodicPlayerScreen.ktYapp/src/main/java/com/iptv/android/presentation/screens/player/EpisodicPlayerViewModel.ktNapp/src/main/java/com/iptv/android/presentation/screens/player/PlayerScreen.ktQapp/src/main/java/com/iptv/android/presentation/screens/player/PlayerViewModel.kt[app/src/main/java/com/iptv/android/presentation/screens/player/components/ChannelSidebar.ktaapp/src/main/java/com/iptv/android/presentation/screens/player/components/ChannelSidebarNative.ktfapp/src/main/java/com/iptv/android/presentation/screens/player/components/EpisodeNavigationControls.ktcapp/src/main/java/com/iptv/android/presentation/screens/player/components/EpisodicPlayerControls.ktaapp/src/main/java/com/iptv/android/presentation/screens/player/components/LiveTVPlayerControls.kt[app/src/main/java/com/iptv/android/presentation/screens/player/components/PlayerControls.kt^app/src/main/java/com/iptv/android/presentation/screens/player/components/VODPlayerControls.ktPapp/src/main/java/com/iptv/android/presentation/screens/profile/ProfileScreen.ktSapp/src/main/java/com/iptv/android/presentation/screens/profile/ProfileViewModel.kt_app/src/main/java/com/iptv/android/presentation/screens/profile/components/ProfileComponents.ktNapp/src/main/java/com/iptv/android/presentation/screens/search/SearchScreen.ktQapp/src/main/java/com/iptv/android/presentation/screens/search/SearchViewModel.ktZapp/src/main/java/com/iptv/android/presentation/screens/search/components/SearchFilters.kt]app/src/main/java/com/iptv/android/presentation/screens/search/components/SearchResultCard.kt^app/src/main/java/com/iptv/android/presentation/screens/search/components/SearchSuggestions.kt^app/src/main/java/com/iptv/android/presentation/screens/search/components/VoiceSearchButton.ktTapp/src/main/java/com/iptv/android/presentation/screens/season/SeasonDetailScreen.ktWapp/src/main/java/com/iptv/android/presentation/screens/season/SeasonDetailViewModel.ktYapp/src/main/java/com/iptv/android/presentation/screens/security/SecurityWarningScreen.ktTapp/src/main/java/com/iptv/android/presentation/screens/series/SeriesDetailScreen.ktWapp/src/main/java/com/iptv/android/presentation/screens/series/SeriesDetailViewModel.ktRapp/src/main/java/com/iptv/android/presentation/screens/settings/SettingsScreen.ktUapp/src/main/java/com/iptv/android/presentation/screens/settings/SettingsViewModel.kt_app/src/main/java/com/iptv/android/presentation/screens/settings/components/LanguageSelector.ktaapp/src/main/java/com/iptv/android/presentation/screens/settings/components/SettingsComponents.ktNapp/src/main/java/com/iptv/android/presentation/screens/splash/SplashScreen.ktQapp/src/main/java/com/iptv/android/presentation/screens/splash/SplashViewModel.kt>app/src/main/java/com/iptv/android/presentation/theme/Color.ktFapp/src/main/java/com/iptv/android/presentation/theme/GoogleTVFocus.kt>app/src/main/java/com/iptv/android/presentation/theme/Theme.kt=app/src/main/java/com/iptv/android/presentation/theme/Type.ktRapp/build/generated/source/buildConfig/dev/debug/com/iptv/android/BuildConfig.java                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     