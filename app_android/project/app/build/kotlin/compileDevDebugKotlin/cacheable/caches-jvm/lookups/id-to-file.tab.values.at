/ Header Record For PersistentHashMapValueStorage6 5app/src/main/java/com/iptv/android/IPTVApplication.ktB Aapp/src/main/java/com/iptv/android/core/constants/ApiConstants.ktB Aapp/src/main/java/com/iptv/android/core/constants/AppConstants.ktJ Iapp/src/main/java/com/iptv/android/core/device/DeviceIdentifierManager.kt> =app/src/main/java/com/iptv/android/core/error/ErrorHandler.ktJ Iapp/src/main/java/com/iptv/android/core/media/CodecCompatibilityHelper.ktB Aapp/src/main/java/com/iptv/android/core/network/BackendHandler.ktK Japp/src/main/java/com/iptv/android/core/network/CleartextHttpDataSource.ktI Happ/src/main/java/com/iptv/android/core/network/NetworkSecurityHelper.ktL Kapp/src/main/java/com/iptv/android/core/security/DynamicUserAgentManager.ktK Japp/src/main/java/com/iptv/android/core/security/NetworkSecurityManager.ktL Kapp/src/main/java/com/iptv/android/core/security/UserAgentRenewalManager.kt> =app/src/main/java/com/iptv/android/core/utils/ErrorHandler.kt< ;app/src/main/java/com/iptv/android/core/utils/Extensions.ktA @app/src/main/java/com/iptv/android/core/utils/LanguageManager.kt9 8app/src/main/java/com/iptv/android/core/utils/UiState.kt@ ?app/src/main/java/com/iptv/android/data/local/dao/ChannelDao.kt@ ?app/src/main/java/com/iptv/android/data/local/dao/ContentDao.kt= <app/src/main/java/com/iptv/android/data/local/dao/UserDao.ktG Fapp/src/main/java/com/iptv/android/data/local/database/IPTVDatabase.ktH Gapp/src/main/java/com/iptv/android/data/local/datastore/TokenManager.ktR Qapp/src/main/java/com/iptv/android/data/local/datastore/UserPreferencesManager.ktF Eapp/src/main/java/com/iptv/android/data/local/entity/ContentEntity.ktC Bapp/src/main/java/com/iptv/android/data/local/entity/UserEntity.kt@ ?app/src/main/java/com/iptv/android/data/mapper/ContentMapper.kt= <app/src/main/java/com/iptv/android/data/mapper/UserMapper.ktE Dapp/src/main/java/com/iptv/android/data/remote/api/IPTVApiService.ktJ Iapp/src/main/java/com/iptv/android/data/remote/api/RefreshTokenService.kt? >app/src/main/java/com/iptv/android/data/remote/dto/AuthDtos.ktJ Iapp/src/main/java/com/iptv/android/data/remote/dto/ChannelsResponseDto.ktB Aapp/src/main/java/com/iptv/android/data/remote/dto/ContentDtos.ktE Dapp/src/main/java/com/iptv/android/data/remote/dto/EPGResponseDto.ktP Oapp/src/main/java/com/iptv/android/data/remote/dto/RenewUserAgentResponseDto.kt? >app/src/main/java/com/iptv/android/data/remote/dto/UserDtos.ktN Mapp/src/main/java/com/iptv/android/data/remote/interceptor/AuthInterceptor.ktX Wapp/src/main/java/com/iptv/android/data/remote/interceptor/DynamicBaseUrlInterceptor.ktZ Yapp/src/main/java/com/iptv/android/data/remote/interceptor/DynamicUserAgentInterceptor.ktO Napp/src/main/java/com/iptv/android/data/remote/interceptor/ErrorInterceptor.ktI Happ/src/main/java/com/iptv/android/data/repository/AuthRepositoryImpl.ktM Lapp/src/main/java/com/iptv/android/data/repository/ChannelsRepositoryImpl.ktL Kapp/src/main/java/com/iptv/android/data/repository/ContentRepositoryImpl.ktL Kapp/src/main/java/com/iptv/android/data/repository/UserProfileRepository.ktI Happ/src/main/java/com/iptv/android/data/repository/UserRepositoryImpl.kt9 8app/src/main/java/com/iptv/android/di/DataStoreModule.kt8 7app/src/main/java/com/iptv/android/di/DatabaseModule.kt7 6app/src/main/java/com/iptv/android/di/NetworkModule.kt6 5app/src/main/java/com/iptv/android/di/PlayerModule.kt: 9app/src/main/java/com/iptv/android/di/RepositoryModule.kt8 7app/src/main/java/com/iptv/android/di/SecurityModule.kt7 6app/src/main/java/com/iptv/android/di/UseCaseModule.kt> =app/src/main/java/com/iptv/android/domain/model/AudioTrack.ktA @app/src/main/java/com/iptv/android/domain/model/ChannelModels.ktA @app/src/main/java/com/iptv/android/domain/model/ContentModels.kt> =app/src/main/java/com/iptv/android/domain/model/UserModels.ktG Fapp/src/main/java/com/iptv/android/domain/repository/AuthRepository.ktK Japp/src/main/java/com/iptv/android/domain/repository/ChannelsRepository.ktJ Iapp/src/main/java/com/iptv/android/domain/repository/ContentRepository.ktG Fapp/src/main/java/com/iptv/android/domain/repository/UserRepository.ktG Fapp/src/main/java/com/iptv/android/domain/usecase/auth/AuthUseCases.ktK Japp/src/main/java/com/iptv/android/domain/usecase/auth/LoginUserUseCase.ktM Lapp/src/main/java/com/iptv/android/domain/usecase/content/ContentUseCases.ktU Tapp/src/main/java/com/iptv/android/domain/usecase/content/GetContentStreamUseCase.ktV Uapp/src/main/java/com/iptv/android/domain/usecase/content/GetLiveTVChannelsUseCase.ktL Kapp/src/main/java/com/iptv/android/domain/usecase/content/NovelaUseCases.ktO Napp/src/main/java/com/iptv/android/domain/usecase/device/DeviceLimitHandler.ktT Sapp/src/main/java/com/iptv/android/domain/usecase/series/GetEpisodeStreamUseCase.ktU Tapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeasonEpisodesUseCase.ktT Sapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeriesDetailsUseCase.ktT Sapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeriesSeasonsUseCase.ktR Qapp/src/main/java/com/iptv/android/domain/usecase/user/ParentalControlUseCases.ktG Fapp/src/main/java/com/iptv/android/domain/usecase/user/UserUseCases.kt@ ?app/src/main/java/com/iptv/android/presentation/MainActivity.ktA @app/src/main/java/com/iptv/android/presentation/MainViewModel.ktR Qapp/src/main/java/com/iptv/android/presentation/components/ActivationCodeInput.ktQ Papp/src/main/java/com/iptv/android/presentation/components/CompactSearchField.ktP Oapp/src/main/java/com/iptv/android/presentation/components/DeviceLimitDialog.ktK Japp/src/main/java/com/iptv/android/presentation/components/ErrorMessage.ktJ Iapp/src/main/java/com/iptv/android/presentation/components/ErrorScreen.ktO Napp/src/main/java/com/iptv/android/presentation/components/GoogleTVCarousel.ktQ Papp/src/main/java/com/iptv/android/presentation/components/GoogleTVNavigation.ktI Happ/src/main/java/com/iptv/android/presentation/components/IPTVButton.ktL Kapp/src/main/java/com/iptv/android/presentation/components/IPTVTextField.ktM Lapp/src/main/java/com/iptv/android/presentation/components/LoadingOverlay.ktL Kapp/src/main/java/com/iptv/android/presentation/components/LoadingScreen.ktP Oapp/src/main/java/com/iptv/android/presentation/components/ModernContentCard.ktR Qapp/src/main/java/com/iptv/android/presentation/components/ModernNavigationBar.ktP Oapp/src/main/java/com/iptv/android/presentation/components/ParentalPinDialog.ktU Tapp/src/main/java/com/iptv/android/presentation/components/ParentalPinSetupDialog.ktP Oapp/src/main/java/com/iptv/android/presentation/components/StreamflixEffects.ktS Rapp/src/main/java/com/iptv/android/presentation/components/StreamflixTestScreen.kt\ [app/src/main/java/com/iptv/android/presentation/components/common/GoogleTVFocusIndicator.ktX Wapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelCategoryRow.ktT Sapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelEPGInfo.ktQ Papp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelGrid.ktX Wapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelInfoOverlay.ktX Wapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelNumberInput.ktT Sapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelPreview.ktV Uapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelSearchBar.ktM Lapp/src/main/java/com/iptv/android/presentation/components/livetv/EPGGrid.ktV Uapp/src/main/java/com/iptv/android/presentation/components/livetv/FavoritesManager.ktY Xapp/src/main/java/com/iptv/android/presentation/components/livetv/GoogleTVChannelCard.kt[ Zapp/src/main/java/com/iptv/android/presentation/components/livetv/ModernChannelSelector.ktb aapp/src/main/java/com/iptv/android/presentation/components/livetv/ModernChannelSelectorPreview.ktU Tapp/src/main/java/com/iptv/android/presentation/components/livetv/ProgramInfoCard.ktV Uapp/src/main/java/com/iptv/android/presentation/components/livetv/QuickAccessPanel.ktX Wapp/src/main/java/com/iptv/android/presentation/components/player/AudioTrackSelector.ktY Xapp/src/main/java/com/iptv/android/presentation/components/transitions/FadeTransition.ktE Dapp/src/main/java/com/iptv/android/presentation/login/LoginScreen.ktS Rapp/src/main/java/com/iptv/android/presentation/navigation/GoogleTVFocusManager.ktM Lapp/src/main/java/com/iptv/android/presentation/navigation/IPTVNavigation.ktT Sapp/src/main/java/com/iptv/android/presentation/navigation/NavigationTransitions.ktR Qapp/src/main/java/com/iptv/android/presentation/navigation/NavigationViewModel.ktO Napp/src/main/java/com/iptv/android/presentation/player/AdvancedPlayerScreen.ktR Qapp/src/main/java/com/iptv/android/presentation/player/AdvancedPlayerViewModel.ktD Capp/src/main/java/com/iptv/android/presentation/player/DrmHelper.ktK Japp/src/main/java/com/iptv/android/presentation/player/ExoPlayerManager.kt\ [app/src/main/java/com/iptv/android/presentation/player/components/AdvancedPlayerControls.ktQ Papp/src/main/java/com/iptv/android/presentation/screens/auth/ActivationScreen.ktT Sapp/src/main/java/com/iptv/android/presentation/screens/auth/ActivationViewModel.ktY Xapp/src/main/java/com/iptv/android/presentation/screens/auth/DeviceRegistrationRouter.ktV Uapp/src/main/java/com/iptv/android/presentation/screens/auth/DeviceSelectionScreen.ktL Kapp/src/main/java/com/iptv/android/presentation/screens/auth/LoginScreen.ktO Napp/src/main/java/com/iptv/android/presentation/screens/auth/LoginViewModel.kt_ ^app/src/main/java/com/iptv/android/presentation/screens/auth/ModernDeviceRegistrationScreen.ktP Oapp/src/main/java/com/iptv/android/presentation/screens/auth/UserLoginScreen.ktS Rapp/src/main/java/com/iptv/android/presentation/screens/auth/UserLoginViewModel.kt] \app/src/main/java/com/iptv/android/presentation/screens/auth/components/DeviceLimitScreen.ktS Rapp/src/main/java/com/iptv/android/presentation/screens/category/CategoryScreen.ktV Uapp/src/main/java/com/iptv/android/presentation/screens/category/CategoryViewModel.ktW Vapp/src/main/java/com/iptv/android/presentation/screens/category/ModernCategoryDemo.ktb aapp/src/main/java/com/iptv/android/presentation/screens/category/components/CategoryComponents.ktQ Papp/src/main/java/com/iptv/android/presentation/screens/demo/ModernUIShowcase.ktV Uapp/src/main/java/com/iptv/android/presentation/screens/detail/ContentDetailScreen.ktY Xapp/src/main/java/com/iptv/android/presentation/screens/detail/ContentDetailViewModel.ktO Napp/src/main/java/com/iptv/android/presentation/screens/detail/DetailScreen.ktR Qapp/src/main/java/com/iptv/android/presentation/screens/detail/DetailViewModel.kte dapp/src/main/java/com/iptv/android/presentation/screens/detail/components/ContentDetailComponents.ktK Japp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/src/main/java/com/iptv/android/presentation/screens/home/<USER>/src/main/java/com/iptv/android/presentation/screens/home/<USER>/HeroBanner.kte dapp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/HorizontalContentCarousel.kt] \app/src/main/java/com/iptv/android/presentation/screens/home/<USER>/NavigationSidebar.ktL Kapp/src/main/java/com/iptv/android/presentation/screens/livetv/EPGScreen.ktU Tapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVDirectScreen.ktO Napp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVScreen.ktR Qapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVViewModel.ktO Napp/src/main/java/com/iptv/android/presentation/screens/movies/MoviesScreen.ktW Vapp/src/main/java/com/iptv/android/presentation/screens/player/EpisodicPlayerScreen.ktZ Yapp/src/main/java/com/iptv/android/presentation/screens/player/EpisodicPlayerViewModel.ktO Napp/src/main/java/com/iptv/android/presentation/screens/player/PlayerScreen.ktR Qapp/src/main/java/com/iptv/android/presentation/screens/player/PlayerViewModel.kt\ [app/src/main/java/com/iptv/android/presentation/screens/player/components/ChannelSidebar.ktb aapp/src/main/java/com/iptv/android/presentation/screens/player/components/ChannelSidebarNative.ktg fapp/src/main/java/com/iptv/android/presentation/screens/player/components/EpisodeNavigationControls.ktd capp/src/main/java/com/iptv/android/presentation/screens/player/components/EpisodicPlayerControls.ktb aapp/src/main/java/com/iptv/android/presentation/screens/player/components/LiveTVPlayerControls.kt\ [app/src/main/java/com/iptv/android/presentation/screens/player/components/PlayerControls.kt_ ^app/src/main/java/com/iptv/android/presentation/screens/player/components/VODPlayerControls.ktQ Papp/src/main/java/com/iptv/android/presentation/screens/profile/ProfileScreen.ktT Sapp/src/main/java/com/iptv/android/presentation/screens/profile/ProfileViewModel.kt` _app/src/main/java/com/iptv/android/presentation/screens/profile/components/ProfileComponents.ktO Napp/src/main/java/com/iptv/android/presentation/screens/search/SearchScreen.ktR Qapp/src/main/java/com/iptv/android/presentation/screens/search/SearchViewModel.kt[ Zapp/src/main/java/com/iptv/android/presentation/screens/search/components/SearchFilters.kt^ ]app/src/main/java/com/iptv/android/presentation/screens/search/components/SearchResultCard.kt_ ^app/src/main/java/com/iptv/android/presentation/screens/search/components/SearchSuggestions.kt_ ^app/src/main/java/com/iptv/android/presentation/screens/search/components/VoiceSearchButton.ktU Tapp/src/main/java/com/iptv/android/presentation/screens/season/SeasonDetailScreen.ktX Wapp/src/main/java/com/iptv/android/presentation/screens/season/SeasonDetailViewModel.ktZ Yapp/src/main/java/com/iptv/android/presentation/screens/security/SecurityWarningScreen.ktU Tapp/src/main/java/com/iptv/android/presentation/screens/series/SeriesDetailScreen.ktX Wapp/src/main/java/com/iptv/android/presentation/screens/series/SeriesDetailViewModel.ktS Rapp/src/main/java/com/iptv/android/presentation/screens/settings/SettingsScreen.ktV Uapp/src/main/java/com/iptv/android/presentation/screens/settings/SettingsViewModel.kt` _app/src/main/java/com/iptv/android/presentation/screens/settings/components/LanguageSelector.ktb aapp/src/main/java/com/iptv/android/presentation/screens/settings/components/SettingsComponents.ktO Napp/src/main/java/com/iptv/android/presentation/screens/splash/SplashScreen.ktR Qapp/src/main/java/com/iptv/android/presentation/screens/splash/SplashViewModel.kt? >app/src/main/java/com/iptv/android/presentation/theme/Color.ktG Fapp/src/main/java/com/iptv/android/presentation/theme/GoogleTVFocus.kt? >app/src/main/java/com/iptv/android/presentation/theme/Theme.kt> =app/src/main/java/com/iptv/android/presentation/theme/Type.kt