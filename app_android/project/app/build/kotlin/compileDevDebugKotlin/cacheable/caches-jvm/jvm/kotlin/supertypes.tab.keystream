 com.iptv.android.IPTVApplication)com.iptv.android.core.error.IPTVException,com.iptv.android.core.error.NetworkException)com.iptv.android.core.error.AuthException,com.iptv.android.core.error.ContentException)com.iptv.android.core.error.UserException,com.iptv.android.core.error.UnknownException/com.iptv.android.core.error.ValidationException*com.iptv.android.core.error.CacheException+com.iptv.android.core.error.PlayerExceptionGcom.iptv.android.core.media.CodecCompatibilityHelper.HevcRecommendation=com.iptv.android.core.network.CleartextHttpDataSource.FactoryLcom.iptv.android.core.network.CleartextHttpDataSource.EnhancedHttpDataSource4com.iptv.android.core.utils.LanguageManager.Language+com.iptv.android.core.utils.UiState.Loading+com.iptv.android.core.utils.UiState.Success)com.iptv.android.core.utils.UiState.Error1com.iptv.android.data.local.database.IPTVDatabase8com.iptv.android.data.remote.interceptor.AuthInterceptorBcom.iptv.android.data.remote.interceptor.DynamicBaseUrlInterceptorDcom.iptv.android.data.remote.interceptor.DynamicUserAgentInterceptor9com.iptv.android.data.remote.interceptor.ErrorInterceptorBcom.iptv.android.data.remote.interceptor.UserAgentErrorInterceptor3com.iptv.android.data.repository.AuthRepositoryImpl7com.iptv.android.data.repository.ChannelsRepositoryImpl6com.iptv.android.data.repository.ContentRepositoryImpl3com.iptv.android.data.repository.UserRepositoryImpl*com.iptv.android.domain.model.StreamFormat)com.iptv.android.domain.model.ContentType*com.iptv.android.domain.model.VideoQuality&com.iptv.android.domain.model.UserRole,com.iptv.android.domain.model.Result.Success*com.iptv.android.domain.model.Result.Error,com.iptv.android.domain.model.Result.Loading-com.iptv.android.domain.model.UiState.Loading-com.iptv.android.domain.model.UiState.Success+com.iptv.android.domain.model.UiState.ErrorHcom.iptv.android.domain.usecase.device.DeviceLimitAction.RegisterCurrentJcom.iptv.android.domain.usecase.device.DeviceLimitAction.AlreadyRegisteredLcom.iptv.android.domain.usecase.device.DeviceLimitAction.ShowDeviceSelection>com.iptv.android.domain.usecase.device.DeviceLimitAction.Error*com.iptv.android.presentation.MainActivity+com.iptv.android.presentation.MainViewModel6com.iptv.android.presentation.components.ButtonVariant3com.iptv.android.presentation.components.ButtonSize4com.iptv.android.presentation.components.LoadingSize7com.iptv.android.presentation.components.NavigationItem8com.iptv.android.presentation.components.livetv.GridTypeCcom.iptv.android.presentation.components.transitions.SlideDirectionAcom.iptv.android.presentation.navigation.GoogleTVFocusManagerImpl<com.iptv.android.presentation.navigation.NavigationViewModel<com.iptv.android.presentation.player.AdvancedPlayerViewModel>com.iptv.android.presentation.screens.auth.ActivationViewModel9com.iptv.android.presentation.screens.auth.LoginViewModel=<EMAIL><com.iptv.android.presentation.screens.detail.DetailViewModel8com.iptv.android.presentation.screens.home.HomeViewModel<com.iptv.android.presentation.screens.livetv.LiveTVViewModel<com.iptv.android.presentation.screens.movies.MoviesViewModel<com.iptv.android.presentation.screens.player.UiState.Loading<com.iptv.android.presentation.screens.player.UiState.Success:com.iptv.android.presentation.screens.player.UiState.ErrorDcom.iptv.android.presentation.screens.player.EpisodicPlayerViewModel<com.iptv.android.presentation.screens.player.PlayerViewModelFcom.iptv.android.presentation.screens.player.components.ChannelAdapterXcom.iptv.android.presentation.screens.player.components.ChannelAdapter.ChannelViewHolder>com.iptv.android.presentation.screens.profile.ProfileViewModel<<EMAIL><com.iptv.android.presentation.screens.splash.SplashViewModel<<EMAIL>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  