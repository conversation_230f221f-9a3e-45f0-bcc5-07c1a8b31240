Papp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelGrid.ktRapp/src/main/java/com/iptv/android/presentation/screens/category/CategoryScreen.ktTapp/src/main/java/com/iptv/android/presentation/screens/series/SeriesDetailScreen.ktFapp/src/main/java/com/iptv/android/presentation/theme/GoogleTVFocus.ktCapp/src/main/java/com/iptv/android/presentation/player/DrmHelper.ktAapp/src/main/java/com/iptv/android/core/network/BackendHandler.ktQapp/src/main/java/com/iptv/android/presentation/screens/player/PlayerViewModel.ktRapp/src/main/java/com/iptv/android/presentation/screens/settings/SettingsScreen.kt>app/src/main/java/com/iptv/android/data/remote/dto/AuthDtos.ktUapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelSearchBar.ktIapp/src/main/java/com/iptv/android/presentation/components/ErrorScreen.kt?app/src/main/java/com/iptv/android/presentation/MainActivity.ktJapp/src/main/java/com/iptv/android/presentation/components/ErrorMessage.ktTapp/src/main/java/com/iptv/android/presentation/components/livetv/ProgramInfoCard.ktSapp/src/main/java/com/iptv/android/presentation/screens/auth/ActivationViewModel.ktMapp/src/main/java/com/iptv/android/data/remote/interceptor/AuthInterceptor.ktDapp/src/main/java/com/iptv/android/data/remote/api/IPTVApiService.kt9app/src/main/java/com/iptv/android/di/RepositoryModule.ktKapp/src/main/java/com/iptv/android/data/repository/ContentRepositoryImpl.ktQapp/src/main/java/com/iptv/android/presentation/screens/splash/SplashViewModel.kt[app/src/main/java/com/iptv/android/presentation/screens/player/components/ChannelSidebar.ktSapp/src/main/java/com/iptv/android/domain/usecase/series/GetEpisodeStreamUseCase.ktFapp/src/main/java/com/iptv/android/domain/repository/AuthRepository.ktLapp/src/main/java/com/iptv/android/presentation/components/livetv/EPGGrid.ktOapp/src/main/java/com/iptv/android/presentation/components/StreamflixEffects.ktTapp/src/main/java/com/iptv/android/domain/usecase/content/GetContentStreamUseCase.kt8app/src/main/java/com/iptv/android/di/DataStoreModule.ktNapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVScreen.ktAapp/src/main/java/com/iptv/android/core/constants/AppConstants.ktKapp/src/main/java/com/iptv/android/presentation/screens/livetv/EPGScreen.ktfapp/src/main/java/com/iptv/android/presentation/screens/player/components/EpisodeNavigationControls.ktPapp/src/main/java/com/iptv/android/presentation/components/GoogleTVNavigation.ktFapp/src/main/java/com/iptv/android/domain/usecase/user/UserUseCases.kt6app/src/main/java/com/iptv/android/di/UseCaseModule.ktPapp/src/main/java/com/iptv/android/presentation/screens/profile/ProfileScreen.ktDapp/src/main/java/com/iptv/android/data/remote/dto/EPGResponseDto.ktXapp/src/main/java/com/iptv/android/presentation/components/transitions/FadeTransition.ktNapp/src/main/java/com/iptv/android/domain/usecase/device/DeviceLimitHandler.ktIapp/src/main/java/com/iptv/android/domain/repository/ContentRepository.kt8app/src/main/java/com/iptv/android/core/utils/UiState.kt@app/src/main/java/com/iptv/android/presentation/MainViewModel.ktNapp/src/main/java/com/iptv/android/presentation/screens/detail/DetailScreen.ktJapp/src/main/java/com/iptv/android/core/security/NetworkSecurityManager.ktBapp/src/main/java/com/iptv/android/data/local/entity/UserEntity.kt_app/src/main/java/com/iptv/android/presentation/screens/settings/components/LanguageSelector.ktcapp/src/main/java/com/iptv/android/presentation/screens/player/components/EpisodicPlayerControls.kt>app/src/main/java/com/iptv/android/presentation/theme/Theme.ktTapp/src/main/java/com/iptv/android/presentation/screens/season/SeasonDetailScreen.kt]app/src/main/java/com/iptv/android/presentation/screens/search/components/SearchResultCard.ktQapp/src/main/java/com/iptv/android/presentation/player/AdvancedPlayerViewModel.ktQapp/src/main/java/com/iptv/android/presentation/components/ActivationCodeInput.kt5app/src/main/java/com/iptv/android/di/PlayerModule.kt>app/src/main/java/com/iptv/android/presentation/theme/Color.ktUapp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/HeroBanner.ktKapp/src/main/java/com/iptv/android/core/security/UserAgentRenewalManager.ktSapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeriesDetailsUseCase.ktKapp/src/main/java/com/iptv/android/presentation/components/LoadingScreen.ktUapp/src/main/java/com/iptv/android/presentation/screens/category/CategoryViewModel.ktSapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeriesSeasonsUseCase.kt@app/src/main/java/com/iptv/android/core/utils/LanguageManager.kt5app/src/main/java/com/iptv/android/IPTVApplication.ktPapp/src/main/java/com/iptv/android/presentation/screens/auth/ActivationScreen.ktQapp/src/main/java/com/iptv/android/data/local/datastore/UserPreferencesManager.ktNapp/src/main/java/com/iptv/android/presentation/screens/splash/SplashScreen.ktdapp/src/main/java/com/iptv/android/presentation/screens/detail/components/ContentDetailComponents.ktNapp/src/main/java/com/iptv/android/data/remote/interceptor/ErrorInterceptor.ktVapp/src/main/java/com/iptv/android/presentation/screens/player/EpisodicPlayerScreen.ktNapp/src/main/java/com/iptv/android/presentation/screens/search/SearchScreen.ktaapp/src/main/java/com/iptv/android/presentation/screens/settings/components/SettingsComponents.ktWapp/src/main/java/com/iptv/android/presentation/screens/season/SeasonDetailViewModel.ktWapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelNumberInput.kt?app/src/main/java/com/iptv/android/data/local/dao/ContentDao.ktJapp/src/main/java/com/iptv/android/core/network/CleartextHttpDataSource.ktPapp/src/main/java/com/iptv/android/presentation/screens/demo/ModernUIShowcase.kt;app/src/main/java/com/iptv/android/core/utils/Extensions.ktYapp/src/main/java/com/iptv/android/presentation/screens/player/EpisodicPlayerViewModel.kt=app/src/main/java/com/iptv/android/domain/model/UserModels.ktaapp/src/main/java/com/iptv/android/presentation/screens/player/components/ChannelSidebarNative.ktEapp/src/main/java/com/iptv/android/data/local/entity/ContentEntity.ktUapp/src/main/java/com/iptv/android/presentation/screens/settings/SettingsViewModel.ktLapp/src/main/java/com/iptv/android/domain/usecase/content/ContentUseCases.kt<app/src/main/java/com/iptv/android/data/mapper/UserMapper.ktRapp/src/main/java/com/iptv/android/presentation/components/StreamflixTestScreen.ktXapp/src/main/java/com/iptv/android/presentation/screens/auth/DeviceRegistrationRouter.kt^app/src/main/java/com/iptv/android/presentation/screens/search/components/SearchSuggestions.ktUapp/src/main/java/com/iptv/android/presentation/components/livetv/QuickAccessPanel.ktKapp/src/main/java/com/iptv/android/data/repository/UserProfileRepository.ktLapp/src/main/java/com/iptv/android/data/repository/ChannelsRepositoryImpl.ktWapp/src/main/java/com/iptv/android/presentation/components/player/AudioTrackSelector.ktAapp/src/main/java/com/iptv/android/core/constants/ApiConstants.ktKapp/src/main/java/com/iptv/android/domain/usecase/content/NovelaUseCases.ktJapp/src/main/java/com/iptv/android/domain/repository/ChannelsRepository.ktQapp/src/main/java/com/iptv/android/presentation/screens/detail/DetailViewModel.ktOapp/src/main/java/com/iptv/android/presentation/components/DeviceLimitDialog.ktWapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelCategoryRow.ktQapp/src/main/java/com/iptv/android/presentation/screens/search/SearchViewModel.kt_app/src/main/java/com/iptv/android/presentation/screens/profile/components/ProfileComponents.ktNapp/src/main/java/com/iptv/android/presentation/screens/movies/MoviesScreen.ktUapp/src/main/java/com/iptv/android/domain/usecase/content/GetLiveTVChannelsUseCase.kt?app/src/main/java/com/iptv/android/data/local/dao/ChannelDao.kt\app/src/main/java/com/iptv/android/presentation/screens/auth/components/DeviceLimitScreen.ktIapp/src/main/java/com/iptv/android/core/media/CodecCompatibilityHelper.ktSapp/src/main/java/com/iptv/android/presentation/screens/profile/ProfileViewModel.ktLapp/src/main/java/com/iptv/android/presentation/navigation/IPTVNavigation.ktHapp/src/main/java/com/iptv/android/core/network/NetworkSecurityHelper.ktFapp/src/main/java/com/iptv/android/data/local/database/IPTVDatabase.ktUapp/src/main/java/com/iptv/android/presentation/components/livetv/FavoritesManager.ktHapp/src/main/java/com/iptv/android/presentation/components/IPTVButton.ktaapp/src/main/java/com/iptv/android/presentation/screens/category/components/CategoryComponents.ktSapp/src/main/java/com/iptv/android/presentation/navigation/NavigationTransitions.ktQapp/src/main/java/com/iptv/android/domain/usecase/user/ParentalControlUseCases.ktIapp/src/main/java/com/iptv/android/core/device/DeviceIdentifierManager.ktOapp/src/main/java/com/iptv/android/data/remote/dto/RenewUserAgentResponseDto.ktLapp/src/main/java/com/iptv/android/presentation/components/LoadingOverlay.ktJapp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/src/main/java/com/iptv/android/presentation/player/AdvancedPlayerScreen.ktHapp/src/main/java/com/iptv/android/data/repository/AuthRepositoryImpl.kt=app/src/main/java/com/iptv/android/domain/model/AudioTrack.ktYapp/src/main/java/com/iptv/android/data/remote/interceptor/DynamicUserAgentInterceptor.ktOapp/src/main/java/com/iptv/android/presentation/components/ParentalPinDialog.kt@app/src/main/java/com/iptv/android/domain/model/ChannelModels.ktWapp/src/main/java/com/iptv/android/data/remote/interceptor/DynamicBaseUrlInterceptor.kt?app/src/main/java/com/iptv/android/data/mapper/ContentMapper.kt=app/src/main/java/com/iptv/android/core/utils/ErrorHandler.ktTapp/src/main/java/com/iptv/android/domain/usecase/series/GetSeasonEpisodesUseCase.kt@app/src/main/java/com/iptv/android/domain/model/ContentModels.ktIapp/src/main/java/com/iptv/android/data/remote/api/RefreshTokenService.ktZapp/src/main/java/com/iptv/android/presentation/screens/search/components/SearchFilters.ktYapp/src/main/java/com/iptv/android/presentation/screens/security/SecurityWarningScreen.ktHapp/src/main/java/com/iptv/android/data/repository/UserRepositoryImpl.ktGapp/src/main/java/com/iptv/android/data/local/datastore/TokenManager.kt=app/src/main/java/com/iptv/android/core/error/ErrorHandler.ktUapp/src/main/java/com/iptv/android/presentation/screens/detail/ContentDetailScreen.ktIapp/src/main/java/com/iptv/android/data/remote/dto/ChannelsResponseDto.kt[app/src/main/java/com/iptv/android/presentation/components/common/GoogleTVFocusIndicator.kt[app/src/main/java/com/iptv/android/presentation/player/components/AdvancedPlayerControls.ktTapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVDirectScreen.ktUapp/src/main/java/com/iptv/android/presentation/screens/auth/DeviceSelectionScreen.ktRapp/src/main/java/com/iptv/android/presentation/screens/auth/UserLoginViewModel.ktdapp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/HorizontalContentCarousel.ktNapp/src/main/java/com/iptv/android/presentation/screens/player/PlayerScreen.ktQapp/src/main/java/com/iptv/android/presentation/navigation/NavigationViewModel.ktMapp/src/main/java/com/iptv/android/presentation/screens/home/<USER>/src/main/java/com/iptv/android/presentation/screens/auth/ModernDeviceRegistrationScreen.ktaapp/src/main/java/com/iptv/android/presentation/components/livetv/ModernChannelSelectorPreview.kt7app/src/main/java/com/iptv/android/di/SecurityModule.kt7app/src/main/java/com/iptv/android/di/DatabaseModule.ktRapp/src/main/java/com/iptv/android/presentation/navigation/GoogleTVFocusManager.ktSapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelEPGInfo.ktFapp/src/main/java/com/iptv/android/domain/repository/UserRepository.kt<app/src/main/java/com/iptv/android/data/local/dao/UserDao.kt^app/src/main/java/com/iptv/android/presentation/screens/search/components/VoiceSearchButton.ktVapp/src/main/java/com/iptv/android/presentation/screens/category/ModernCategoryDemo.kt^app/src/main/java/com/iptv/android/presentation/screens/player/components/VODPlayerControls.ktXapp/src/main/java/com/iptv/android/presentation/screens/detail/ContentDetailViewModel.ktKapp/src/main/java/com/iptv/android/presentation/screens/auth/LoginScreen.ktKapp/src/main/java/com/iptv/android/presentation/components/IPTVTextField.ktOapp/src/main/java/com/iptv/android/presentation/screens/auth/UserLoginScreen.ktTapp/src/main/java/com/iptv/android/presentation/components/ParentalPinSetupDialog.ktAapp/src/main/java/com/iptv/android/data/remote/dto/ContentDtos.ktJapp/src/main/java/com/iptv/android/presentation/player/ExoPlayerManager.ktSapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelPreview.ktNapp/src/main/java/com/iptv/android/presentation/components/GoogleTVCarousel.kt[app/src/main/java/com/iptv/android/presentation/screens/player/components/PlayerControls.ktJapp/src/main/java/com/iptv/android/domain/usecase/auth/LoginUserUseCase.ktWapp/src/main/java/com/iptv/android/presentation/components/livetv/ChannelInfoOverlay.ktPapp/src/main/java/com/iptv/android/presentation/components/CompactSearchField.ktQapp/src/main/java/com/iptv/android/presentation/screens/livetv/LiveTVViewModel.ktaapp/src/main/java/com/iptv/android/presentation/screens/player/components/LiveTVPlayerControls.kt\app/src/main/java/com/iptv/android/presentation/screens/home/<USER>/NavigationSidebar.ktFapp/src/main/java/com/iptv/android/domain/usecase/auth/AuthUseCases.ktDapp/src/main/java/com/iptv/android/presentation/login/LoginScreen.ktXapp/src/main/java/com/iptv/android/presentation/components/livetv/GoogleTVChannelCard.ktKapp/src/main/java/com/iptv/android/core/security/DynamicUserAgentManager.ktQapp/src/main/java/com/iptv/android/presentation/components/ModernNavigationBar.kt>app/src/main/java/com/iptv/android/data/remote/dto/UserDtos.ktOapp/src/main/java/com/iptv/android/presentation/components/ModernContentCard.kt=app/src/main/java/com/iptv/android/presentation/theme/Type.ktWapp/src/main/java/com/iptv/android/presentation/screens/series/SeriesDetailViewModel.ktZapp/src/main/java/com/iptv/android/presentation/components/livetv/ModernChannelSelector.ktNapp/src/main/java/com/iptv/android/presentation/screens/auth/LoginViewModel.kt6app/src/main/java/com/iptv/android/di/NetworkModule.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        