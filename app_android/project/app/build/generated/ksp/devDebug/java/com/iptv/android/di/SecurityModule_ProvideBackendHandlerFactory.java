package com.iptv.android.di;

import android.content.Context;
import com.iptv.android.core.network.BackendHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_ProvideBackendHandlerFactory implements Factory<BackendHandler> {
  private final Provider<Context> contextProvider;

  public SecurityModule_ProvideBackendHandlerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public BackendHandler get() {
    return provideBackendHandler(contextProvider.get());
  }

  public static SecurityModule_ProvideBackendHandlerFactory create(
      Provider<Context> contextProvider) {
    return new SecurityModule_ProvideBackendHandlerFactory(contextProvider);
  }

  public static BackendHandler provideBackendHandler(Context context) {
    return Preconditions.checkNotNullFromProvides(SecurityModule.INSTANCE.provideBackendHandler(context));
  }
}
