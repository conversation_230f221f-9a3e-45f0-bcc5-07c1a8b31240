package com.iptv.android.presentation.screens.category;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.data.local.datastore.UserPreferencesManager;
import com.iptv.android.domain.usecase.content.GetCategoriesUseCase;
import com.iptv.android.domain.usecase.content.GetContentByCategoryUseCase;
import com.iptv.android.domain.usecase.user.SetParentalPinUseCase;
import com.iptv.android.domain.usecase.user.VerifyParentalPinUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CategoryViewModel_Factory implements Factory<CategoryViewModel> {
  private final Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider;

  private final Provider<GetCategoriesUseCase> getCategoriesUseCaseProvider;

  private final Provider<VerifyParentalPinUseCase> verifyParentalPinUseCaseProvider;

  private final Provider<SetParentalPinUseCase> setParentalPinUseCaseProvider;

  private final Provider<UserPreferencesManager> userPreferencesManagerProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public CategoryViewModel_Factory(
      Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider,
      Provider<GetCategoriesUseCase> getCategoriesUseCaseProvider,
      Provider<VerifyParentalPinUseCase> verifyParentalPinUseCaseProvider,
      Provider<SetParentalPinUseCase> setParentalPinUseCaseProvider,
      Provider<UserPreferencesManager> userPreferencesManagerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.getContentByCategoryUseCaseProvider = getContentByCategoryUseCaseProvider;
    this.getCategoriesUseCaseProvider = getCategoriesUseCaseProvider;
    this.verifyParentalPinUseCaseProvider = verifyParentalPinUseCaseProvider;
    this.setParentalPinUseCaseProvider = setParentalPinUseCaseProvider;
    this.userPreferencesManagerProvider = userPreferencesManagerProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public CategoryViewModel get() {
    return newInstance(getContentByCategoryUseCaseProvider.get(), getCategoriesUseCaseProvider.get(), verifyParentalPinUseCaseProvider.get(), setParentalPinUseCaseProvider.get(), userPreferencesManagerProvider.get(), errorHandlerProvider.get());
  }

  public static CategoryViewModel_Factory create(
      Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider,
      Provider<GetCategoriesUseCase> getCategoriesUseCaseProvider,
      Provider<VerifyParentalPinUseCase> verifyParentalPinUseCaseProvider,
      Provider<SetParentalPinUseCase> setParentalPinUseCaseProvider,
      Provider<UserPreferencesManager> userPreferencesManagerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new CategoryViewModel_Factory(getContentByCategoryUseCaseProvider, getCategoriesUseCaseProvider, verifyParentalPinUseCaseProvider, setParentalPinUseCaseProvider, userPreferencesManagerProvider, errorHandlerProvider);
  }

  public static CategoryViewModel newInstance(
      GetContentByCategoryUseCase getContentByCategoryUseCase,
      GetCategoriesUseCase getCategoriesUseCase, VerifyParentalPinUseCase verifyParentalPinUseCase,
      SetParentalPinUseCase setParentalPinUseCase, UserPreferencesManager userPreferencesManager,
      ErrorHandler errorHandler) {
    return new CategoryViewModel(getContentByCategoryUseCase, getCategoriesUseCase, verifyParentalPinUseCase, setParentalPinUseCase, userPreferencesManager, errorHandler);
  }
}
