package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CheckAdultContentAccessUseCase_Factory implements Factory<CheckAdultContentAccessUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public CheckAdultContentAccessUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public CheckAdultContentAccessUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static CheckAdultContentAccessUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new CheckAdultContentAccessUseCase_Factory(userRepositoryProvider);
  }

  public static CheckAdultContentAccessUseCase newInstance(UserRepository userRepository) {
    return new CheckAdultContentAccessUseCase(userRepository);
  }
}
