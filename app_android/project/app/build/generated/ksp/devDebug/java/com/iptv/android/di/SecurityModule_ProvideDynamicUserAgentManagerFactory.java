package com.iptv.android.di;

import android.content.Context;
import com.iptv.android.core.security.DynamicUserAgentManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import kotlinx.coroutines.CoroutineScope;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_ProvideDynamicUserAgentManagerFactory implements Factory<DynamicUserAgentManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CoroutineScope> coroutineScopeProvider;

  public SecurityModule_ProvideDynamicUserAgentManagerFactory(Provider<Context> contextProvider,
      Provider<CoroutineScope> coroutineScopeProvider) {
    this.contextProvider = contextProvider;
    this.coroutineScopeProvider = coroutineScopeProvider;
  }

  @Override
  public DynamicUserAgentManager get() {
    return provideDynamicUserAgentManager(contextProvider.get(), coroutineScopeProvider.get());
  }

  public static SecurityModule_ProvideDynamicUserAgentManagerFactory create(
      Provider<Context> contextProvider, Provider<CoroutineScope> coroutineScopeProvider) {
    return new SecurityModule_ProvideDynamicUserAgentManagerFactory(contextProvider, coroutineScopeProvider);
  }

  public static DynamicUserAgentManager provideDynamicUserAgentManager(Context context,
      CoroutineScope coroutineScope) {
    return Preconditions.checkNotNullFromProvides(SecurityModule.INSTANCE.provideDynamicUserAgentManager(context, coroutineScope));
  }
}
