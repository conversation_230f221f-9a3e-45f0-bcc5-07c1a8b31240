package com.iptv.android.core.security;

import com.iptv.android.data.local.datastore.TokenManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserAgentRenewalManager_Factory implements Factory<UserAgentRenewalManager> {
  private final Provider<TokenManager> tokenManagerProvider;

  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  public UserAgentRenewalManager_Factory(Provider<TokenManager> tokenManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider) {
    this.tokenManagerProvider = tokenManagerProvider;
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
  }

  @Override
  public UserAgentRenewalManager get() {
    return newInstance(tokenManagerProvider.get(), dynamicUserAgentManagerProvider.get());
  }

  public static UserAgentRenewalManager_Factory create(Provider<TokenManager> tokenManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider) {
    return new UserAgentRenewalManager_Factory(tokenManagerProvider, dynamicUserAgentManagerProvider);
  }

  public static UserAgentRenewalManager newInstance(TokenManager tokenManager,
      DynamicUserAgentManager dynamicUserAgentManager) {
    return new UserAgentRenewalManager(tokenManager, dynamicUserAgentManager);
  }
}
