package com.iptv.android.core.security;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import kotlinx.coroutines.CoroutineScope;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DynamicUserAgentManager_Factory implements Factory<DynamicUserAgentManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CoroutineScope> coroutineScopeProvider;

  public DynamicUserAgentManager_Factory(Provider<Context> contextProvider,
      Provider<CoroutineScope> coroutineScopeProvider) {
    this.contextProvider = contextProvider;
    this.coroutineScopeProvider = coroutineScopeProvider;
  }

  @Override
  public DynamicUserAgentManager get() {
    return newInstance(contextProvider.get(), coroutineScopeProvider.get());
  }

  public static DynamicUserAgentManager_Factory create(Provider<Context> contextProvider,
      Provider<CoroutineScope> coroutineScopeProvider) {
    return new DynamicUserAgentManager_Factory(contextProvider, coroutineScopeProvider);
  }

  public static DynamicUserAgentManager newInstance(Context context,
      CoroutineScope coroutineScope) {
    return new DynamicUserAgentManager(context, coroutineScope);
  }
}
