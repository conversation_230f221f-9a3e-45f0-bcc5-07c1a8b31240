package com.iptv.android.di;

import com.iptv.android.domain.repository.ContentRepository;
import com.iptv.android.domain.usecase.content.GetCategoriesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideGetCategoriesUseCaseFactory implements Factory<GetCategoriesUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public UseCaseModule_ProvideGetCategoriesUseCaseFactory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetCategoriesUseCase get() {
    return provideGetCategoriesUseCase(contentRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideGetCategoriesUseCaseFactory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new UseCaseModule_ProvideGetCategoriesUseCaseFactory(contentRepositoryProvider);
  }

  public static GetCategoriesUseCase provideGetCategoriesUseCase(
      ContentRepository contentRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideGetCategoriesUseCase(contentRepository));
  }
}
