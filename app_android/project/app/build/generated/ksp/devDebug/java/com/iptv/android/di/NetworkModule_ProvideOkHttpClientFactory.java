package com.iptv.android.di;

import com.iptv.android.data.remote.interceptor.AuthInterceptor;
import com.iptv.android.data.remote.interceptor.DynamicBaseUrlInterceptor;
import com.iptv.android.data.remote.interceptor.DynamicUserAgentInterceptor;
import com.iptv.android.data.remote.interceptor.ErrorInterceptor;
import com.iptv.android.data.remote.interceptor.UserAgentErrorInterceptor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideOkHttpClientFactory implements Factory<OkHttpClient> {
  private final Provider<DynamicBaseUrlInterceptor> dynamicBaseUrlInterceptorProvider;

  private final Provider<AuthInterceptor> authInterceptorProvider;

  private final Provider<DynamicUserAgentInterceptor> dynamicUserAgentInterceptorProvider;

  private final Provider<UserAgentErrorInterceptor> userAgentErrorInterceptorProvider;

  private final Provider<ErrorInterceptor> errorInterceptorProvider;

  private final Provider<HttpLoggingInterceptor> loggingInterceptorProvider;

  public NetworkModule_ProvideOkHttpClientFactory(
      Provider<DynamicBaseUrlInterceptor> dynamicBaseUrlInterceptorProvider,
      Provider<AuthInterceptor> authInterceptorProvider,
      Provider<DynamicUserAgentInterceptor> dynamicUserAgentInterceptorProvider,
      Provider<UserAgentErrorInterceptor> userAgentErrorInterceptorProvider,
      Provider<ErrorInterceptor> errorInterceptorProvider,
      Provider<HttpLoggingInterceptor> loggingInterceptorProvider) {
    this.dynamicBaseUrlInterceptorProvider = dynamicBaseUrlInterceptorProvider;
    this.authInterceptorProvider = authInterceptorProvider;
    this.dynamicUserAgentInterceptorProvider = dynamicUserAgentInterceptorProvider;
    this.userAgentErrorInterceptorProvider = userAgentErrorInterceptorProvider;
    this.errorInterceptorProvider = errorInterceptorProvider;
    this.loggingInterceptorProvider = loggingInterceptorProvider;
  }

  @Override
  public OkHttpClient get() {
    return provideOkHttpClient(dynamicBaseUrlInterceptorProvider.get(), authInterceptorProvider.get(), dynamicUserAgentInterceptorProvider.get(), userAgentErrorInterceptorProvider.get(), errorInterceptorProvider.get(), loggingInterceptorProvider.get());
  }

  public static NetworkModule_ProvideOkHttpClientFactory create(
      Provider<DynamicBaseUrlInterceptor> dynamicBaseUrlInterceptorProvider,
      Provider<AuthInterceptor> authInterceptorProvider,
      Provider<DynamicUserAgentInterceptor> dynamicUserAgentInterceptorProvider,
      Provider<UserAgentErrorInterceptor> userAgentErrorInterceptorProvider,
      Provider<ErrorInterceptor> errorInterceptorProvider,
      Provider<HttpLoggingInterceptor> loggingInterceptorProvider) {
    return new NetworkModule_ProvideOkHttpClientFactory(dynamicBaseUrlInterceptorProvider, authInterceptorProvider, dynamicUserAgentInterceptorProvider, userAgentErrorInterceptorProvider, errorInterceptorProvider, loggingInterceptorProvider);
  }

  public static OkHttpClient provideOkHttpClient(
      DynamicBaseUrlInterceptor dynamicBaseUrlInterceptor, AuthInterceptor authInterceptor,
      DynamicUserAgentInterceptor dynamicUserAgentInterceptor,
      UserAgentErrorInterceptor userAgentErrorInterceptor, ErrorInterceptor errorInterceptor,
      HttpLoggingInterceptor loggingInterceptor) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideOkHttpClient(dynamicBaseUrlInterceptor, authInterceptor, dynamicUserAgentInterceptor, userAgentErrorInterceptor, errorInterceptor, loggingInterceptor));
  }
}
