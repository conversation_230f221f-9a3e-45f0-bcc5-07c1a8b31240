package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VerifyAdultContentAccessUseCase_Factory implements Factory<VerifyAdultContentAccessUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public VerifyAdultContentAccessUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public VerifyAdultContentAccessUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static VerifyAdultContentAccessUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new VerifyAdultContentAccessUseCase_Factory(userRepositoryProvider);
  }

  public static VerifyAdultContentAccessUseCase newInstance(UserRepository userRepository) {
    return new VerifyAdultContentAccessUseCase(userRepository);
  }
}
