package com.iptv.android.di;

import com.iptv.android.domain.repository.ContentRepository;
import com.iptv.android.domain.usecase.content.SearchContentUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideSearchContentUseCaseFactory implements Factory<SearchContentUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public UseCaseModule_ProvideSearchContentUseCaseFactory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public SearchContentUseCase get() {
    return provideSearchContentUseCase(contentRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideSearchContentUseCaseFactory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new UseCaseModule_ProvideSearchContentUseCaseFactory(contentRepositoryProvider);
  }

  public static SearchContentUseCase provideSearchContentUseCase(
      ContentRepository contentRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideSearchContentUseCase(contentRepository));
  }
}
