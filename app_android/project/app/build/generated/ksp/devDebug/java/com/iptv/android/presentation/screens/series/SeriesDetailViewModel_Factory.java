package com.iptv.android.presentation.screens.series;

import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import com.iptv.android.domain.usecase.content.GetNovelaByContentIdUseCase;
import com.iptv.android.domain.usecase.content.GetNovelaSeasonsUseCase;
import com.iptv.android.domain.usecase.series.GetSeriesDetailsByContentIdUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SeriesDetailViewModel_Factory implements Factory<SeriesDetailViewModel> {
  private final Provider<GetSeriesDetailsByContentIdUseCase> getSeriesDetailsByContentIdUseCaseProvider;

  private final Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider;

  private final Provider<GetNovelaByContentIdUseCase> getNovelaByContentIdUseCaseProvider;

  private final Provider<GetNovelaSeasonsUseCase> getNovelaSeasonsUseCaseProvider;

  public SeriesDetailViewModel_Factory(
      Provider<GetSeriesDetailsByContentIdUseCase> getSeriesDetailsByContentIdUseCaseProvider,
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<GetNovelaByContentIdUseCase> getNovelaByContentIdUseCaseProvider,
      Provider<GetNovelaSeasonsUseCase> getNovelaSeasonsUseCaseProvider) {
    this.getSeriesDetailsByContentIdUseCaseProvider = getSeriesDetailsByContentIdUseCaseProvider;
    this.getContentDetailsUseCaseProvider = getContentDetailsUseCaseProvider;
    this.getNovelaByContentIdUseCaseProvider = getNovelaByContentIdUseCaseProvider;
    this.getNovelaSeasonsUseCaseProvider = getNovelaSeasonsUseCaseProvider;
  }

  @Override
  public SeriesDetailViewModel get() {
    return newInstance(getSeriesDetailsByContentIdUseCaseProvider.get(), getContentDetailsUseCaseProvider.get(), getNovelaByContentIdUseCaseProvider.get(), getNovelaSeasonsUseCaseProvider.get());
  }

  public static SeriesDetailViewModel_Factory create(
      Provider<GetSeriesDetailsByContentIdUseCase> getSeriesDetailsByContentIdUseCaseProvider,
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<GetNovelaByContentIdUseCase> getNovelaByContentIdUseCaseProvider,
      Provider<GetNovelaSeasonsUseCase> getNovelaSeasonsUseCaseProvider) {
    return new SeriesDetailViewModel_Factory(getSeriesDetailsByContentIdUseCaseProvider, getContentDetailsUseCaseProvider, getNovelaByContentIdUseCaseProvider, getNovelaSeasonsUseCaseProvider);
  }

  public static SeriesDetailViewModel newInstance(
      GetSeriesDetailsByContentIdUseCase getSeriesDetailsByContentIdUseCase,
      GetContentDetailsUseCase getContentDetailsUseCase,
      GetNovelaByContentIdUseCase getNovelaByContentIdUseCase,
      GetNovelaSeasonsUseCase getNovelaSeasonsUseCase) {
    return new SeriesDetailViewModel(getSeriesDetailsByContentIdUseCase, getContentDetailsUseCase, getNovelaByContentIdUseCase, getNovelaSeasonsUseCase);
  }
}
