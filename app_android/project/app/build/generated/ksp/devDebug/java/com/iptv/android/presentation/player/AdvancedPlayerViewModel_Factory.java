package com.iptv.android.presentation.player;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import com.iptv.android.domain.usecase.user.UpdateWatchProgressUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AdvancedPlayerViewModel_Factory implements Factory<AdvancedPlayerViewModel> {
  private final Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider;

  private final Provider<UpdateWatchProgressUseCase> updateWatchProgressUseCaseProvider;

  private final Provider<ExoPlayerManager> exoPlayerManagerProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public AdvancedPlayerViewModel_Factory(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<UpdateWatchProgressUseCase> updateWatchProgressUseCaseProvider,
      Provider<ExoPlayerManager> exoPlayerManagerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.getContentDetailsUseCaseProvider = getContentDetailsUseCaseProvider;
    this.updateWatchProgressUseCaseProvider = updateWatchProgressUseCaseProvider;
    this.exoPlayerManagerProvider = exoPlayerManagerProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public AdvancedPlayerViewModel get() {
    return newInstance(getContentDetailsUseCaseProvider.get(), updateWatchProgressUseCaseProvider.get(), exoPlayerManagerProvider.get(), errorHandlerProvider.get());
  }

  public static AdvancedPlayerViewModel_Factory create(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<UpdateWatchProgressUseCase> updateWatchProgressUseCaseProvider,
      Provider<ExoPlayerManager> exoPlayerManagerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new AdvancedPlayerViewModel_Factory(getContentDetailsUseCaseProvider, updateWatchProgressUseCaseProvider, exoPlayerManagerProvider, errorHandlerProvider);
  }

  public static AdvancedPlayerViewModel newInstance(
      GetContentDetailsUseCase getContentDetailsUseCase,
      UpdateWatchProgressUseCase updateWatchProgressUseCase, ExoPlayerManager exoPlayerManager,
      ErrorHandler errorHandler) {
    return new AdvancedPlayerViewModel(getContentDetailsUseCase, updateWatchProgressUseCase, exoPlayerManager, errorHandler);
  }
}
