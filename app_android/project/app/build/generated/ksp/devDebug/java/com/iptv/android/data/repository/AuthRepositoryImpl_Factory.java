package com.iptv.android.data.repository;

import com.iptv.android.core.device.DeviceIdentifierManager;
import com.iptv.android.core.network.BackendHandler;
import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.data.local.datastore.TokenManager;
import com.iptv.android.data.local.datastore.UserPreferencesManager;
import com.iptv.android.data.remote.api.IPTVApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthRepositoryImpl_Factory implements Factory<AuthRepositoryImpl> {
  private final Provider<IPTVApiService> apiServiceProvider;

  private final Provider<TokenManager> tokenManagerProvider;

  private final Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider;

  private final Provider<UserPreferencesManager> userPreferencesManagerProvider;

  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  private final Provider<BackendHandler> backendHandlerProvider;

  public AuthRepositoryImpl_Factory(Provider<IPTVApiService> apiServiceProvider,
      Provider<TokenManager> tokenManagerProvider,
      Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider,
      Provider<UserPreferencesManager> userPreferencesManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<BackendHandler> backendHandlerProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.tokenManagerProvider = tokenManagerProvider;
    this.deviceIdentifierManagerProvider = deviceIdentifierManagerProvider;
    this.userPreferencesManagerProvider = userPreferencesManagerProvider;
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
    this.backendHandlerProvider = backendHandlerProvider;
  }

  @Override
  public AuthRepositoryImpl get() {
    return newInstance(apiServiceProvider.get(), tokenManagerProvider.get(), deviceIdentifierManagerProvider.get(), userPreferencesManagerProvider.get(), dynamicUserAgentManagerProvider.get(), backendHandlerProvider.get());
  }

  public static AuthRepositoryImpl_Factory create(Provider<IPTVApiService> apiServiceProvider,
      Provider<TokenManager> tokenManagerProvider,
      Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider,
      Provider<UserPreferencesManager> userPreferencesManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<BackendHandler> backendHandlerProvider) {
    return new AuthRepositoryImpl_Factory(apiServiceProvider, tokenManagerProvider, deviceIdentifierManagerProvider, userPreferencesManagerProvider, dynamicUserAgentManagerProvider, backendHandlerProvider);
  }

  public static AuthRepositoryImpl newInstance(IPTVApiService apiService, TokenManager tokenManager,
      DeviceIdentifierManager deviceIdentifierManager,
      UserPreferencesManager userPreferencesManager,
      DynamicUserAgentManager dynamicUserAgentManager, BackendHandler backendHandler) {
    return new AuthRepositoryImpl(apiService, tokenManager, deviceIdentifierManager, userPreferencesManager, dynamicUserAgentManager, backendHandler);
  }
}
