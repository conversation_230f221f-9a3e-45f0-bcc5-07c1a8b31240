package com.iptv.android.domain.usecase.series;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSeriesSeasonsUseCase_Factory implements Factory<GetSeriesSeasonsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetSeriesSeasonsUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetSeriesSeasonsUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetSeriesSeasonsUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetSeriesSeasonsUseCase_Factory(contentRepositoryProvider);
  }

  public static GetSeriesSeasonsUseCase newInstance(ContentRepository contentRepository) {
    return new GetSeriesSeasonsUseCase(contentRepository);
  }
}
