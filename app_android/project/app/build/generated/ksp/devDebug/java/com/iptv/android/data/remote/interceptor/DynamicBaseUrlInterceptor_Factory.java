package com.iptv.android.data.remote.interceptor;

import com.iptv.android.core.network.BackendHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DynamicBaseUrlInterceptor_Factory implements Factory<DynamicBaseUrlInterceptor> {
  private final Provider<BackendHandler> backendHandlerProvider;

  public DynamicBaseUrlInterceptor_Factory(Provider<BackendHandler> backendHandlerProvider) {
    this.backendHandlerProvider = backendHandlerProvider;
  }

  @Override
  public DynamicBaseUrlInterceptor get() {
    return newInstance(backendHandlerProvider.get());
  }

  public static DynamicBaseUrlInterceptor_Factory create(
      Provider<BackendHandler> backendHandlerProvider) {
    return new DynamicBaseUrlInterceptor_Factory(backendHandlerProvider);
  }

  public static DynamicBaseUrlInterceptor newInstance(BackendHandler backendHandler) {
    return new DynamicBaseUrlInterceptor(backendHandler);
  }
}
