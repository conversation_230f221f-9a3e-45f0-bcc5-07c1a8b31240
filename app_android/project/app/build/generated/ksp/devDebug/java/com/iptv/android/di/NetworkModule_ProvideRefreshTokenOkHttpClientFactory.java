package com.iptv.android.di;

import com.iptv.android.data.remote.interceptor.DynamicBaseUrlInterceptor;
import com.iptv.android.data.remote.interceptor.DynamicUserAgentInterceptor;
import com.iptv.android.data.remote.interceptor.ErrorInterceptor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideRefreshTokenOkHttpClientFactory implements Factory<OkHttpClient> {
  private final Provider<DynamicBaseUrlInterceptor> dynamicBaseUrlInterceptorProvider;

  private final Provider<DynamicUserAgentInterceptor> dynamicUserAgentInterceptorProvider;

  private final Provider<ErrorInterceptor> errorInterceptorProvider;

  private final Provider<HttpLoggingInterceptor> loggingInterceptorProvider;

  public NetworkModule_ProvideRefreshTokenOkHttpClientFactory(
      Provider<DynamicBaseUrlInterceptor> dynamicBaseUrlInterceptorProvider,
      Provider<DynamicUserAgentInterceptor> dynamicUserAgentInterceptorProvider,
      Provider<ErrorInterceptor> errorInterceptorProvider,
      Provider<HttpLoggingInterceptor> loggingInterceptorProvider) {
    this.dynamicBaseUrlInterceptorProvider = dynamicBaseUrlInterceptorProvider;
    this.dynamicUserAgentInterceptorProvider = dynamicUserAgentInterceptorProvider;
    this.errorInterceptorProvider = errorInterceptorProvider;
    this.loggingInterceptorProvider = loggingInterceptorProvider;
  }

  @Override
  public OkHttpClient get() {
    return provideRefreshTokenOkHttpClient(dynamicBaseUrlInterceptorProvider.get(), dynamicUserAgentInterceptorProvider.get(), errorInterceptorProvider.get(), loggingInterceptorProvider.get());
  }

  public static NetworkModule_ProvideRefreshTokenOkHttpClientFactory create(
      Provider<DynamicBaseUrlInterceptor> dynamicBaseUrlInterceptorProvider,
      Provider<DynamicUserAgentInterceptor> dynamicUserAgentInterceptorProvider,
      Provider<ErrorInterceptor> errorInterceptorProvider,
      Provider<HttpLoggingInterceptor> loggingInterceptorProvider) {
    return new NetworkModule_ProvideRefreshTokenOkHttpClientFactory(dynamicBaseUrlInterceptorProvider, dynamicUserAgentInterceptorProvider, errorInterceptorProvider, loggingInterceptorProvider);
  }

  public static OkHttpClient provideRefreshTokenOkHttpClient(
      DynamicBaseUrlInterceptor dynamicBaseUrlInterceptor,
      DynamicUserAgentInterceptor dynamicUserAgentInterceptor, ErrorInterceptor errorInterceptor,
      HttpLoggingInterceptor loggingInterceptor) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideRefreshTokenOkHttpClient(dynamicBaseUrlInterceptor, dynamicUserAgentInterceptor, errorInterceptor, loggingInterceptor));
  }
}
