package com.iptv.android.di;

import com.iptv.android.domain.repository.ContentRepository;
import com.iptv.android.domain.usecase.content.GetPopularContentUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideGetPopularContentUseCaseFactory implements Factory<GetPopularContentUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public UseCaseModule_ProvideGetPopularContentUseCaseFactory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetPopularContentUseCase get() {
    return provideGetPopularContentUseCase(contentRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideGetPopularContentUseCaseFactory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new UseCaseModule_ProvideGetPopularContentUseCaseFactory(contentRepositoryProvider);
  }

  public static GetPopularContentUseCase provideGetPopularContentUseCase(
      ContentRepository contentRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideGetPopularContentUseCase(contentRepository));
  }
}
