package com.iptv.android.di;

import com.iptv.android.domain.repository.UserRepository;
import com.iptv.android.domain.usecase.user.HasParentalPinUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideHasParentalPinUseCaseFactory implements Factory<HasParentalPinUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UseCaseModule_ProvideHasParentalPinUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public HasParentalPinUseCase get() {
    return provideHasParentalPinUseCase(userRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideHasParentalPinUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UseCaseModule_ProvideHasParentalPinUseCaseFactory(userRepositoryProvider);
  }

  public static HasParentalPinUseCase provideHasParentalPinUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideHasParentalPinUseCase(userRepository));
  }
}
