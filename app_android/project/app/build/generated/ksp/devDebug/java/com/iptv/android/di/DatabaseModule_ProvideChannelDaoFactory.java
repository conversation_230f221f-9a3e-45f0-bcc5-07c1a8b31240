package com.iptv.android.di;

import com.iptv.android.data.local.dao.ChannelDao;
import com.iptv.android.data.local.database.IPTVDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideChannelDaoFactory implements Factory<ChannelDao> {
  private final Provider<IPTVDatabase> databaseProvider;

  public DatabaseModule_ProvideChannelDaoFactory(Provider<IPTVDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ChannelDao get() {
    return provideChannelDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideChannelDaoFactory create(
      Provider<IPTVDatabase> databaseProvider) {
    return new DatabaseModule_ProvideChannelDaoFactory(databaseProvider);
  }

  public static ChannelDao provideChannelDao(IPTVDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideChannelDao(database));
  }
}
