package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetCategoriesUseCase_Factory implements Factory<GetCategoriesUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetCategoriesUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetCategoriesUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetCategoriesUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetCategoriesUseCase_Factory(contentRepositoryProvider);
  }

  public static GetCategoriesUseCase newInstance(ContentRepository contentRepository) {
    return new GetCategoriesUseCase(contentRepository);
  }
}
