package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetFavoritesFlowUseCase_Factory implements Factory<GetFavoritesFlowUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetFavoritesFlowUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetFavoritesFlowUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetFavoritesFlowUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetFavoritesFlowUseCase_Factory(userRepositoryProvider);
  }

  public static GetFavoritesFlowUseCase newInstance(UserRepository userRepository) {
    return new GetFavoritesFlowUseCase(userRepository);
  }
}
