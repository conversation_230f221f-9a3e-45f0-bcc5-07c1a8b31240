package com.iptv.android.di;

import com.iptv.android.domain.repository.UserRepository;
import com.iptv.android.domain.usecase.user.GetUserDevicesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideGetUserDevicesUseCaseFactory implements Factory<GetUserDevicesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UseCaseModule_ProvideGetUserDevicesUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetUserDevicesUseCase get() {
    return provideGetUserDevicesUseCase(userRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideGetUserDevicesUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UseCaseModule_ProvideGetUserDevicesUseCaseFactory(userRepositoryProvider);
  }

  public static GetUserDevicesUseCase provideGetUserDevicesUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideGetUserDevicesUseCase(userRepository));
  }
}
