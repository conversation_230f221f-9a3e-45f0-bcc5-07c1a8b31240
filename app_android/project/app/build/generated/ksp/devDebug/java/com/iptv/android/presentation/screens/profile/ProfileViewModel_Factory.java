package com.iptv.android.presentation.screens.profile;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.auth.LogoutUseCase;
import com.iptv.android.domain.usecase.user.ClearWatchHistoryUseCase;
import com.iptv.android.domain.usecase.user.GetFavoritesUseCase;
import com.iptv.android.domain.usecase.user.GetUserProfileUseCase;
import com.iptv.android.domain.usecase.user.GetWatchHistoryUseCase;
import com.iptv.android.domain.usecase.user.RemoveDeviceUseCase;
import com.iptv.android.domain.usecase.user.UpdateDeviceNameUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProfileViewModel_Factory implements Factory<ProfileViewModel> {
  private final Provider<GetUserProfileUseCase> getUserProfileUseCaseProvider;

  private final Provider<GetWatchHistoryUseCase> getWatchHistoryUseCaseProvider;

  private final Provider<GetFavoritesUseCase> getFavoritesUseCaseProvider;

  private final Provider<ClearWatchHistoryUseCase> clearWatchHistoryUseCaseProvider;

  private final Provider<RemoveDeviceUseCase> removeDeviceUseCaseProvider;

  private final Provider<UpdateDeviceNameUseCase> updateDeviceNameUseCaseProvider;

  private final Provider<LogoutUseCase> logoutUseCaseProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public ProfileViewModel_Factory(Provider<GetUserProfileUseCase> getUserProfileUseCaseProvider,
      Provider<GetWatchHistoryUseCase> getWatchHistoryUseCaseProvider,
      Provider<GetFavoritesUseCase> getFavoritesUseCaseProvider,
      Provider<ClearWatchHistoryUseCase> clearWatchHistoryUseCaseProvider,
      Provider<RemoveDeviceUseCase> removeDeviceUseCaseProvider,
      Provider<UpdateDeviceNameUseCase> updateDeviceNameUseCaseProvider,
      Provider<LogoutUseCase> logoutUseCaseProvider, Provider<ErrorHandler> errorHandlerProvider) {
    this.getUserProfileUseCaseProvider = getUserProfileUseCaseProvider;
    this.getWatchHistoryUseCaseProvider = getWatchHistoryUseCaseProvider;
    this.getFavoritesUseCaseProvider = getFavoritesUseCaseProvider;
    this.clearWatchHistoryUseCaseProvider = clearWatchHistoryUseCaseProvider;
    this.removeDeviceUseCaseProvider = removeDeviceUseCaseProvider;
    this.updateDeviceNameUseCaseProvider = updateDeviceNameUseCaseProvider;
    this.logoutUseCaseProvider = logoutUseCaseProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public ProfileViewModel get() {
    return newInstance(getUserProfileUseCaseProvider.get(), getWatchHistoryUseCaseProvider.get(), getFavoritesUseCaseProvider.get(), clearWatchHistoryUseCaseProvider.get(), removeDeviceUseCaseProvider.get(), updateDeviceNameUseCaseProvider.get(), logoutUseCaseProvider.get(), errorHandlerProvider.get());
  }

  public static ProfileViewModel_Factory create(
      Provider<GetUserProfileUseCase> getUserProfileUseCaseProvider,
      Provider<GetWatchHistoryUseCase> getWatchHistoryUseCaseProvider,
      Provider<GetFavoritesUseCase> getFavoritesUseCaseProvider,
      Provider<ClearWatchHistoryUseCase> clearWatchHistoryUseCaseProvider,
      Provider<RemoveDeviceUseCase> removeDeviceUseCaseProvider,
      Provider<UpdateDeviceNameUseCase> updateDeviceNameUseCaseProvider,
      Provider<LogoutUseCase> logoutUseCaseProvider, Provider<ErrorHandler> errorHandlerProvider) {
    return new ProfileViewModel_Factory(getUserProfileUseCaseProvider, getWatchHistoryUseCaseProvider, getFavoritesUseCaseProvider, clearWatchHistoryUseCaseProvider, removeDeviceUseCaseProvider, updateDeviceNameUseCaseProvider, logoutUseCaseProvider, errorHandlerProvider);
  }

  public static ProfileViewModel newInstance(GetUserProfileUseCase getUserProfileUseCase,
      GetWatchHistoryUseCase getWatchHistoryUseCase, GetFavoritesUseCase getFavoritesUseCase,
      ClearWatchHistoryUseCase clearWatchHistoryUseCase, RemoveDeviceUseCase removeDeviceUseCase,
      UpdateDeviceNameUseCase updateDeviceNameUseCase, LogoutUseCase logoutUseCase,
      ErrorHandler errorHandler) {
    return new ProfileViewModel(getUserProfileUseCase, getWatchHistoryUseCase, getFavoritesUseCase, clearWatchHistoryUseCase, removeDeviceUseCase, updateDeviceNameUseCase, logoutUseCase, errorHandler);
  }
}
