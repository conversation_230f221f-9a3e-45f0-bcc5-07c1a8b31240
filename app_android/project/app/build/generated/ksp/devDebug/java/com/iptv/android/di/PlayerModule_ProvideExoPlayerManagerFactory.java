package com.iptv.android.di;

import android.content.Context;
import com.iptv.android.core.media.CodecCompatibilityHelper;
import com.iptv.android.core.network.NetworkSecurityHelper;
import com.iptv.android.presentation.player.DrmHelper;
import com.iptv.android.presentation.player.ExoPlayerManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerModule_ProvideExoPlayerManagerFactory implements Factory<ExoPlayerManager> {
  private final Provider<Context> contextProvider;

  private final Provider<DrmHelper> drmHelperProvider;

  private final Provider<NetworkSecurityHelper> networkSecurityHelperProvider;

  private final Provider<CodecCompatibilityHelper> codecCompatibilityHelperProvider;

  public PlayerModule_ProvideExoPlayerManagerFactory(Provider<Context> contextProvider,
      Provider<DrmHelper> drmHelperProvider,
      Provider<NetworkSecurityHelper> networkSecurityHelperProvider,
      Provider<CodecCompatibilityHelper> codecCompatibilityHelperProvider) {
    this.contextProvider = contextProvider;
    this.drmHelperProvider = drmHelperProvider;
    this.networkSecurityHelperProvider = networkSecurityHelperProvider;
    this.codecCompatibilityHelperProvider = codecCompatibilityHelperProvider;
  }

  @Override
  public ExoPlayerManager get() {
    return provideExoPlayerManager(contextProvider.get(), drmHelperProvider.get(), networkSecurityHelperProvider.get(), codecCompatibilityHelperProvider.get());
  }

  public static PlayerModule_ProvideExoPlayerManagerFactory create(
      Provider<Context> contextProvider, Provider<DrmHelper> drmHelperProvider,
      Provider<NetworkSecurityHelper> networkSecurityHelperProvider,
      Provider<CodecCompatibilityHelper> codecCompatibilityHelperProvider) {
    return new PlayerModule_ProvideExoPlayerManagerFactory(contextProvider, drmHelperProvider, networkSecurityHelperProvider, codecCompatibilityHelperProvider);
  }

  public static ExoPlayerManager provideExoPlayerManager(Context context, DrmHelper drmHelper,
      NetworkSecurityHelper networkSecurityHelper,
      CodecCompatibilityHelper codecCompatibilityHelper) {
    return Preconditions.checkNotNullFromProvides(PlayerModule.INSTANCE.provideExoPlayerManager(context, drmHelper, networkSecurityHelper, codecCompatibilityHelper));
  }
}
