package com.iptv.android.presentation.player;

import android.content.Context;
import com.iptv.android.core.media.CodecCompatibilityHelper;
import com.iptv.android.core.network.NetworkSecurityHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExoPlayerManager_Factory implements Factory<ExoPlayerManager> {
  private final Provider<Context> contextProvider;

  private final Provider<DrmHelper> drmHelperProvider;

  private final Provider<NetworkSecurityHelper> networkSecurityHelperProvider;

  private final Provider<CodecCompatibilityHelper> codecCompatibilityHelperProvider;

  public ExoPlayerManager_Factory(Provider<Context> contextProvider,
      Provider<DrmHelper> drmHelperProvider,
      Provider<NetworkSecurityHelper> networkSecurityHelperProvider,
      Provider<CodecCompatibilityHelper> codecCompatibilityHelperProvider) {
    this.contextProvider = contextProvider;
    this.drmHelperProvider = drmHelperProvider;
    this.networkSecurityHelperProvider = networkSecurityHelperProvider;
    this.codecCompatibilityHelperProvider = codecCompatibilityHelperProvider;
  }

  @Override
  public ExoPlayerManager get() {
    return newInstance(contextProvider.get(), drmHelperProvider.get(), networkSecurityHelperProvider.get(), codecCompatibilityHelperProvider.get());
  }

  public static ExoPlayerManager_Factory create(Provider<Context> contextProvider,
      Provider<DrmHelper> drmHelperProvider,
      Provider<NetworkSecurityHelper> networkSecurityHelperProvider,
      Provider<CodecCompatibilityHelper> codecCompatibilityHelperProvider) {
    return new ExoPlayerManager_Factory(contextProvider, drmHelperProvider, networkSecurityHelperProvider, codecCompatibilityHelperProvider);
  }

  public static ExoPlayerManager newInstance(Context context, DrmHelper drmHelper,
      NetworkSecurityHelper networkSecurityHelper,
      CodecCompatibilityHelper codecCompatibilityHelper) {
    return new ExoPlayerManager(context, drmHelper, networkSecurityHelper, codecCompatibilityHelper);
  }
}
