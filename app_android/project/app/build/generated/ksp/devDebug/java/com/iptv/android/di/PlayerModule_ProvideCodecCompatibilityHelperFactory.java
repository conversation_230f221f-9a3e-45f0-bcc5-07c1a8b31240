package com.iptv.android.di;

import android.content.Context;
import com.iptv.android.core.media.CodecCompatibilityHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerModule_ProvideCodecCompatibilityHelperFactory implements Factory<CodecCompatibilityHelper> {
  private final Provider<Context> contextProvider;

  public PlayerModule_ProvideCodecCompatibilityHelperFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CodecCompatibilityHelper get() {
    return provideCodecCompatibilityHelper(contextProvider.get());
  }

  public static PlayerModule_ProvideCodecCompatibilityHelperFactory create(
      Provider<Context> contextProvider) {
    return new PlayerModule_ProvideCodecCompatibilityHelperFactory(contextProvider);
  }

  public static CodecCompatibilityHelper provideCodecCompatibilityHelper(Context context) {
    return Preconditions.checkNotNullFromProvides(PlayerModule.INSTANCE.provideCodecCompatibilityHelper(context));
  }
}
