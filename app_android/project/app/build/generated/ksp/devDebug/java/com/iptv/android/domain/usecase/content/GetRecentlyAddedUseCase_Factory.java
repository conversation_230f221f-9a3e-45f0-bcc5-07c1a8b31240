package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetRecentlyAddedUseCase_Factory implements Factory<GetRecentlyAddedUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetRecentlyAddedUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetRecentlyAddedUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetRecentlyAddedUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetRecentlyAddedUseCase_Factory(contentRepositoryProvider);
  }

  public static GetRecentlyAddedUseCase newInstance(ContentRepository contentRepository) {
    return new GetRecentlyAddedUseCase(contentRepository);
  }
}
