package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetNovelaSeasonEpisodesUseCase_Factory implements Factory<GetNovelaSeasonEpisodesUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetNovelaSeasonEpisodesUseCase_Factory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetNovelaSeasonEpisodesUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetNovelaSeasonEpisodesUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetNovelaSeasonEpisodesUseCase_Factory(contentRepositoryProvider);
  }

  public static GetNovelaSeasonEpisodesUseCase newInstance(ContentRepository contentRepository) {
    return new GetNovelaSeasonEpisodesUseCase(contentRepository);
  }
}
