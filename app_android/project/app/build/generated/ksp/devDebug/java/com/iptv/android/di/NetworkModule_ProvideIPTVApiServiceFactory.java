package com.iptv.android.di;

import com.iptv.android.data.remote.api.IPTVApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideIPTVApiServiceFactory implements Factory<IPTVApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideIPTVApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public IPTVApiService get() {
    return provideIPTVApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideIPTVApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideIPTVApiServiceFactory(retrofitProvider);
  }

  public static IPTVApiService provideIPTVApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideIPTVApiService(retrofit));
  }
}
