package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetContentByCategoryUseCase_Factory implements Factory<GetContentByCategoryUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetContentByCategoryUseCase_Factory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetContentByCategoryUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetContentByCategoryUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetContentByCategoryUseCase_Factory(contentRepositoryProvider);
  }

  public static GetContentByCategoryUseCase newInstance(ContentRepository contentRepository) {
    return new GetContentByCategoryUseCase(contentRepository);
  }
}
