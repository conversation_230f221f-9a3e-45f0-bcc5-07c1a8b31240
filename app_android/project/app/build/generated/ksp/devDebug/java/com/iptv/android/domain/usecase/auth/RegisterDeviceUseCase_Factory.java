package com.iptv.android.domain.usecase.auth;

import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RegisterDeviceUseCase_Factory implements Factory<RegisterDeviceUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public RegisterDeviceUseCase_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public RegisterDeviceUseCase get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static RegisterDeviceUseCase_Factory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new RegisterDeviceUseCase_Factory(authRepositoryProvider);
  }

  public static RegisterDeviceUseCase newInstance(AuthRepository authRepository) {
    return new RegisterDeviceUseCase(authRepository);
  }
}
