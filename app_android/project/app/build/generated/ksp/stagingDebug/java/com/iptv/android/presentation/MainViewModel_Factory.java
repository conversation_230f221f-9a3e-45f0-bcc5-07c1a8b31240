package com.iptv.android.presentation;

import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.core.security.UserAgentMonitorService;
import com.iptv.android.data.local.datastore.TokenManager;
import com.iptv.android.data.local.datastore.UserPreferencesManager;
import com.iptv.android.domain.repository.AuthRepository;
import com.iptv.android.domain.usecase.user.HasParentalPinUseCase;
import com.iptv.android.domain.usecase.user.SetParentalPinUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<UserPreferencesManager> userPreferencesManagerProvider;

  private final Provider<SetParentalPinUseCase> setParentalPinUseCaseProvider;

  private final Provider<HasParentalPinUseCase> hasParentalPinUseCaseProvider;

  private final Provider<TokenManager> tokenManagerProvider;

  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  private final Provider<UserAgentMonitorService> userAgentMonitorServiceProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  public MainViewModel_Factory(Provider<UserPreferencesManager> userPreferencesManagerProvider,
      Provider<SetParentalPinUseCase> setParentalPinUseCaseProvider,
      Provider<HasParentalPinUseCase> hasParentalPinUseCaseProvider,
      Provider<TokenManager> tokenManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<UserAgentMonitorService> userAgentMonitorServiceProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    this.userPreferencesManagerProvider = userPreferencesManagerProvider;
    this.setParentalPinUseCaseProvider = setParentalPinUseCaseProvider;
    this.hasParentalPinUseCaseProvider = hasParentalPinUseCaseProvider;
    this.tokenManagerProvider = tokenManagerProvider;
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
    this.userAgentMonitorServiceProvider = userAgentMonitorServiceProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(userPreferencesManagerProvider.get(), setParentalPinUseCaseProvider.get(), hasParentalPinUseCaseProvider.get(), tokenManagerProvider.get(), dynamicUserAgentManagerProvider.get(), userAgentMonitorServiceProvider.get(), authRepositoryProvider.get());
  }

  public static MainViewModel_Factory create(
      Provider<UserPreferencesManager> userPreferencesManagerProvider,
      Provider<SetParentalPinUseCase> setParentalPinUseCaseProvider,
      Provider<HasParentalPinUseCase> hasParentalPinUseCaseProvider,
      Provider<TokenManager> tokenManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<UserAgentMonitorService> userAgentMonitorServiceProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    return new MainViewModel_Factory(userPreferencesManagerProvider, setParentalPinUseCaseProvider, hasParentalPinUseCaseProvider, tokenManagerProvider, dynamicUserAgentManagerProvider, userAgentMonitorServiceProvider, authRepositoryProvider);
  }

  public static MainViewModel newInstance(UserPreferencesManager userPreferencesManager,
      SetParentalPinUseCase setParentalPinUseCase, HasParentalPinUseCase hasParentalPinUseCase,
      TokenManager tokenManager, DynamicUserAgentManager dynamicUserAgentManager,
      UserAgentMonitorService userAgentMonitorService, AuthRepository authRepository) {
    return new MainViewModel(userPreferencesManager, setParentalPinUseCase, hasParentalPinUseCase, tokenManager, dynamicUserAgentManager, userAgentMonitorService, authRepository);
  }
}
