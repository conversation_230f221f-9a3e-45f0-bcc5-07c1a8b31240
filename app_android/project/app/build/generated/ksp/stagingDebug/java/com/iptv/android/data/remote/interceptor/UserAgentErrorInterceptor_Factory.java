package com.iptv.android.data.remote.interceptor;

import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.core.security.UserAgentRenewalManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserAgentErrorInterceptor_Factory implements Factory<UserAgentErrorInterceptor> {
  private final Provider<DynamicUserAgentManager> userAgentManagerProvider;

  private final Provider<UserAgentRenewalManager> userAgentRenewalManagerProvider;

  public UserAgentErrorInterceptor_Factory(
      Provider<DynamicUserAgentManager> userAgentManagerProvider,
      Provider<UserAgentRenewalManager> userAgentRenewalManagerProvider) {
    this.userAgentManagerProvider = userAgentManagerProvider;
    this.userAgentRenewalManagerProvider = userAgentRenewalManagerProvider;
  }

  @Override
  public UserAgentErrorInterceptor get() {
    return newInstance(userAgentManagerProvider.get(), userAgentRenewalManagerProvider.get());
  }

  public static UserAgentErrorInterceptor_Factory create(
      Provider<DynamicUserAgentManager> userAgentManagerProvider,
      Provider<UserAgentRenewalManager> userAgentRenewalManagerProvider) {
    return new UserAgentErrorInterceptor_Factory(userAgentManagerProvider, userAgentRenewalManagerProvider);
  }

  public static UserAgentErrorInterceptor newInstance(DynamicUserAgentManager userAgentManager,
      UserAgentRenewalManager userAgentRenewalManager) {
    return new UserAgentErrorInterceptor(userAgentManager, userAgentRenewalManager);
  }
}
