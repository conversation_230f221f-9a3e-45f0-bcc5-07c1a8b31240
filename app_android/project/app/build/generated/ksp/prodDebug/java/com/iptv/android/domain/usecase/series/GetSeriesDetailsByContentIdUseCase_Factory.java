package com.iptv.android.domain.usecase.series;

import com.iptv.android.domain.repository.ContentRepository;
import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSeriesDetailsByContentIdUseCase_Factory implements Factory<GetSeriesDetailsByContentIdUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  private final Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider;

  public GetSeriesDetailsByContentIdUseCase_Factory(
      Provider<ContentRepository> contentRepositoryProvider,
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
    this.getContentDetailsUseCaseProvider = getContentDetailsUseCaseProvider;
  }

  @Override
  public GetSeriesDetailsByContentIdUseCase get() {
    return newInstance(contentRepositoryProvider.get(), getContentDetailsUseCaseProvider.get());
  }

  public static GetSeriesDetailsByContentIdUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider,
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider) {
    return new GetSeriesDetailsByContentIdUseCase_Factory(contentRepositoryProvider, getContentDetailsUseCaseProvider);
  }

  public static GetSeriesDetailsByContentIdUseCase newInstance(ContentRepository contentRepository,
      GetContentDetailsUseCase getContentDetailsUseCase) {
    return new GetSeriesDetailsByContentIdUseCase(contentRepository, getContentDetailsUseCase);
  }
}
