package com.iptv.android.presentation.screens.search;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.content.SearchContentUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchViewModel_Factory implements Factory<SearchViewModel> {
  private final Provider<SearchContentUseCase> searchContentUseCaseProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public SearchViewModel_Factory(Provider<SearchContentUseCase> searchContentUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.searchContentUseCaseProvider = searchContentUseCaseProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public SearchViewModel get() {
    return newInstance(searchContentUseCaseProvider.get(), errorHandlerProvider.get());
  }

  public static SearchViewModel_Factory create(
      Provider<SearchContentUseCase> searchContentUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new SearchViewModel_Factory(searchContentUseCaseProvider, errorHandlerProvider);
  }

  public static SearchViewModel newInstance(SearchContentUseCase searchContentUseCase,
      ErrorHandler errorHandler) {
    return new SearchViewModel(searchContentUseCase, errorHandler);
  }
}
