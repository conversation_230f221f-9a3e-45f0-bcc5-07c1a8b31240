package com.iptv.android.presentation.screens.detail;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DetailViewModel_Factory implements Factory<DetailViewModel> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public DetailViewModel_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public DetailViewModel get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static DetailViewModel_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new DetailViewModel_Factory(contentRepositoryProvider);
  }

  public static DetailViewModel newInstance(ContentRepository contentRepository) {
    return new DetailViewModel(contentRepository);
  }
}
