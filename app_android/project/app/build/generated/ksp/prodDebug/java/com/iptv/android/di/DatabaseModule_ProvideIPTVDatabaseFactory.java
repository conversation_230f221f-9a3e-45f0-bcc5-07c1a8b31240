package com.iptv.android.di;

import android.content.Context;
import com.iptv.android.data.local.database.IPTVDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideIPTVDatabaseFactory implements Factory<IPTVDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideIPTVDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public IPTVDatabase get() {
    return provideIPTVDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideIPTVDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideIPTVDatabaseFactory(contextProvider);
  }

  public static IPTVDatabase provideIPTVDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideIPTVDatabase(context));
  }
}
