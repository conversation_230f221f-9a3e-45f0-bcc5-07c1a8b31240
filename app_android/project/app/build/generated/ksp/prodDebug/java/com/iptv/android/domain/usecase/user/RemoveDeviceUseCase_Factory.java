package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RemoveDeviceUseCase_Factory implements Factory<RemoveDeviceUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public RemoveDeviceUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public RemoveDeviceUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static RemoveDeviceUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new RemoveDeviceUseCase_Factory(userRepositoryProvider);
  }

  public static RemoveDeviceUseCase newInstance(UserRepository userRepository) {
    return new RemoveDeviceUseCase(userRepository);
  }
}
