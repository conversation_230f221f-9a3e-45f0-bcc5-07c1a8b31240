package com.iptv.android.presentation.navigation;

import com.iptv.android.domain.usecase.auth.IsAuthenticatedUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NavigationViewModel_Factory implements Factory<NavigationViewModel> {
  private final Provider<IsAuthenticatedUseCase> isAuthenticatedUseCaseProvider;

  public NavigationViewModel_Factory(
      Provider<IsAuthenticatedUseCase> isAuthenticatedUseCaseProvider) {
    this.isAuthenticatedUseCaseProvider = isAuthenticatedUseCaseProvider;
  }

  @Override
  public NavigationViewModel get() {
    return newInstance(isAuthenticatedUseCaseProvider.get());
  }

  public static NavigationViewModel_Factory create(
      Provider<IsAuthenticatedUseCase> isAuthenticatedUseCaseProvider) {
    return new NavigationViewModel_Factory(isAuthenticatedUseCaseProvider);
  }

  public static NavigationViewModel newInstance(IsAuthenticatedUseCase isAuthenticatedUseCase) {
    return new NavigationViewModel(isAuthenticatedUseCase);
  }
}
