package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchContentUseCase_Factory implements Factory<SearchContentUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public SearchContentUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public SearchContentUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static SearchContentUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new SearchContentUseCase_Factory(contentRepositoryProvider);
  }

  public static SearchContentUseCase newInstance(ContentRepository contentRepository) {
    return new SearchContentUseCase(contentRepository);
  }
}
