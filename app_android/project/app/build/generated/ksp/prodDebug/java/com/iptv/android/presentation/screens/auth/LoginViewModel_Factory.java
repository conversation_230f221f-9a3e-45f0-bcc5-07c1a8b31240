package com.iptv.android.presentation.screens.auth;

import com.iptv.android.core.device.DeviceIdentifierManager;
import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.auth.GetDeviceStatusUseCase;
import com.iptv.android.domain.usecase.auth.RegisterDeviceUseCase;
import com.iptv.android.domain.usecase.device.DeviceLimitHandler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LoginViewModel_Factory implements Factory<LoginViewModel> {
  private final Provider<RegisterDeviceUseCase> registerDeviceUseCaseProvider;

  private final Provider<GetDeviceStatusUseCase> getDeviceStatusUseCaseProvider;

  private final Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider;

  private final Provider<DeviceLimitHandler> deviceLimitHandlerProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public LoginViewModel_Factory(Provider<RegisterDeviceUseCase> registerDeviceUseCaseProvider,
      Provider<GetDeviceStatusUseCase> getDeviceStatusUseCaseProvider,
      Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider,
      Provider<DeviceLimitHandler> deviceLimitHandlerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.registerDeviceUseCaseProvider = registerDeviceUseCaseProvider;
    this.getDeviceStatusUseCaseProvider = getDeviceStatusUseCaseProvider;
    this.deviceIdentifierManagerProvider = deviceIdentifierManagerProvider;
    this.deviceLimitHandlerProvider = deviceLimitHandlerProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public LoginViewModel get() {
    return newInstance(registerDeviceUseCaseProvider.get(), getDeviceStatusUseCaseProvider.get(), deviceIdentifierManagerProvider.get(), deviceLimitHandlerProvider.get(), errorHandlerProvider.get());
  }

  public static LoginViewModel_Factory create(
      Provider<RegisterDeviceUseCase> registerDeviceUseCaseProvider,
      Provider<GetDeviceStatusUseCase> getDeviceStatusUseCaseProvider,
      Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider,
      Provider<DeviceLimitHandler> deviceLimitHandlerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new LoginViewModel_Factory(registerDeviceUseCaseProvider, getDeviceStatusUseCaseProvider, deviceIdentifierManagerProvider, deviceLimitHandlerProvider, errorHandlerProvider);
  }

  public static LoginViewModel newInstance(RegisterDeviceUseCase registerDeviceUseCase,
      GetDeviceStatusUseCase getDeviceStatusUseCase,
      DeviceIdentifierManager deviceIdentifierManager, DeviceLimitHandler deviceLimitHandler,
      ErrorHandler errorHandler) {
    return new LoginViewModel(registerDeviceUseCase, getDeviceStatusUseCase, deviceIdentifierManager, deviceLimitHandler, errorHandler);
  }
}
