package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetUserDevicesUseCase_Factory implements Factory<GetUserDevicesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetUserDevicesUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetUserDevicesUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetUserDevicesUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetUserDevicesUseCase_Factory(userRepositoryProvider);
  }

  public static GetUserDevicesUseCase newInstance(UserRepository userRepository) {
    return new GetUserDevicesUseCase(userRepository);
  }
}
