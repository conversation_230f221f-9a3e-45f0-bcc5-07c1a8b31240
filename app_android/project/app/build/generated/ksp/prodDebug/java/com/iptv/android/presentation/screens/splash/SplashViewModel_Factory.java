package com.iptv.android.presentation.screens.splash;

import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.core.security.UserAgentRenewalManager;
import com.iptv.android.data.local.datastore.TokenManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SplashViewModel_Factory implements Factory<SplashViewModel> {
  private final Provider<TokenManager> tokenManagerProvider;

  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  private final Provider<UserAgentRenewalManager> userAgentRenewalManagerProvider;

  public SplashViewModel_Factory(Provider<TokenManager> tokenManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<UserAgentRenewalManager> userAgentRenewalManagerProvider) {
    this.tokenManagerProvider = tokenManagerProvider;
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
    this.userAgentRenewalManagerProvider = userAgentRenewalManagerProvider;
  }

  @Override
  public SplashViewModel get() {
    return newInstance(tokenManagerProvider.get(), dynamicUserAgentManagerProvider.get(), userAgentRenewalManagerProvider.get());
  }

  public static SplashViewModel_Factory create(Provider<TokenManager> tokenManagerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<UserAgentRenewalManager> userAgentRenewalManagerProvider) {
    return new SplashViewModel_Factory(tokenManagerProvider, dynamicUserAgentManagerProvider, userAgentRenewalManagerProvider);
  }

  public static SplashViewModel newInstance(TokenManager tokenManager,
      DynamicUserAgentManager dynamicUserAgentManager,
      UserAgentRenewalManager userAgentRenewalManager) {
    return new SplashViewModel(tokenManager, dynamicUserAgentManager, userAgentRenewalManager);
  }
}
