package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RemoveFromFavoritesUseCase_Factory implements Factory<RemoveFromFavoritesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public RemoveFromFavoritesUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public RemoveFromFavoritesUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static RemoveFromFavoritesUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new RemoveFromFavoritesUseCase_Factory(userRepositoryProvider);
  }

  public static RemoveFromFavoritesUseCase newInstance(UserRepository userRepository) {
    return new RemoveFromFavoritesUseCase(userRepository);
  }
}
