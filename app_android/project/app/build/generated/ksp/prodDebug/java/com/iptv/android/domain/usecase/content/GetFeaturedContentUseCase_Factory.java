package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetFeaturedContentUseCase_Factory implements Factory<GetFeaturedContentUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetFeaturedContentUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetFeaturedContentUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetFeaturedContentUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetFeaturedContentUseCase_Factory(contentRepositoryProvider);
  }

  public static GetFeaturedContentUseCase newInstance(ContentRepository contentRepository) {
    return new GetFeaturedContentUseCase(contentRepository);
  }
}
