package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.iptv.android.IPTVApplication",
    rootPackage = "com.iptv.android",
    originatingRoot = "com.iptv.android.IPTVApplication",
    originatingRootPackage = "com.iptv.android",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "IPTVApplication",
    originatingRootSimpleNames = "IPTVApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_iptv_android_IPTVApplication {
}
