package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RemoveParentalPinUseCase_Factory implements Factory<RemoveParentalPinUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public RemoveParentalPinUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public RemoveParentalPinUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static RemoveParentalPinUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new RemoveParentalPinUseCase_Factory(userRepositoryProvider);
  }

  public static RemoveParentalPinUseCase newInstance(UserRepository userRepository) {
    return new RemoveParentalPinUseCase(userRepository);
  }
}
