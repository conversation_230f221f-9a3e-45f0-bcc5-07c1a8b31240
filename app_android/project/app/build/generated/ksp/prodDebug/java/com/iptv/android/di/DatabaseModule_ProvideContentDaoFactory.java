package com.iptv.android.di;

import com.iptv.android.data.local.dao.ContentDao;
import com.iptv.android.data.local.database.IPTVDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideContentDaoFactory implements Factory<ContentDao> {
  private final Provider<IPTVDatabase> databaseProvider;

  public DatabaseModule_ProvideContentDaoFactory(Provider<IPTVDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ContentDao get() {
    return provideContentDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideContentDaoFactory create(
      Provider<IPTVDatabase> databaseProvider) {
    return new DatabaseModule_ProvideContentDaoFactory(databaseProvider);
  }

  public static ContentDao provideContentDao(IPTVDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideContentDao(database));
  }
}
