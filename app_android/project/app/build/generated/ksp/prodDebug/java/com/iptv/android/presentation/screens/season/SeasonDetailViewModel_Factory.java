package com.iptv.android.presentation.screens.season;

import com.iptv.android.domain.usecase.series.GetSeasonEpisodesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SeasonDetailViewModel_Factory implements Factory<SeasonDetailViewModel> {
  private final Provider<GetSeasonEpisodesUseCase> getSeasonEpisodesUseCaseProvider;

  public SeasonDetailViewModel_Factory(
      Provider<GetSeasonEpisodesUseCase> getSeasonEpisodesUseCaseProvider) {
    this.getSeasonEpisodesUseCaseProvider = getSeasonEpisodesUseCaseProvider;
  }

  @Override
  public SeasonDetailViewModel get() {
    return newInstance(getSeasonEpisodesUseCaseProvider.get());
  }

  public static SeasonDetailViewModel_Factory create(
      Provider<GetSeasonEpisodesUseCase> getSeasonEpisodesUseCaseProvider) {
    return new SeasonDetailViewModel_Factory(getSeasonEpisodesUseCaseProvider);
  }

  public static SeasonDetailViewModel newInstance(
      GetSeasonEpisodesUseCase getSeasonEpisodesUseCase) {
    return new SeasonDetailViewModel(getSeasonEpisodesUseCase);
  }
}
