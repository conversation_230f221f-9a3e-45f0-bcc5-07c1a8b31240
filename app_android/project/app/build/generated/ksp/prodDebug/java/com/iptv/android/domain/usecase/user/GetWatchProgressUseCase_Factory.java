package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetWatchProgressUseCase_Factory implements Factory<GetWatchProgressUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetWatchProgressUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetWatchProgressUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetWatchProgressUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetWatchProgressUseCase_Factory(userRepositoryProvider);
  }

  public static GetWatchProgressUseCase newInstance(UserRepository userRepository) {
    return new GetWatchProgressUseCase(userRepository);
  }
}
