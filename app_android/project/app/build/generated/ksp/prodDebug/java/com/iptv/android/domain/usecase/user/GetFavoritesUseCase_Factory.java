package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetFavoritesUseCase_Factory implements Factory<GetFavoritesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetFavoritesUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetFavoritesUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetFavoritesUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetFavoritesUseCase_Factory(userRepositoryProvider);
  }

  public static GetFavoritesUseCase newInstance(UserRepository userRepository) {
    return new GetFavoritesUseCase(userRepository);
  }
}
