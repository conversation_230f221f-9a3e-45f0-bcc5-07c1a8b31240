package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetContentDetailsUseCase_Factory implements Factory<GetContentDetailsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetContentDetailsUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetContentDetailsUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetContentDetailsUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetContentDetailsUseCase_Factory(contentRepositoryProvider);
  }

  public static GetContentDetailsUseCase newInstance(ContentRepository contentRepository) {
    return new GetContentDetailsUseCase(contentRepository);
  }
}
