package com.iptv.android.presentation;

import com.iptv.android.core.security.NetworkSecurityManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<NetworkSecurityManager> networkSecurityManagerProvider;

  public MainActivity_MembersInjector(
      Provider<NetworkSecurityManager> networkSecurityManagerProvider) {
    this.networkSecurityManagerProvider = networkSecurityManagerProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<NetworkSecurityManager> networkSecurityManagerProvider) {
    return new MainActivity_MembersInjector(networkSecurityManagerProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectNetworkSecurityManager(instance, networkSecurityManagerProvider.get());
  }

  @InjectedFieldSignature("com.iptv.android.presentation.MainActivity.networkSecurityManager")
  public static void injectNetworkSecurityManager(MainActivity instance,
      NetworkSecurityManager networkSecurityManager) {
    instance.networkSecurityManager = networkSecurityManager;
  }
}
