package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SetParentalPinUseCase_Factory implements Factory<SetParentalPinUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public SetParentalPinUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public SetParentalPinUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static SetParentalPinUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new SetParentalPinUseCase_Factory(userRepositoryProvider);
  }

  public static SetParentalPinUseCase newInstance(UserRepository userRepository) {
    return new SetParentalPinUseCase(userRepository);
  }
}
