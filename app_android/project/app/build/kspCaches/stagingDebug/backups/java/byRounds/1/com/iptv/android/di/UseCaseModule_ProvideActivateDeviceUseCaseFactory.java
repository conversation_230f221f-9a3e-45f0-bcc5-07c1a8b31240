package com.iptv.android.di;

import com.iptv.android.domain.repository.AuthRepository;
import com.iptv.android.domain.usecase.auth.ActivateDeviceUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideActivateDeviceUseCaseFactory implements Factory<ActivateDeviceUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public UseCaseModule_ProvideActivateDeviceUseCaseFactory(
      Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public ActivateDeviceUseCase get() {
    return provideActivateDeviceUseCase(authRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideActivateDeviceUseCaseFactory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new UseCaseModule_ProvideActivateDeviceUseCaseFactory(authRepositoryProvider);
  }

  public static ActivateDeviceUseCase provideActivateDeviceUseCase(AuthRepository authRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideActivateDeviceUseCase(authRepository));
  }
}
