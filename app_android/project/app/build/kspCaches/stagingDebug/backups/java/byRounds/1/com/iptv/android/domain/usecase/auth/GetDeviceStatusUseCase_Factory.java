package com.iptv.android.domain.usecase.auth;

import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetDeviceStatusUseCase_Factory implements Factory<GetDeviceStatusUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public GetDeviceStatusUseCase_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public GetDeviceStatusUseCase get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static GetDeviceStatusUseCase_Factory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new GetDeviceStatusUseCase_Factory(authRepositoryProvider);
  }

  public static GetDeviceStatusUseCase newInstance(AuthRepository authRepository) {
    return new GetDeviceStatusUseCase(authRepository);
  }
}
