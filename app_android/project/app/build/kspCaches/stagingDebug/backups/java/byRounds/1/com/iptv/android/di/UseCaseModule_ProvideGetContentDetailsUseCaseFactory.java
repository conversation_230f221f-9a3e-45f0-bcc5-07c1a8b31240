package com.iptv.android.di;

import com.iptv.android.domain.repository.ContentRepository;
import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideGetContentDetailsUseCaseFactory implements Factory<GetContentDetailsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public UseCaseModule_ProvideGetContentDetailsUseCaseFactory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetContentDetailsUseCase get() {
    return provideGetContentDetailsUseCase(contentRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideGetContentDetailsUseCaseFactory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new UseCaseModule_ProvideGetContentDetailsUseCaseFactory(contentRepositoryProvider);
  }

  public static GetContentDetailsUseCase provideGetContentDetailsUseCase(
      ContentRepository contentRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideGetContentDetailsUseCase(contentRepository));
  }
}
