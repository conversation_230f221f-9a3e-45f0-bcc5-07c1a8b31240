package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetContinueWatchingUseCase_Factory implements Factory<GetContinueWatchingUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetContinueWatchingUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetContinueWatchingUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetContinueWatchingUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetContinueWatchingUseCase_Factory(userRepositoryProvider);
  }

  public static GetContinueWatchingUseCase newInstance(UserRepository userRepository) {
    return new GetContinueWatchingUseCase(userRepository);
  }
}
