package com.iptv.android.di;

import com.iptv.android.data.remote.api.RefreshTokenService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("javax.inject.Named")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideRefreshTokenServiceFactory implements Factory<RefreshTokenService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideRefreshTokenServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public RefreshTokenService get() {
    return provideRefreshTokenService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideRefreshTokenServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideRefreshTokenServiceFactory(retrofitProvider);
  }

  public static RefreshTokenService provideRefreshTokenService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideRefreshTokenService(retrofit));
  }
}
