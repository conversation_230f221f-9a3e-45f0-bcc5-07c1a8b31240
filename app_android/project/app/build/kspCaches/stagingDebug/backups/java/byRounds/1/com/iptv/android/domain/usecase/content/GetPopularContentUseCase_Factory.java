package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetPopularContentUseCase_Factory implements Factory<GetPopularContentUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetPopularContentUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetPopularContentUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetPopularContentUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetPopularContentUseCase_Factory(contentRepositoryProvider);
  }

  public static GetPopularContentUseCase newInstance(ContentRepository contentRepository) {
    return new GetPopularContentUseCase(contentRepository);
  }
}
