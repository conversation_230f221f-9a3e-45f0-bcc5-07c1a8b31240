package com.iptv.android.presentation.screens.auth;

import com.iptv.android.core.utils.ErrorHandler;
import com.iptv.android.domain.usecase.auth.LoginUserUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserLoginViewModel_Factory implements Factory<UserLoginViewModel> {
  private final Provider<LoginUserUseCase> loginUserUseCaseProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public UserLoginViewModel_Factory(Provider<LoginUserUseCase> loginUserUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.loginUserUseCaseProvider = loginUserUseCaseProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public UserLoginViewModel get() {
    return newInstance(loginUserUseCaseProvider.get(), errorHandlerProvider.get());
  }

  public static UserLoginViewModel_Factory create(
      Provider<LoginUserUseCase> loginUserUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new UserLoginViewModel_Factory(loginUserUseCaseProvider, errorHandlerProvider);
  }

  public static UserLoginViewModel newInstance(LoginUserUseCase loginUserUseCase,
      ErrorHandler errorHandler) {
    return new UserLoginViewModel(loginUserUseCase, errorHandler);
  }
}
