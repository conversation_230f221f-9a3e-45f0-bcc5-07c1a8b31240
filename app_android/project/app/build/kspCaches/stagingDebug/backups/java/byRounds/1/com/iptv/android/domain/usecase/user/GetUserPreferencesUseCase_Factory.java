package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetUserPreferencesUseCase_Factory implements Factory<GetUserPreferencesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetUserPreferencesUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetUserPreferencesUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetUserPreferencesUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetUserPreferencesUseCase_Factory(userRepositoryProvider);
  }

  public static GetUserPreferencesUseCase newInstance(UserRepository userRepository) {
    return new GetUserPreferencesUseCase(userRepository);
  }
}
