package com.iptv.android.domain.usecase.auth;

import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LoginUserUseCase_Factory implements Factory<LoginUserUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public LoginUserUseCase_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public LoginUserUseCase get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static LoginUserUseCase_Factory create(Provider<AuthRepository> authRepositoryProvider) {
    return new LoginUserUseCase_Factory(authRepositoryProvider);
  }

  public static LoginUserUseCase newInstance(AuthRepository authRepository) {
    return new LoginUserUseCase(authRepository);
  }
}
