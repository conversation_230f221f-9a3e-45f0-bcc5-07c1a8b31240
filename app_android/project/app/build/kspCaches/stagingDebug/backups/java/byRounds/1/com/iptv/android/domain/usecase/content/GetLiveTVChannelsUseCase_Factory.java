package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetLiveTVChannelsUseCase_Factory implements Factory<GetLiveTVChannelsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetLiveTVChannelsUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetLiveTVChannelsUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetLiveTVChannelsUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetLiveTVChannelsUseCase_Factory(contentRepositoryProvider);
  }

  public static GetLiveTVChannelsUseCase newInstance(ContentRepository contentRepository) {
    return new GetLiveTVChannelsUseCase(contentRepository);
  }
}
