package com.iptv.android.di;

import android.content.Context;
import com.iptv.android.core.security.NetworkSecurityManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_ProvideNetworkSecurityManagerFactory implements Factory<NetworkSecurityManager> {
  private final Provider<Context> contextProvider;

  public SecurityModule_ProvideNetworkSecurityManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public NetworkSecurityManager get() {
    return provideNetworkSecurityManager(contextProvider.get());
  }

  public static SecurityModule_ProvideNetworkSecurityManagerFactory create(
      Provider<Context> contextProvider) {
    return new SecurityModule_ProvideNetworkSecurityManagerFactory(contextProvider);
  }

  public static NetworkSecurityManager provideNetworkSecurityManager(Context context) {
    return Preconditions.checkNotNullFromProvides(SecurityModule.INSTANCE.provideNetworkSecurityManager(context));
  }
}
