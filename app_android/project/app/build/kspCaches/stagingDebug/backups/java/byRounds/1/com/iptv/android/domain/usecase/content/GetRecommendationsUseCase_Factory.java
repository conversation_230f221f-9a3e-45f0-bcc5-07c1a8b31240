package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetRecommendationsUseCase_Factory implements Factory<GetRecommendationsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetRecommendationsUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetRecommendationsUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetRecommendationsUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetRecommendationsUseCase_Factory(contentRepositoryProvider);
  }

  public static GetRecommendationsUseCase newInstance(ContentRepository contentRepository) {
    return new GetRecommendationsUseCase(contentRepository);
  }
}
