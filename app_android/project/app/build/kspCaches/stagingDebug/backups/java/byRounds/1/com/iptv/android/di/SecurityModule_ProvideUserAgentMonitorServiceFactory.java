package com.iptv.android.di;

import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.core.security.UserAgentMonitorService;
import com.iptv.android.data.local.datastore.TokenManager;
import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_ProvideUserAgentMonitorServiceFactory implements Factory<UserAgentMonitorService> {
  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  private final Provider<TokenManager> tokenManagerProvider;

  public SecurityModule_ProvideUserAgentMonitorServiceFactory(
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<TokenManager> tokenManagerProvider) {
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
    this.authRepositoryProvider = authRepositoryProvider;
    this.tokenManagerProvider = tokenManagerProvider;
  }

  @Override
  public UserAgentMonitorService get() {
    return provideUserAgentMonitorService(dynamicUserAgentManagerProvider.get(), authRepositoryProvider.get(), tokenManagerProvider.get());
  }

  public static SecurityModule_ProvideUserAgentMonitorServiceFactory create(
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<TokenManager> tokenManagerProvider) {
    return new SecurityModule_ProvideUserAgentMonitorServiceFactory(dynamicUserAgentManagerProvider, authRepositoryProvider, tokenManagerProvider);
  }

  public static UserAgentMonitorService provideUserAgentMonitorService(
      DynamicUserAgentManager dynamicUserAgentManager, AuthRepository authRepository,
      TokenManager tokenManager) {
    return Preconditions.checkNotNullFromProvides(SecurityModule.INSTANCE.provideUserAgentMonitorService(dynamicUserAgentManager, authRepository, tokenManager));
  }
}
