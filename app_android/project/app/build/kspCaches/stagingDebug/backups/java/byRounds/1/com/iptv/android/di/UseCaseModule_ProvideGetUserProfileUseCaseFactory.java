package com.iptv.android.di;

import com.iptv.android.domain.repository.UserRepository;
import com.iptv.android.domain.usecase.user.GetUserProfileUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideGetUserProfileUseCaseFactory implements Factory<GetUserProfileUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UseCaseModule_ProvideGetUserProfileUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetUserProfileUseCase get() {
    return provideGetUserProfileUseCase(userRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideGetUserProfileUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UseCaseModule_ProvideGetUserProfileUseCaseFactory(userRepositoryProvider);
  }

  public static GetUserProfileUseCase provideGetUserProfileUseCase(UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideGetUserProfileUseCase(userRepository));
  }
}
