package com.iptv.android.presentation.screens.auth;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.auth.ActivateDeviceUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ActivationViewModel_Factory implements Factory<ActivationViewModel> {
  private final Provider<ActivateDeviceUseCase> activateDeviceUseCaseProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public ActivationViewModel_Factory(Provider<ActivateDeviceUseCase> activateDeviceUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.activateDeviceUseCaseProvider = activateDeviceUseCaseProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public ActivationViewModel get() {
    return newInstance(activateDeviceUseCaseProvider.get(), errorHandlerProvider.get());
  }

  public static ActivationViewModel_Factory create(
      Provider<ActivateDeviceUseCase> activateDeviceUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new ActivationViewModel_Factory(activateDeviceUseCaseProvider, errorHandlerProvider);
  }

  public static ActivationViewModel newInstance(ActivateDeviceUseCase activateDeviceUseCase,
      ErrorHandler errorHandler) {
    return new ActivationViewModel(activateDeviceUseCase, errorHandler);
  }
}
