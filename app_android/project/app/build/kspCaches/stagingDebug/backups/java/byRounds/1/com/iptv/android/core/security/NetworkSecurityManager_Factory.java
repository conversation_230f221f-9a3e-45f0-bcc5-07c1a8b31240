package com.iptv.android.core.security;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkSecurityManager_Factory implements Factory<NetworkSecurityManager> {
  private final Provider<Context> contextProvider;

  public NetworkSecurityManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public NetworkSecurityManager get() {
    return newInstance(contextProvider.get());
  }

  public static NetworkSecurityManager_Factory create(Provider<Context> contextProvider) {
    return new NetworkSecurityManager_Factory(contextProvider);
  }

  public static NetworkSecurityManager newInstance(Context context) {
    return new NetworkSecurityManager(context);
  }
}
