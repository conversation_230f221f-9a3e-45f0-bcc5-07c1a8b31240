package com.iptv.android.presentation.screens.home;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.content.GetCategoriesUseCase;
import com.iptv.android.domain.usecase.content.GetContentByCategoryUseCase;
import com.iptv.android.domain.usecase.content.GetContentSectionsUseCase;
import com.iptv.android.domain.usecase.content.GetFeaturedContentUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HomeViewModel_Factory implements Factory<HomeViewModel> {
  private final Provider<GetFeaturedContentUseCase> getFeaturedContentUseCaseProvider;

  private final Provider<GetContentSectionsUseCase> getContentSectionsUseCaseProvider;

  private final Provider<GetCategoriesUseCase> getCategoriesUseCaseProvider;

  private final Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public HomeViewModel_Factory(
      Provider<GetFeaturedContentUseCase> getFeaturedContentUseCaseProvider,
      Provider<GetContentSectionsUseCase> getContentSectionsUseCaseProvider,
      Provider<GetCategoriesUseCase> getCategoriesUseCaseProvider,
      Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.getFeaturedContentUseCaseProvider = getFeaturedContentUseCaseProvider;
    this.getContentSectionsUseCaseProvider = getContentSectionsUseCaseProvider;
    this.getCategoriesUseCaseProvider = getCategoriesUseCaseProvider;
    this.getContentByCategoryUseCaseProvider = getContentByCategoryUseCaseProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public HomeViewModel get() {
    return newInstance(getFeaturedContentUseCaseProvider.get(), getContentSectionsUseCaseProvider.get(), getCategoriesUseCaseProvider.get(), getContentByCategoryUseCaseProvider.get(), errorHandlerProvider.get());
  }

  public static HomeViewModel_Factory create(
      Provider<GetFeaturedContentUseCase> getFeaturedContentUseCaseProvider,
      Provider<GetContentSectionsUseCase> getContentSectionsUseCaseProvider,
      Provider<GetCategoriesUseCase> getCategoriesUseCaseProvider,
      Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new HomeViewModel_Factory(getFeaturedContentUseCaseProvider, getContentSectionsUseCaseProvider, getCategoriesUseCaseProvider, getContentByCategoryUseCaseProvider, errorHandlerProvider);
  }

  public static HomeViewModel newInstance(GetFeaturedContentUseCase getFeaturedContentUseCase,
      GetContentSectionsUseCase getContentSectionsUseCase,
      GetCategoriesUseCase getCategoriesUseCase,
      GetContentByCategoryUseCase getContentByCategoryUseCase, ErrorHandler errorHandler) {
    return new HomeViewModel(getFeaturedContentUseCase, getContentSectionsUseCase, getCategoriesUseCase, getContentByCategoryUseCase, errorHandler);
  }
}
