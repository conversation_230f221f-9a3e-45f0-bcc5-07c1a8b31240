package com.iptv.android.data.remote.interceptor;

import com.iptv.android.data.local.datastore.TokenManager;
import com.iptv.android.data.remote.api.RefreshTokenService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthInterceptor_Factory implements Factory<AuthInterceptor> {
  private final Provider<TokenManager> tokenManagerProvider;

  private final Provider<RefreshTokenService> refreshTokenServiceProvider;

  public AuthInterceptor_Factory(Provider<TokenManager> tokenManagerProvider,
      Provider<RefreshTokenService> refreshTokenServiceProvider) {
    this.tokenManagerProvider = tokenManagerProvider;
    this.refreshTokenServiceProvider = refreshTokenServiceProvider;
  }

  @Override
  public AuthInterceptor get() {
    return newInstance(tokenManagerProvider.get(), refreshTokenServiceProvider.get());
  }

  public static AuthInterceptor_Factory create(Provider<TokenManager> tokenManagerProvider,
      Provider<RefreshTokenService> refreshTokenServiceProvider) {
    return new AuthInterceptor_Factory(tokenManagerProvider, refreshTokenServiceProvider);
  }

  public static AuthInterceptor newInstance(TokenManager tokenManager,
      RefreshTokenService refreshTokenService) {
    return new AuthInterceptor(tokenManager, refreshTokenService);
  }
}
