package com.iptv.android.di;

import com.iptv.android.domain.repository.AuthRepository;
import com.iptv.android.domain.usecase.auth.RefreshTokenUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideRefreshTokenUseCaseFactory implements Factory<RefreshTokenUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public UseCaseModule_ProvideRefreshTokenUseCaseFactory(
      Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public RefreshTokenUseCase get() {
    return provideRefreshTokenUseCase(authRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideRefreshTokenUseCaseFactory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new UseCaseModule_ProvideRefreshTokenUseCaseFactory(authRepositoryProvider);
  }

  public static RefreshTokenUseCase provideRefreshTokenUseCase(AuthRepository authRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideRefreshTokenUseCase(authRepository));
  }
}
