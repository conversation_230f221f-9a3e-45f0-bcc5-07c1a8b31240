package com.iptv.android.presentation.screens.detail;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import com.iptv.android.domain.usecase.user.AddToFavoritesUseCase;
import com.iptv.android.domain.usecase.user.GetWatchProgressUseCase;
import com.iptv.android.domain.usecase.user.IsFavoriteUseCase;
import com.iptv.android.domain.usecase.user.RemoveFromFavoritesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ContentDetailViewModel_Factory implements Factory<ContentDetailViewModel> {
  private final Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider;

  private final Provider<IsFavoriteUseCase> isFavoriteUseCaseProvider;

  private final Provider<AddToFavoritesUseCase> addToFavoritesUseCaseProvider;

  private final Provider<RemoveFromFavoritesUseCase> removeFromFavoritesUseCaseProvider;

  private final Provider<GetWatchProgressUseCase> getWatchProgressUseCaseProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public ContentDetailViewModel_Factory(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<IsFavoriteUseCase> isFavoriteUseCaseProvider,
      Provider<AddToFavoritesUseCase> addToFavoritesUseCaseProvider,
      Provider<RemoveFromFavoritesUseCase> removeFromFavoritesUseCaseProvider,
      Provider<GetWatchProgressUseCase> getWatchProgressUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.getContentDetailsUseCaseProvider = getContentDetailsUseCaseProvider;
    this.isFavoriteUseCaseProvider = isFavoriteUseCaseProvider;
    this.addToFavoritesUseCaseProvider = addToFavoritesUseCaseProvider;
    this.removeFromFavoritesUseCaseProvider = removeFromFavoritesUseCaseProvider;
    this.getWatchProgressUseCaseProvider = getWatchProgressUseCaseProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public ContentDetailViewModel get() {
    return newInstance(getContentDetailsUseCaseProvider.get(), isFavoriteUseCaseProvider.get(), addToFavoritesUseCaseProvider.get(), removeFromFavoritesUseCaseProvider.get(), getWatchProgressUseCaseProvider.get(), errorHandlerProvider.get());
  }

  public static ContentDetailViewModel_Factory create(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<IsFavoriteUseCase> isFavoriteUseCaseProvider,
      Provider<AddToFavoritesUseCase> addToFavoritesUseCaseProvider,
      Provider<RemoveFromFavoritesUseCase> removeFromFavoritesUseCaseProvider,
      Provider<GetWatchProgressUseCase> getWatchProgressUseCaseProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new ContentDetailViewModel_Factory(getContentDetailsUseCaseProvider, isFavoriteUseCaseProvider, addToFavoritesUseCaseProvider, removeFromFavoritesUseCaseProvider, getWatchProgressUseCaseProvider, errorHandlerProvider);
  }

  public static ContentDetailViewModel newInstance(
      GetContentDetailsUseCase getContentDetailsUseCase, IsFavoriteUseCase isFavoriteUseCase,
      AddToFavoritesUseCase addToFavoritesUseCase,
      RemoveFromFavoritesUseCase removeFromFavoritesUseCase,
      GetWatchProgressUseCase getWatchProgressUseCase, ErrorHandler errorHandler) {
    return new ContentDetailViewModel(getContentDetailsUseCase, isFavoriteUseCase, addToFavoritesUseCase, removeFromFavoritesUseCase, getWatchProgressUseCase, errorHandler);
  }
}
