package com.iptv.android.core.media;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CodecCompatibilityHelper_Factory implements Factory<CodecCompatibilityHelper> {
  private final Provider<Context> contextProvider;

  public CodecCompatibilityHelper_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CodecCompatibilityHelper get() {
    return newInstance(contextProvider.get());
  }

  public static CodecCompatibilityHelper_Factory create(Provider<Context> contextProvider) {
    return new CodecCompatibilityHelper_Factory(contextProvider);
  }

  public static CodecCompatibilityHelper newInstance(Context context) {
    return new CodecCompatibilityHelper(context);
  }
}
