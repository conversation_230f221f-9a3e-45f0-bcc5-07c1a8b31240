package com.iptv.android.data.repository;

import com.iptv.android.data.local.dao.ContentDao;
import com.iptv.android.data.local.dao.UserDao;
import com.iptv.android.data.local.datastore.UserPreferencesManager;
import com.iptv.android.data.remote.api.IPTVApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepositoryImpl_Factory implements Factory<UserRepositoryImpl> {
  private final Provider<IPTVApiService> apiServiceProvider;

  private final Provider<UserDao> userDaoProvider;

  private final Provider<ContentDao> contentDaoProvider;

  private final Provider<UserPreferencesManager> userPreferencesManagerProvider;

  public UserRepositoryImpl_Factory(Provider<IPTVApiService> apiServiceProvider,
      Provider<UserDao> userDaoProvider, Provider<ContentDao> contentDaoProvider,
      Provider<UserPreferencesManager> userPreferencesManagerProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.userDaoProvider = userDaoProvider;
    this.contentDaoProvider = contentDaoProvider;
    this.userPreferencesManagerProvider = userPreferencesManagerProvider;
  }

  @Override
  public UserRepositoryImpl get() {
    return newInstance(apiServiceProvider.get(), userDaoProvider.get(), contentDaoProvider.get(), userPreferencesManagerProvider.get());
  }

  public static UserRepositoryImpl_Factory create(Provider<IPTVApiService> apiServiceProvider,
      Provider<UserDao> userDaoProvider, Provider<ContentDao> contentDaoProvider,
      Provider<UserPreferencesManager> userPreferencesManagerProvider) {
    return new UserRepositoryImpl_Factory(apiServiceProvider, userDaoProvider, contentDaoProvider, userPreferencesManagerProvider);
  }

  public static UserRepositoryImpl newInstance(IPTVApiService apiService, UserDao userDao,
      ContentDao contentDao, UserPreferencesManager userPreferencesManager) {
    return new UserRepositoryImpl(apiService, userDao, contentDao, userPreferencesManager);
  }
}
