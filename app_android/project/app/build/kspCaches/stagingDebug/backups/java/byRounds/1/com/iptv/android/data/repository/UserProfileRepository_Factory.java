package com.iptv.android.data.repository;

import com.iptv.android.data.remote.api.IPTVApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserProfileRepository_Factory implements Factory<UserProfileRepository> {
  private final Provider<IPTVApiService> apiServiceProvider;

  public UserProfileRepository_Factory(Provider<IPTVApiService> apiServiceProvider) {
    this.apiServiceProvider = apiServiceProvider;
  }

  @Override
  public UserProfileRepository get() {
    return newInstance(apiServiceProvider.get());
  }

  public static UserProfileRepository_Factory create(Provider<IPTVApiService> apiServiceProvider) {
    return new UserProfileRepository_Factory(apiServiceProvider);
  }

  public static UserProfileRepository newInstance(IPTVApiService apiService) {
    return new UserProfileRepository(apiService);
  }
}
