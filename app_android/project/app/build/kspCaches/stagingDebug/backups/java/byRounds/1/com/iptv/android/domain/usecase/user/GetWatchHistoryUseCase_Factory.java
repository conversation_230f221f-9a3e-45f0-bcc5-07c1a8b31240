package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetWatchHistoryUseCase_Factory implements Factory<GetWatchHistoryUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetWatchHistoryUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetWatchHistoryUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetWatchHistoryUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetWatchHistoryUseCase_Factory(userRepositoryProvider);
  }

  public static GetWatchHistoryUseCase newInstance(UserRepository userRepository) {
    return new GetWatchHistoryUseCase(userRepository);
  }
}
