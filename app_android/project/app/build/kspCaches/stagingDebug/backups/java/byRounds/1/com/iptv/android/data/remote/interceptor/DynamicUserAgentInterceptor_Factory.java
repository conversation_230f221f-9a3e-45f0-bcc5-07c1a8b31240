package com.iptv.android.data.remote.interceptor;

import com.iptv.android.core.security.DynamicUserAgentManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DynamicUserAgentInterceptor_Factory implements Factory<DynamicUserAgentInterceptor> {
  private final Provider<DynamicUserAgentManager> userAgentManagerProvider;

  public DynamicUserAgentInterceptor_Factory(
      Provider<DynamicUserAgentManager> userAgentManagerProvider) {
    this.userAgentManagerProvider = userAgentManagerProvider;
  }

  @Override
  public DynamicUserAgentInterceptor get() {
    return newInstance(userAgentManagerProvider.get());
  }

  public static DynamicUserAgentInterceptor_Factory create(
      Provider<DynamicUserAgentManager> userAgentManagerProvider) {
    return new DynamicUserAgentInterceptor_Factory(userAgentManagerProvider);
  }

  public static DynamicUserAgentInterceptor newInstance(DynamicUserAgentManager userAgentManager) {
    return new DynamicUserAgentInterceptor(userAgentManager);
  }
}
