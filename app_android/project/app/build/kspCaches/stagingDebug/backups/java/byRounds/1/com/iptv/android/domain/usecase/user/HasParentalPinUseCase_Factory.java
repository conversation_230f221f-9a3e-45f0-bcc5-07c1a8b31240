package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HasParentalPinUseCase_Factory implements Factory<HasParentalPinUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public HasParentalPinUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public HasParentalPinUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static HasParentalPinUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new HasParentalPinUseCase_Factory(userRepositoryProvider);
  }

  public static HasParentalPinUseCase newInstance(UserRepository userRepository) {
    return new HasParentalPinUseCase(userRepository);
  }
}
