package com.iptv.android.di;

import com.iptv.android.domain.repository.AuthRepository;
import com.iptv.android.domain.usecase.auth.GetAccessTokenUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideGetAccessTokenUseCaseFactory implements Factory<GetAccessTokenUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public UseCaseModule_ProvideGetAccessTokenUseCaseFactory(
      Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public GetAccessTokenUseCase get() {
    return provideGetAccessTokenUseCase(authRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideGetAccessTokenUseCaseFactory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new UseCaseModule_ProvideGetAccessTokenUseCaseFactory(authRepositoryProvider);
  }

  public static GetAccessTokenUseCase provideGetAccessTokenUseCase(AuthRepository authRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideGetAccessTokenUseCase(authRepository));
  }
}
