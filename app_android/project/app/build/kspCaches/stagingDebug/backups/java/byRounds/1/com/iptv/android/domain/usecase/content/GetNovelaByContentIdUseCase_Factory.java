package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetNovelaByContentIdUseCase_Factory implements Factory<GetNovelaByContentIdUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetNovelaByContentIdUseCase_Factory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetNovelaByContentIdUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetNovelaByContentIdUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetNovelaByContentIdUseCase_Factory(contentRepositoryProvider);
  }

  public static GetNovelaByContentIdUseCase newInstance(ContentRepository contentRepository) {
    return new GetNovelaByContentIdUseCase(contentRepository);
  }
}
