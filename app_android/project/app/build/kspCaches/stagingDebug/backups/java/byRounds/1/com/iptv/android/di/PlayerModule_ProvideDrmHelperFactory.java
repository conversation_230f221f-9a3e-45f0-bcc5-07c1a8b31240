package com.iptv.android.di;

import com.iptv.android.presentation.player.DrmHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerModule_ProvideDrmHelperFactory implements Factory<DrmHelper> {
  @Override
  public DrmHelper get() {
    return provideDrmHelper();
  }

  public static PlayerModule_ProvideDrmHelperFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DrmHelper provideDrmHelper() {
    return Preconditions.checkNotNullFromProvides(PlayerModule.INSTANCE.provideDrmHelper());
  }

  private static final class InstanceHolder {
    private static final PlayerModule_ProvideDrmHelperFactory INSTANCE = new PlayerModule_ProvideDrmHelperFactory();
  }
}
