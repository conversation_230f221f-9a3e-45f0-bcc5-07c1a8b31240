package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VerifyParentalPinUseCase_Factory implements Factory<VerifyParentalPinUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public VerifyParentalPinUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public VerifyParentalPinUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static VerifyParentalPinUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new VerifyParentalPinUseCase_Factory(userRepositoryProvider);
  }

  public static VerifyParentalPinUseCase newInstance(UserRepository userRepository) {
    return new VerifyParentalPinUseCase(userRepository);
  }
}
