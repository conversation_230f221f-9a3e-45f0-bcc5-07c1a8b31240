package com.iptv.android.presentation.screens.player;

import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import com.iptv.android.domain.usecase.series.GetEpisodeStreamUseCase;
import com.iptv.android.domain.usecase.series.GetSeriesDetailsByContentIdUseCase;
import com.iptv.android.presentation.player.ExoPlayerManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EpisodicPlayerViewModel_Factory implements Factory<EpisodicPlayerViewModel> {
  private final Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider;

  private final Provider<GetSeriesDetailsByContentIdUseCase> getSeriesDetailsUseCaseProvider;

  private final Provider<GetEpisodeStreamUseCase> getEpisodeStreamUseCaseProvider;

  private final Provider<ExoPlayerManager> exoPlayerManagerProvider;

  public EpisodicPlayerViewModel_Factory(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<GetSeriesDetailsByContentIdUseCase> getSeriesDetailsUseCaseProvider,
      Provider<GetEpisodeStreamUseCase> getEpisodeStreamUseCaseProvider,
      Provider<ExoPlayerManager> exoPlayerManagerProvider) {
    this.getContentDetailsUseCaseProvider = getContentDetailsUseCaseProvider;
    this.getSeriesDetailsUseCaseProvider = getSeriesDetailsUseCaseProvider;
    this.getEpisodeStreamUseCaseProvider = getEpisodeStreamUseCaseProvider;
    this.exoPlayerManagerProvider = exoPlayerManagerProvider;
  }

  @Override
  public EpisodicPlayerViewModel get() {
    return newInstance(getContentDetailsUseCaseProvider.get(), getSeriesDetailsUseCaseProvider.get(), getEpisodeStreamUseCaseProvider.get(), exoPlayerManagerProvider.get());
  }

  public static EpisodicPlayerViewModel_Factory create(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<GetSeriesDetailsByContentIdUseCase> getSeriesDetailsUseCaseProvider,
      Provider<GetEpisodeStreamUseCase> getEpisodeStreamUseCaseProvider,
      Provider<ExoPlayerManager> exoPlayerManagerProvider) {
    return new EpisodicPlayerViewModel_Factory(getContentDetailsUseCaseProvider, getSeriesDetailsUseCaseProvider, getEpisodeStreamUseCaseProvider, exoPlayerManagerProvider);
  }

  public static EpisodicPlayerViewModel newInstance(
      GetContentDetailsUseCase getContentDetailsUseCase,
      GetSeriesDetailsByContentIdUseCase getSeriesDetailsUseCase,
      GetEpisodeStreamUseCase getEpisodeStreamUseCase, ExoPlayerManager exoPlayerManager) {
    return new EpisodicPlayerViewModel(getContentDetailsUseCase, getSeriesDetailsUseCase, getEpisodeStreamUseCase, exoPlayerManager);
  }
}
