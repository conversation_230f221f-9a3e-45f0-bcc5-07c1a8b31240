package com.iptv.android.presentation.screens.settings;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.data.repository.UserProfileRepository;
import com.iptv.android.domain.usecase.auth.LogoutUseCase;
import com.iptv.android.domain.usecase.user.GetUserPreferencesUseCase;
import com.iptv.android.domain.usecase.user.UpdateUserPreferencesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<GetUserPreferencesUseCase> getUserPreferencesUseCaseProvider;

  private final Provider<UpdateUserPreferencesUseCase> updateUserPreferencesUseCaseProvider;

  private final Provider<LogoutUseCase> logoutUseCaseProvider;

  private final Provider<UserProfileRepository> userProfileRepositoryProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public SettingsViewModel_Factory(
      Provider<GetUserPreferencesUseCase> getUserPreferencesUseCaseProvider,
      Provider<UpdateUserPreferencesUseCase> updateUserPreferencesUseCaseProvider,
      Provider<LogoutUseCase> logoutUseCaseProvider,
      Provider<UserProfileRepository> userProfileRepositoryProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.getUserPreferencesUseCaseProvider = getUserPreferencesUseCaseProvider;
    this.updateUserPreferencesUseCaseProvider = updateUserPreferencesUseCaseProvider;
    this.logoutUseCaseProvider = logoutUseCaseProvider;
    this.userProfileRepositoryProvider = userProfileRepositoryProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public SettingsViewModel get() {
    return newInstance(getUserPreferencesUseCaseProvider.get(), updateUserPreferencesUseCaseProvider.get(), logoutUseCaseProvider.get(), userProfileRepositoryProvider.get(), errorHandlerProvider.get());
  }

  public static SettingsViewModel_Factory create(
      Provider<GetUserPreferencesUseCase> getUserPreferencesUseCaseProvider,
      Provider<UpdateUserPreferencesUseCase> updateUserPreferencesUseCaseProvider,
      Provider<LogoutUseCase> logoutUseCaseProvider,
      Provider<UserProfileRepository> userProfileRepositoryProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new SettingsViewModel_Factory(getUserPreferencesUseCaseProvider, updateUserPreferencesUseCaseProvider, logoutUseCaseProvider, userProfileRepositoryProvider, errorHandlerProvider);
  }

  public static SettingsViewModel newInstance(GetUserPreferencesUseCase getUserPreferencesUseCase,
      UpdateUserPreferencesUseCase updateUserPreferencesUseCase, LogoutUseCase logoutUseCase,
      UserProfileRepository userProfileRepository, ErrorHandler errorHandler) {
    return new SettingsViewModel(getUserPreferencesUseCase, updateUserPreferencesUseCase, logoutUseCase, userProfileRepository, errorHandler);
  }
}
