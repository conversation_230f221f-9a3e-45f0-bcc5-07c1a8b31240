package com.iptv.android.di;

import com.iptv.android.domain.repository.UserRepository;
import com.iptv.android.domain.usecase.user.UpdateDeviceNameUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideUpdateDeviceNameUseCaseFactory implements Factory<UpdateDeviceNameUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UseCaseModule_ProvideUpdateDeviceNameUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateDeviceNameUseCase get() {
    return provideUpdateDeviceNameUseCase(userRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideUpdateDeviceNameUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UseCaseModule_ProvideUpdateDeviceNameUseCaseFactory(userRepositoryProvider);
  }

  public static UpdateDeviceNameUseCase provideUpdateDeviceNameUseCase(
      UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideUpdateDeviceNameUseCase(userRepository));
  }
}
