package com.iptv.android.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ChannelsRepositoryImpl_Factory implements Factory<ChannelsRepositoryImpl> {
  @Override
  public ChannelsRepositoryImpl get() {
    return newInstance();
  }

  public static ChannelsRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ChannelsRepositoryImpl newInstance() {
    return new ChannelsRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final ChannelsRepositoryImpl_Factory INSTANCE = new ChannelsRepositoryImpl_Factory();
  }
}
