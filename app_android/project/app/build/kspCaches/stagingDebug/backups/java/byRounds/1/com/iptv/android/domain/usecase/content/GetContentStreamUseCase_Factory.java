package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetContentStreamUseCase_Factory implements Factory<GetContentStreamUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetContentStreamUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetContentStreamUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetContentStreamUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetContentStreamUseCase_Factory(contentRepositoryProvider);
  }

  public static GetContentStreamUseCase newInstance(ContentRepository contentRepository) {
    return new GetContentStreamUseCase(contentRepository);
  }
}
