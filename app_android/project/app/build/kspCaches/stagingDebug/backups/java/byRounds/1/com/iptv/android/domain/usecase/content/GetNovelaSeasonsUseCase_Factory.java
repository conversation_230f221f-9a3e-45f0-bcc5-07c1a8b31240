package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetNovelaSeasonsUseCase_Factory implements Factory<GetNovelaSeasonsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetNovelaSeasonsUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetNovelaSeasonsUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetNovelaSeasonsUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetNovelaSeasonsUseCase_Factory(contentRepositoryProvider);
  }

  public static GetNovelaSeasonsUseCase newInstance(ContentRepository contentRepository) {
    return new GetNovelaSeasonsUseCase(contentRepository);
  }
}
