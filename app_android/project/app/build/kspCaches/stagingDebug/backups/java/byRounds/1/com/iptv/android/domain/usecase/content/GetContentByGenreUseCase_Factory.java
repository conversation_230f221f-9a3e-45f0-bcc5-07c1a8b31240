package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetContentByGenreUseCase_Factory implements Factory<GetContentByGenreUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetContentByGenreUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetContentByGenreUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetContentByGenreUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetContentByGenreUseCase_Factory(contentRepositoryProvider);
  }

  public static GetContentByGenreUseCase newInstance(ContentRepository contentRepository) {
    return new GetContentByGenreUseCase(contentRepository);
  }
}
