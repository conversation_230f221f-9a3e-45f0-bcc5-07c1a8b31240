package com.iptv.android.domain.usecase.series;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetEpisodeStreamUseCase_Factory implements Factory<GetEpisodeStreamUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetEpisodeStreamUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetEpisodeStreamUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetEpisodeStreamUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetEpisodeStreamUseCase_Factory(contentRepositoryProvider);
  }

  public static GetEpisodeStreamUseCase newInstance(ContentRepository contentRepository) {
    return new GetEpisodeStreamUseCase(contentRepository);
  }
}
