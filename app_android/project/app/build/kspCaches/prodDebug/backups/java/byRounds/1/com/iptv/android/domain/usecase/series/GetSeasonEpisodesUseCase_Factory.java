package com.iptv.android.domain.usecase.series;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSeasonEpisodesUseCase_Factory implements Factory<GetSeasonEpisodesUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetSeasonEpisodesUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetSeasonEpisodesUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetSeasonEpisodesUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetSeasonEpisodesUseCase_Factory(contentRepositoryProvider);
  }

  public static GetSeasonEpisodesUseCase newInstance(ContentRepository contentRepository) {
    return new GetSeasonEpisodesUseCase(contentRepository);
  }
}
