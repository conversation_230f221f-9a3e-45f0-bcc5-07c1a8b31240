package com.iptv.android.domain.usecase.auth;

import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ActivateDeviceUseCase_Factory implements Factory<ActivateDeviceUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public ActivateDeviceUseCase_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public ActivateDeviceUseCase get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static ActivateDeviceUseCase_Factory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new ActivateDeviceUseCase_Factory(authRepositoryProvider);
  }

  public static ActivateDeviceUseCase newInstance(AuthRepository authRepository) {
    return new ActivateDeviceUseCase(authRepository);
  }
}
