package com.iptv.android.core.security;

import com.iptv.android.data.local.datastore.TokenManager;
import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserAgentMonitorService_Factory implements Factory<UserAgentMonitorService> {
  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  private final Provider<TokenManager> tokenManagerProvider;

  public UserAgentMonitorService_Factory(
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<TokenManager> tokenManagerProvider) {
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
    this.authRepositoryProvider = authRepositoryProvider;
    this.tokenManagerProvider = tokenManagerProvider;
  }

  @Override
  public UserAgentMonitorService get() {
    return newInstance(dynamicUserAgentManagerProvider.get(), authRepositoryProvider.get(), tokenManagerProvider.get());
  }

  public static UserAgentMonitorService_Factory create(
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<TokenManager> tokenManagerProvider) {
    return new UserAgentMonitorService_Factory(dynamicUserAgentManagerProvider, authRepositoryProvider, tokenManagerProvider);
  }

  public static UserAgentMonitorService newInstance(DynamicUserAgentManager dynamicUserAgentManager,
      AuthRepository authRepository, TokenManager tokenManager) {
    return new UserAgentMonitorService(dynamicUserAgentManager, authRepository, tokenManager);
  }
}
