package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateDeviceNameUseCase_Factory implements Factory<UpdateDeviceNameUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UpdateDeviceNameUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateDeviceNameUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static UpdateDeviceNameUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UpdateDeviceNameUseCase_Factory(userRepositoryProvider);
  }

  public static UpdateDeviceNameUseCase newInstance(UserRepository userRepository) {
    return new UpdateDeviceNameUseCase(userRepository);
  }
}
