package com.iptv.android.core.security;

import com.iptv.android.core.network.BackendHandler;
import com.iptv.android.data.local.datastore.TokenManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserAgentRenewalService_Factory implements Factory<UserAgentRenewalService> {
  private final Provider<TokenManager> tokenManagerProvider;

  private final Provider<BackendHandler> backendHandlerProvider;

  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  public UserAgentRenewalService_Factory(Provider<TokenManager> tokenManagerProvider,
      Provider<BackendHandler> backendHandlerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider) {
    this.tokenManagerProvider = tokenManagerProvider;
    this.backendHandlerProvider = backendHandlerProvider;
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
  }

  @Override
  public UserAgentRenewalService get() {
    return newInstance(tokenManagerProvider.get(), backendHandlerProvider.get(), dynamicUserAgentManagerProvider.get());
  }

  public static UserAgentRenewalService_Factory create(Provider<TokenManager> tokenManagerProvider,
      Provider<BackendHandler> backendHandlerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider) {
    return new UserAgentRenewalService_Factory(tokenManagerProvider, backendHandlerProvider, dynamicUserAgentManagerProvider);
  }

  public static UserAgentRenewalService newInstance(TokenManager tokenManager,
      BackendHandler backendHandler, DynamicUserAgentManager dynamicUserAgentManager) {
    return new UserAgentRenewalService(tokenManager, backendHandler, dynamicUserAgentManager);
  }
}
