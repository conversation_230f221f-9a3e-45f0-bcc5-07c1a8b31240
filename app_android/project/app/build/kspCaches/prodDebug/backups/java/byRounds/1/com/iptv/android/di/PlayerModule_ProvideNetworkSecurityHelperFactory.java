package com.iptv.android.di;

import android.content.Context;
import com.iptv.android.core.network.NetworkSecurityHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerModule_ProvideNetworkSecurityHelperFactory implements Factory<NetworkSecurityHelper> {
  private final Provider<Context> contextProvider;

  public PlayerModule_ProvideNetworkSecurityHelperFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public NetworkSecurityHelper get() {
    return provideNetworkSecurityHelper(contextProvider.get());
  }

  public static PlayerModule_ProvideNetworkSecurityHelperFactory create(
      Provider<Context> contextProvider) {
    return new PlayerModule_ProvideNetworkSecurityHelperFactory(contextProvider);
  }

  public static NetworkSecurityHelper provideNetworkSecurityHelper(Context context) {
    return Preconditions.checkNotNullFromProvides(PlayerModule.INSTANCE.provideNetworkSecurityHelper(context));
  }
}
