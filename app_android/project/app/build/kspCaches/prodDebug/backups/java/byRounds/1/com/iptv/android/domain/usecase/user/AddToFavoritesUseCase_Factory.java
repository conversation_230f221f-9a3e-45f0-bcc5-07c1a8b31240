package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddToFavoritesUseCase_Factory implements Factory<AddToFavoritesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public AddToFavoritesUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public AddToFavoritesUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static AddToFavoritesUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new AddToFavoritesUseCase_Factory(userRepositoryProvider);
  }

  public static AddToFavoritesUseCase newInstance(UserRepository userRepository) {
    return new AddToFavoritesUseCase(userRepository);
  }
}
