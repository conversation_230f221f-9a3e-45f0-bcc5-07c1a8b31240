package com.iptv.android.di;

import com.iptv.android.domain.repository.ContentRepository;
import com.iptv.android.domain.usecase.content.GetContentByGenreUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideGetContentByGenreUseCaseFactory implements Factory<GetContentByGenreUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public UseCaseModule_ProvideGetContentByGenreUseCaseFactory(
      Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetContentByGenreUseCase get() {
    return provideGetContentByGenreUseCase(contentRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideGetContentByGenreUseCaseFactory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new UseCaseModule_ProvideGetContentByGenreUseCaseFactory(contentRepositoryProvider);
  }

  public static GetContentByGenreUseCase provideGetContentByGenreUseCase(
      ContentRepository contentRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideGetContentByGenreUseCase(contentRepository));
  }
}
