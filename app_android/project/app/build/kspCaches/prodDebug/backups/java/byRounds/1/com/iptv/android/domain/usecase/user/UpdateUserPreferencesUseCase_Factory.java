package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateUserPreferencesUseCase_Factory implements Factory<UpdateUserPreferencesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UpdateUserPreferencesUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateUserPreferencesUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static UpdateUserPreferencesUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UpdateUserPreferencesUseCase_Factory(userRepositoryProvider);
  }

  public static UpdateUserPreferencesUseCase newInstance(UserRepository userRepository) {
    return new UpdateUserPreferencesUseCase(userRepository);
  }
}
