package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateWatchProgressUseCase_Factory implements Factory<UpdateWatchProgressUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UpdateWatchProgressUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateWatchProgressUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static UpdateWatchProgressUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UpdateWatchProgressUseCase_Factory(userRepositoryProvider);
  }

  public static UpdateWatchProgressUseCase newInstance(UserRepository userRepository) {
    return new UpdateWatchProgressUseCase(userRepository);
  }
}
