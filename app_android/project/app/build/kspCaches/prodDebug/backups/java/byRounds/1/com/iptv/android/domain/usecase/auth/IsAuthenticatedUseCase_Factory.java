package com.iptv.android.domain.usecase.auth;

import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class IsAuthenticatedUseCase_Factory implements Factory<IsAuthenticatedUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public IsAuthenticatedUseCase_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public IsAuthenticatedUseCase get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static IsAuthenticatedUseCase_Factory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new IsAuthenticatedUseCase_Factory(authRepositoryProvider);
  }

  public static IsAuthenticatedUseCase newInstance(AuthRepository authRepository) {
    return new IsAuthenticatedUseCase(authRepository);
  }
}
