package com.iptv.android.domain.usecase.device;

import com.iptv.android.core.device.DeviceIdentifierManager;
import com.iptv.android.domain.repository.AuthRepository;
import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DeviceLimitHandler_Factory implements Factory<DeviceLimitHandler> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  private final Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider;

  public DeviceLimitHandler_Factory(Provider<UserRepository> userRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.authRepositoryProvider = authRepositoryProvider;
    this.deviceIdentifierManagerProvider = deviceIdentifierManagerProvider;
  }

  @Override
  public DeviceLimitHandler get() {
    return newInstance(userRepositoryProvider.get(), authRepositoryProvider.get(), deviceIdentifierManagerProvider.get());
  }

  public static DeviceLimitHandler_Factory create(Provider<UserRepository> userRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider) {
    return new DeviceLimitHandler_Factory(userRepositoryProvider, authRepositoryProvider, deviceIdentifierManagerProvider);
  }

  public static DeviceLimitHandler newInstance(UserRepository userRepository,
      AuthRepository authRepository, DeviceIdentifierManager deviceIdentifierManager) {
    return new DeviceLimitHandler(userRepository, authRepository, deviceIdentifierManager);
  }
}
