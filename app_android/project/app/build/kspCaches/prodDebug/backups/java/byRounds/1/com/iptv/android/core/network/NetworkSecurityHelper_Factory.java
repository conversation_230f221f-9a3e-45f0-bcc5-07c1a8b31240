package com.iptv.android.core.network;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkSecurityHelper_Factory implements Factory<NetworkSecurityHelper> {
  private final Provider<Context> contextProvider;

  public NetworkSecurityHelper_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public NetworkSecurityHelper get() {
    return newInstance(contextProvider.get());
  }

  public static NetworkSecurityHelper_Factory create(Provider<Context> contextProvider) {
    return new NetworkSecurityHelper_Factory(contextProvider);
  }

  public static NetworkSecurityHelper newInstance(Context context) {
    return new NetworkSecurityHelper(context);
  }
}
