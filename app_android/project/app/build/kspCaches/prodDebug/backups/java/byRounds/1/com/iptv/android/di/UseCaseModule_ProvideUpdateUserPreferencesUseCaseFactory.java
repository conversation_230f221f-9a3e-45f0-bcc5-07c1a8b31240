package com.iptv.android.di;

import com.iptv.android.domain.repository.UserRepository;
import com.iptv.android.domain.usecase.user.UpdateUserPreferencesUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideUpdateUserPreferencesUseCaseFactory implements Factory<UpdateUserPreferencesUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UseCaseModule_ProvideUpdateUserPreferencesUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UpdateUserPreferencesUseCase get() {
    return provideUpdateUserPreferencesUseCase(userRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideUpdateUserPreferencesUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UseCaseModule_ProvideUpdateUserPreferencesUseCaseFactory(userRepositoryProvider);
  }

  public static UpdateUserPreferencesUseCase provideUpdateUserPreferencesUseCase(
      UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideUpdateUserPreferencesUseCase(userRepository));
  }
}
