package com.iptv.android.di;

import com.iptv.android.core.network.BackendHandler;
import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.core.security.UserAgentRenewalService;
import com.iptv.android.data.local.datastore.TokenManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_ProvideUserAgentRenewalServiceFactory implements Factory<UserAgentRenewalService> {
  private final Provider<TokenManager> tokenManagerProvider;

  private final Provider<BackendHandler> backendHandlerProvider;

  private final Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider;

  public SecurityModule_ProvideUserAgentRenewalServiceFactory(
      Provider<TokenManager> tokenManagerProvider, Provider<BackendHandler> backendHandlerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider) {
    this.tokenManagerProvider = tokenManagerProvider;
    this.backendHandlerProvider = backendHandlerProvider;
    this.dynamicUserAgentManagerProvider = dynamicUserAgentManagerProvider;
  }

  @Override
  public UserAgentRenewalService get() {
    return provideUserAgentRenewalService(tokenManagerProvider.get(), backendHandlerProvider.get(), dynamicUserAgentManagerProvider.get());
  }

  public static SecurityModule_ProvideUserAgentRenewalServiceFactory create(
      Provider<TokenManager> tokenManagerProvider, Provider<BackendHandler> backendHandlerProvider,
      Provider<DynamicUserAgentManager> dynamicUserAgentManagerProvider) {
    return new SecurityModule_ProvideUserAgentRenewalServiceFactory(tokenManagerProvider, backendHandlerProvider, dynamicUserAgentManagerProvider);
  }

  public static UserAgentRenewalService provideUserAgentRenewalService(TokenManager tokenManager,
      BackendHandler backendHandler, DynamicUserAgentManager dynamicUserAgentManager) {
    return Preconditions.checkNotNullFromProvides(SecurityModule.INSTANCE.provideUserAgentRenewalService(tokenManager, backendHandler, dynamicUserAgentManager));
  }
}
