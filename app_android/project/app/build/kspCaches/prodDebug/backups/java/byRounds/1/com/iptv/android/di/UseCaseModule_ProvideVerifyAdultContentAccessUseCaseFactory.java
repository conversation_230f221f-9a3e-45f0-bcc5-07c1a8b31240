package com.iptv.android.di;

import com.iptv.android.domain.repository.UserRepository;
import com.iptv.android.domain.usecase.user.VerifyAdultContentAccessUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideVerifyAdultContentAccessUseCaseFactory implements Factory<VerifyAdultContentAccessUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UseCaseModule_ProvideVerifyAdultContentAccessUseCaseFactory(
      Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public VerifyAdultContentAccessUseCase get() {
    return provideVerifyAdultContentAccessUseCase(userRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideVerifyAdultContentAccessUseCaseFactory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UseCaseModule_ProvideVerifyAdultContentAccessUseCaseFactory(userRepositoryProvider);
  }

  public static VerifyAdultContentAccessUseCase provideVerifyAdultContentAccessUseCase(
      UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideVerifyAdultContentAccessUseCase(userRepository));
  }
}
