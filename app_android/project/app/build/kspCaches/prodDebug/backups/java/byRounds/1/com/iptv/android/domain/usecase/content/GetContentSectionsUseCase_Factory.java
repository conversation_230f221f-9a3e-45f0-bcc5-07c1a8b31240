package com.iptv.android.domain.usecase.content;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetContentSectionsUseCase_Factory implements Factory<GetContentSectionsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetContentSectionsUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetContentSectionsUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetContentSectionsUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetContentSectionsUseCase_Factory(contentRepositoryProvider);
  }

  public static GetContentSectionsUseCase newInstance(ContentRepository contentRepository) {
    return new GetContentSectionsUseCase(contentRepository);
  }
}
