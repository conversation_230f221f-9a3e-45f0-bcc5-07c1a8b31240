package com.iptv.android.di;

import com.iptv.android.domain.repository.AuthRepository;
import com.iptv.android.domain.usecase.auth.IsAuthenticatedUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideIsAuthenticatedUseCaseFactory implements Factory<IsAuthenticatedUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public UseCaseModule_ProvideIsAuthenticatedUseCaseFactory(
      Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public IsAuthenticatedUseCase get() {
    return provideIsAuthenticatedUseCase(authRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideIsAuthenticatedUseCaseFactory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new UseCaseModule_ProvideIsAuthenticatedUseCaseFactory(authRepositoryProvider);
  }

  public static IsAuthenticatedUseCase provideIsAuthenticatedUseCase(
      AuthRepository authRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideIsAuthenticatedUseCase(authRepository));
  }
}
