package com.iptv.android.data.repository;

import com.iptv.android.data.local.dao.ContentDao;
import com.iptv.android.data.remote.api.IPTVApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ContentRepositoryImpl_Factory implements Factory<ContentRepositoryImpl> {
  private final Provider<IPTVApiService> apiServiceProvider;

  private final Provider<ContentDao> contentDaoProvider;

  public ContentRepositoryImpl_Factory(Provider<IPTVApiService> apiServiceProvider,
      Provider<ContentDao> contentDaoProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.contentDaoProvider = contentDaoProvider;
  }

  @Override
  public ContentRepositoryImpl get() {
    return newInstance(apiServiceProvider.get(), contentDaoProvider.get());
  }

  public static ContentRepositoryImpl_Factory create(Provider<IPTVApiService> apiServiceProvider,
      Provider<ContentDao> contentDaoProvider) {
    return new ContentRepositoryImpl_Factory(apiServiceProvider, contentDaoProvider);
  }

  public static ContentRepositoryImpl newInstance(IPTVApiService apiService,
      ContentDao contentDao) {
    return new ContentRepositoryImpl(apiService, contentDao);
  }
}
