package com.iptv.android.core.device;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DeviceIdentifierManager_Factory implements Factory<DeviceIdentifierManager> {
  private final Provider<Context> contextProvider;

  public DeviceIdentifierManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DeviceIdentifierManager get() {
    return newInstance(contextProvider.get());
  }

  public static DeviceIdentifierManager_Factory create(Provider<Context> contextProvider) {
    return new DeviceIdentifierManager_Factory(contextProvider);
  }

  public static DeviceIdentifierManager newInstance(Context context) {
    return new DeviceIdentifierManager(context);
  }
}
