package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ClearWatchHistoryUseCase_Factory implements Factory<ClearWatchHistoryUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public ClearWatchHistoryUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public ClearWatchHistoryUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static ClearWatchHistoryUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new ClearWatchHistoryUseCase_Factory(userRepositoryProvider);
  }

  public static ClearWatchHistoryUseCase newInstance(UserRepository userRepository) {
    return new ClearWatchHistoryUseCase(userRepository);
  }
}
