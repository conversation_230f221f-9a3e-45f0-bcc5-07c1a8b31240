package com.iptv.android.core.network;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BackendHandler_Factory implements Factory<BackendHandler> {
  private final Provider<Context> contextProvider;

  public BackendHandler_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public BackendHandler get() {
    return newInstance(contextProvider.get());
  }

  public static BackendHandler_Factory create(Provider<Context> contextProvider) {
    return new BackendHandler_Factory(contextProvider);
  }

  public static BackendHandler newInstance(Context context) {
    return new BackendHandler(context);
  }
}
