package com.iptv.android.presentation.screens.player;

import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.domain.usecase.content.GetContentByCategoryUseCase;
import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import com.iptv.android.domain.usecase.content.GetContentStreamUseCase;
import com.iptv.android.domain.usecase.content.GetLiveTVChannelsUseCase;
import com.iptv.android.domain.usecase.series.GetEpisodeStreamUseCase;
import com.iptv.android.domain.usecase.user.UpdateWatchProgressUseCase;
import com.iptv.android.presentation.player.ExoPlayerManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerViewModel_Factory implements Factory<PlayerViewModel> {
  private final Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider;

  private final Provider<GetContentStreamUseCase> getContentStreamUseCaseProvider;

  private final Provider<GetEpisodeStreamUseCase> getEpisodeStreamUseCaseProvider;

  private final Provider<UpdateWatchProgressUseCase> updateWatchProgressUseCaseProvider;

  private final Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider;

  private final Provider<GetLiveTVChannelsUseCase> getLiveTVChannelsUseCaseProvider;

  private final Provider<ExoPlayerManager> exoPlayerManagerProvider;

  private final Provider<ErrorHandler> errorHandlerProvider;

  public PlayerViewModel_Factory(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<GetContentStreamUseCase> getContentStreamUseCaseProvider,
      Provider<GetEpisodeStreamUseCase> getEpisodeStreamUseCaseProvider,
      Provider<UpdateWatchProgressUseCase> updateWatchProgressUseCaseProvider,
      Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider,
      Provider<GetLiveTVChannelsUseCase> getLiveTVChannelsUseCaseProvider,
      Provider<ExoPlayerManager> exoPlayerManagerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    this.getContentDetailsUseCaseProvider = getContentDetailsUseCaseProvider;
    this.getContentStreamUseCaseProvider = getContentStreamUseCaseProvider;
    this.getEpisodeStreamUseCaseProvider = getEpisodeStreamUseCaseProvider;
    this.updateWatchProgressUseCaseProvider = updateWatchProgressUseCaseProvider;
    this.getContentByCategoryUseCaseProvider = getContentByCategoryUseCaseProvider;
    this.getLiveTVChannelsUseCaseProvider = getLiveTVChannelsUseCaseProvider;
    this.exoPlayerManagerProvider = exoPlayerManagerProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public PlayerViewModel get() {
    return newInstance(getContentDetailsUseCaseProvider.get(), getContentStreamUseCaseProvider.get(), getEpisodeStreamUseCaseProvider.get(), updateWatchProgressUseCaseProvider.get(), getContentByCategoryUseCaseProvider.get(), getLiveTVChannelsUseCaseProvider.get(), exoPlayerManagerProvider.get(), errorHandlerProvider.get());
  }

  public static PlayerViewModel_Factory create(
      Provider<GetContentDetailsUseCase> getContentDetailsUseCaseProvider,
      Provider<GetContentStreamUseCase> getContentStreamUseCaseProvider,
      Provider<GetEpisodeStreamUseCase> getEpisodeStreamUseCaseProvider,
      Provider<UpdateWatchProgressUseCase> updateWatchProgressUseCaseProvider,
      Provider<GetContentByCategoryUseCase> getContentByCategoryUseCaseProvider,
      Provider<GetLiveTVChannelsUseCase> getLiveTVChannelsUseCaseProvider,
      Provider<ExoPlayerManager> exoPlayerManagerProvider,
      Provider<ErrorHandler> errorHandlerProvider) {
    return new PlayerViewModel_Factory(getContentDetailsUseCaseProvider, getContentStreamUseCaseProvider, getEpisodeStreamUseCaseProvider, updateWatchProgressUseCaseProvider, getContentByCategoryUseCaseProvider, getLiveTVChannelsUseCaseProvider, exoPlayerManagerProvider, errorHandlerProvider);
  }

  public static PlayerViewModel newInstance(GetContentDetailsUseCase getContentDetailsUseCase,
      GetContentStreamUseCase getContentStreamUseCase,
      GetEpisodeStreamUseCase getEpisodeStreamUseCase,
      UpdateWatchProgressUseCase updateWatchProgressUseCase,
      GetContentByCategoryUseCase getContentByCategoryUseCase,
      GetLiveTVChannelsUseCase getLiveTVChannelsUseCase, ExoPlayerManager exoPlayerManager,
      ErrorHandler errorHandler) {
    return new PlayerViewModel(getContentDetailsUseCase, getContentStreamUseCase, getEpisodeStreamUseCase, updateWatchProgressUseCase, getContentByCategoryUseCase, getLiveTVChannelsUseCase, exoPlayerManager, errorHandler);
  }
}
