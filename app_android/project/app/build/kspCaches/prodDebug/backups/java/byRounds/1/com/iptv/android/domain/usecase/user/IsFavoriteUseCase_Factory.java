package com.iptv.android.domain.usecase.user;

import com.iptv.android.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class IsFavoriteUseCase_Factory implements Factory<IsFavoriteUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public IsFavoriteUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public IsFavoriteUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static IsFavoriteUseCase_Factory create(Provider<UserRepository> userRepositoryProvider) {
    return new IsFavoriteUseCase_Factory(userRepositoryProvider);
  }

  public static IsFavoriteUseCase newInstance(UserRepository userRepository) {
    return new IsFavoriteUseCase(userRepository);
  }
}
