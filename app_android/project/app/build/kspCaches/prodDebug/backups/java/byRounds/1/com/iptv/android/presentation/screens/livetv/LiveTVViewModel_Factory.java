package com.iptv.android.presentation.screens.livetv;

import com.iptv.android.domain.usecase.content.GetLiveTVChannelsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LiveTVViewModel_Factory implements Factory<LiveTVViewModel> {
  private final Provider<GetLiveTVChannelsUseCase> getLiveTVChannelsUseCaseProvider;

  public LiveTVViewModel_Factory(
      Provider<GetLiveTVChannelsUseCase> getLiveTVChannelsUseCaseProvider) {
    this.getLiveTVChannelsUseCaseProvider = getLiveTVChannelsUseCaseProvider;
  }

  @Override
  public LiveTVViewModel get() {
    return newInstance(getLiveTVChannelsUseCaseProvider.get());
  }

  public static LiveTVViewModel_Factory create(
      Provider<GetLiveTVChannelsUseCase> getLiveTVChannelsUseCaseProvider) {
    return new LiveTVViewModel_Factory(getLiveTVChannelsUseCaseProvider);
  }

  public static LiveTVViewModel newInstance(GetLiveTVChannelsUseCase getLiveTVChannelsUseCase) {
    return new LiveTVViewModel(getLiveTVChannelsUseCase);
  }
}
