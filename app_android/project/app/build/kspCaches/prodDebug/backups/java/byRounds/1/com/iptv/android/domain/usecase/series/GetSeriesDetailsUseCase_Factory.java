package com.iptv.android.domain.usecase.series;

import com.iptv.android.domain.repository.ContentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSeriesDetailsUseCase_Factory implements Factory<GetSeriesDetailsUseCase> {
  private final Provider<ContentRepository> contentRepositoryProvider;

  public GetSeriesDetailsUseCase_Factory(Provider<ContentRepository> contentRepositoryProvider) {
    this.contentRepositoryProvider = contentRepositoryProvider;
  }

  @Override
  public GetSeriesDetailsUseCase get() {
    return newInstance(contentRepositoryProvider.get());
  }

  public static GetSeriesDetailsUseCase_Factory create(
      Provider<ContentRepository> contentRepositoryProvider) {
    return new GetSeriesDetailsUseCase_Factory(contentRepositoryProvider);
  }

  public static GetSeriesDetailsUseCase newInstance(ContentRepository contentRepository) {
    return new GetSeriesDetailsUseCase(contentRepository);
  }
}
