package com.iptv.android.domain.usecase.auth;

import com.iptv.android.domain.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetAccessTokenUseCase_Factory implements Factory<GetAccessTokenUseCase> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public GetAccessTokenUseCase_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public GetAccessTokenUseCase get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static GetAccessTokenUseCase_Factory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new GetAccessTokenUseCase_Factory(authRepositoryProvider);
  }

  public static GetAccessTokenUseCase newInstance(AuthRepository authRepository) {
    return new GetAccessTokenUseCase(authRepository);
  }
}
