package com.iptv.android.data.remote.interceptor;

import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.core.security.UserAgentRenewalService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DynamicUserAgentInterceptor_Factory implements Factory<DynamicUserAgentInterceptor> {
  private final Provider<DynamicUserAgentManager> userAgentManagerProvider;

  private final Provider<UserAgentRenewalService> userAgentRenewalServiceProvider;

  public DynamicUserAgentInterceptor_Factory(
      Provider<DynamicUserAgentManager> userAgentManagerProvider,
      Provider<UserAgentRenewalService> userAgentRenewalServiceProvider) {
    this.userAgentManagerProvider = userAgentManagerProvider;
    this.userAgentRenewalServiceProvider = userAgentRenewalServiceProvider;
  }

  @Override
  public DynamicUserAgentInterceptor get() {
    return newInstance(userAgentManagerProvider.get(), userAgentRenewalServiceProvider.get());
  }

  public static DynamicUserAgentInterceptor_Factory create(
      Provider<DynamicUserAgentManager> userAgentManagerProvider,
      Provider<UserAgentRenewalService> userAgentRenewalServiceProvider) {
    return new DynamicUserAgentInterceptor_Factory(userAgentManagerProvider, userAgentRenewalServiceProvider);
  }

  public static DynamicUserAgentInterceptor newInstance(DynamicUserAgentManager userAgentManager,
      UserAgentRenewalService userAgentRenewalService) {
    return new DynamicUserAgentInterceptor(userAgentManager, userAgentRenewalService);
  }
}
