// Generated by Da<PERSON> (https://dagger.dev).
package com.iptv.android;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.google.gson.Gson;
import com.iptv.android.core.device.DeviceIdentifierManager;
import com.iptv.android.core.error.ErrorHandler;
import com.iptv.android.core.media.CodecCompatibilityHelper;
import com.iptv.android.core.network.BackendHandler;
import com.iptv.android.core.network.NetworkSecurityHelper;
import com.iptv.android.core.security.DynamicUserAgentManager;
import com.iptv.android.core.security.NetworkSecurityManager;
import com.iptv.android.core.security.UserAgentMonitorService;
import com.iptv.android.core.security.UserAgentRenewalManager;
import com.iptv.android.core.security.UserAgentRenewalService;
import com.iptv.android.data.local.dao.ContentDao;
import com.iptv.android.data.local.dao.UserDao;
import com.iptv.android.data.local.database.IPTVDatabase;
import com.iptv.android.data.local.datastore.TokenManager;
import com.iptv.android.data.local.datastore.UserPreferencesManager;
import com.iptv.android.data.remote.api.IPTVApiService;
import com.iptv.android.data.remote.api.RefreshTokenService;
import com.iptv.android.data.remote.interceptor.AuthInterceptor;
import com.iptv.android.data.remote.interceptor.DynamicBaseUrlInterceptor;
import com.iptv.android.data.remote.interceptor.DynamicUserAgentInterceptor;
import com.iptv.android.data.remote.interceptor.ErrorInterceptor;
import com.iptv.android.data.remote.interceptor.UserAgentErrorInterceptor;
import com.iptv.android.data.repository.AuthRepositoryImpl;
import com.iptv.android.data.repository.ContentRepositoryImpl;
import com.iptv.android.data.repository.UserProfileRepository;
import com.iptv.android.data.repository.UserRepositoryImpl;
import com.iptv.android.di.DataStoreModule;
import com.iptv.android.di.DataStoreModule_ProvideDataStoreFactory;
import com.iptv.android.di.DataStoreModule_ProvideTokenManagerFactory;
import com.iptv.android.di.DataStoreModule_ProvideUserPreferencesManagerFactory;
import com.iptv.android.di.DatabaseModule;
import com.iptv.android.di.DatabaseModule_ProvideContentDaoFactory;
import com.iptv.android.di.DatabaseModule_ProvideIPTVDatabaseFactory;
import com.iptv.android.di.DatabaseModule_ProvideUserDaoFactory;
import com.iptv.android.di.NetworkModule;
import com.iptv.android.di.NetworkModule_ProvideGsonFactory;
import com.iptv.android.di.NetworkModule_ProvideHttpLoggingInterceptorFactory;
import com.iptv.android.di.NetworkModule_ProvideIPTVApiServiceFactory;
import com.iptv.android.di.NetworkModule_ProvideOkHttpClientFactory;
import com.iptv.android.di.NetworkModule_ProvideRefreshTokenOkHttpClientFactory;
import com.iptv.android.di.NetworkModule_ProvideRefreshTokenRetrofitFactory;
import com.iptv.android.di.NetworkModule_ProvideRefreshTokenServiceFactory;
import com.iptv.android.di.NetworkModule_ProvideRetrofitFactory;
import com.iptv.android.di.PlayerModule;
import com.iptv.android.di.PlayerModule_ProvideCodecCompatibilityHelperFactory;
import com.iptv.android.di.PlayerModule_ProvideDrmHelperFactory;
import com.iptv.android.di.PlayerModule_ProvideExoPlayerManagerFactory;
import com.iptv.android.di.PlayerModule_ProvideNetworkSecurityHelperFactory;
import com.iptv.android.di.SecurityModule;
import com.iptv.android.di.SecurityModule_ProvideApplicationCoroutineScopeFactory;
import com.iptv.android.di.SecurityModule_ProvideBackendHandlerFactory;
import com.iptv.android.di.SecurityModule_ProvideDynamicUserAgentManagerFactory;
import com.iptv.android.di.SecurityModule_ProvideNetworkSecurityManagerFactory;
import com.iptv.android.di.SecurityModule_ProvideUserAgentMonitorServiceFactory;
import com.iptv.android.di.SecurityModule_ProvideUserAgentRenewalServiceFactory;
import com.iptv.android.di.UseCaseModule;
import com.iptv.android.di.UseCaseModule_ProvideActivateDeviceUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideAddToFavoritesUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideClearWatchHistoryUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetCategoriesUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetContentByCategoryUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetContentDetailsUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetContentSectionsUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetDeviceStatusUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetFavoritesUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetFeaturedContentUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetUserPreferencesUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetUserProfileUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetWatchHistoryUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideGetWatchProgressUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideHasParentalPinUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideIsAuthenticatedUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideIsFavoriteUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideLoginUserUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideLogoutUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideRegisterDeviceUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideRemoveDeviceUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideRemoveFromFavoritesUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideSearchContentUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideSetParentalPinUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideUpdateDeviceNameUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideUpdateUserPreferencesUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideUpdateWatchProgressUseCaseFactory;
import com.iptv.android.di.UseCaseModule_ProvideVerifyParentalPinUseCaseFactory;
import com.iptv.android.domain.usecase.auth.ActivateDeviceUseCase;
import com.iptv.android.domain.usecase.auth.GetDeviceStatusUseCase;
import com.iptv.android.domain.usecase.auth.IsAuthenticatedUseCase;
import com.iptv.android.domain.usecase.auth.LoginUserUseCase;
import com.iptv.android.domain.usecase.auth.LogoutUseCase;
import com.iptv.android.domain.usecase.auth.RegisterDeviceUseCase;
import com.iptv.android.domain.usecase.content.GetCategoriesUseCase;
import com.iptv.android.domain.usecase.content.GetContentByCategoryUseCase;
import com.iptv.android.domain.usecase.content.GetContentDetailsUseCase;
import com.iptv.android.domain.usecase.content.GetContentSectionsUseCase;
import com.iptv.android.domain.usecase.content.GetContentStreamUseCase;
import com.iptv.android.domain.usecase.content.GetFeaturedContentUseCase;
import com.iptv.android.domain.usecase.content.GetLiveTVChannelsUseCase;
import com.iptv.android.domain.usecase.content.GetNovelaByContentIdUseCase;
import com.iptv.android.domain.usecase.content.GetNovelaSeasonsUseCase;
import com.iptv.android.domain.usecase.content.SearchContentUseCase;
import com.iptv.android.domain.usecase.device.DeviceLimitHandler;
import com.iptv.android.domain.usecase.series.GetEpisodeStreamUseCase;
import com.iptv.android.domain.usecase.series.GetSeasonEpisodesUseCase;
import com.iptv.android.domain.usecase.series.GetSeriesDetailsByContentIdUseCase;
import com.iptv.android.domain.usecase.user.AddToFavoritesUseCase;
import com.iptv.android.domain.usecase.user.ClearWatchHistoryUseCase;
import com.iptv.android.domain.usecase.user.GetFavoritesUseCase;
import com.iptv.android.domain.usecase.user.GetUserPreferencesUseCase;
import com.iptv.android.domain.usecase.user.GetUserProfileUseCase;
import com.iptv.android.domain.usecase.user.GetWatchHistoryUseCase;
import com.iptv.android.domain.usecase.user.GetWatchProgressUseCase;
import com.iptv.android.domain.usecase.user.HasParentalPinUseCase;
import com.iptv.android.domain.usecase.user.IsFavoriteUseCase;
import com.iptv.android.domain.usecase.user.RemoveDeviceUseCase;
import com.iptv.android.domain.usecase.user.RemoveFromFavoritesUseCase;
import com.iptv.android.domain.usecase.user.SetParentalPinUseCase;
import com.iptv.android.domain.usecase.user.UpdateDeviceNameUseCase;
import com.iptv.android.domain.usecase.user.UpdateUserPreferencesUseCase;
import com.iptv.android.domain.usecase.user.UpdateWatchProgressUseCase;
import com.iptv.android.domain.usecase.user.VerifyParentalPinUseCase;
import com.iptv.android.presentation.MainActivity;
import com.iptv.android.presentation.MainActivity_MembersInjector;
import com.iptv.android.presentation.MainViewModel;
import com.iptv.android.presentation.MainViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.navigation.NavigationViewModel;
import com.iptv.android.presentation.navigation.NavigationViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.player.AdvancedPlayerViewModel;
import com.iptv.android.presentation.player.AdvancedPlayerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.player.DrmHelper;
import com.iptv.android.presentation.player.ExoPlayerManager;
import com.iptv.android.presentation.screens.auth.ActivationViewModel;
import com.iptv.android.presentation.screens.auth.ActivationViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.auth.LoginViewModel;
import com.iptv.android.presentation.screens.auth.LoginViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.auth.UserLoginViewModel;
import com.iptv.android.presentation.screens.auth.UserLoginViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.category.CategoryViewModel;
import com.iptv.android.presentation.screens.category.CategoryViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.detail.ContentDetailViewModel;
import com.iptv.android.presentation.screens.detail.ContentDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.detail.DetailViewModel;
import com.iptv.android.presentation.screens.detail.DetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.home.HomeViewModel;
import com.iptv.android.presentation.screens.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.livetv.LiveTVViewModel;
import com.iptv.android.presentation.screens.livetv.LiveTVViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.player.EpisodicPlayerViewModel;
import com.iptv.android.presentation.screens.player.EpisodicPlayerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.player.PlayerViewModel;
import com.iptv.android.presentation.screens.player.PlayerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.profile.ProfileViewModel;
import com.iptv.android.presentation.screens.profile.ProfileViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.search.SearchViewModel;
import com.iptv.android.presentation.screens.search.SearchViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.season.SeasonDetailViewModel;
import com.iptv.android.presentation.screens.season.SeasonDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.series.SeriesDetailViewModel;
import com.iptv.android.presentation.screens.series.SeriesDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.settings.SettingsViewModel;
import com.iptv.android.presentation.screens.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.iptv.android.presentation.screens.splash.SplashViewModel;
import com.iptv.android.presentation.screens.splash.SplashViewModel_HiltModules_KeyModule_ProvideFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import java.util.Map;
import java.util.Set;
import javax.inject.Provider;
import kotlinx.coroutines.CoroutineScope;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerIPTVApplication_HiltComponents_SingletonC {
  private DaggerIPTVApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder dataStoreModule(DataStoreModule dataStoreModule) {
      Preconditions.checkNotNull(dataStoreModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder databaseModule(DatabaseModule databaseModule) {
      Preconditions.checkNotNull(databaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder networkModule(NetworkModule networkModule) {
      Preconditions.checkNotNull(networkModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder playerModule(PlayerModule playerModule) {
      Preconditions.checkNotNull(playerModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder securityModule(SecurityModule securityModule) {
      Preconditions.checkNotNull(securityModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder useCaseModule(UseCaseModule useCaseModule) {
      Preconditions.checkNotNull(useCaseModule);
      return this;
    }

    public IPTVApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements IPTVApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public IPTVApplication_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements IPTVApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public IPTVApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements IPTVApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public IPTVApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements IPTVApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public IPTVApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements IPTVApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public IPTVApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements IPTVApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public IPTVApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements IPTVApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public IPTVApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends IPTVApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends IPTVApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends IPTVApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends IPTVApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
      injectMainActivity2(mainActivity);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return ImmutableSet.<String>of(ActivationViewModel_HiltModules_KeyModule_ProvideFactory.provide(), AdvancedPlayerViewModel_HiltModules_KeyModule_ProvideFactory.provide(), CategoryViewModel_HiltModules_KeyModule_ProvideFactory.provide(), ContentDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide(), DetailViewModel_HiltModules_KeyModule_ProvideFactory.provide(), EpisodicPlayerViewModel_HiltModules_KeyModule_ProvideFactory.provide(), HomeViewModel_HiltModules_KeyModule_ProvideFactory.provide(), LiveTVViewModel_HiltModules_KeyModule_ProvideFactory.provide(), LoginViewModel_HiltModules_KeyModule_ProvideFactory.provide(), MainViewModel_HiltModules_KeyModule_ProvideFactory.provide(), NavigationViewModel_HiltModules_KeyModule_ProvideFactory.provide(), PlayerViewModel_HiltModules_KeyModule_ProvideFactory.provide(), ProfileViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SearchViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SeasonDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SeriesDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SplashViewModel_HiltModules_KeyModule_ProvideFactory.provide(), UserLoginViewModel_HiltModules_KeyModule_ProvideFactory.provide());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectNetworkSecurityManager(instance, singletonCImpl.provideNetworkSecurityManagerProvider.get());
      return instance;
    }
  }

  private static final class ViewModelCImpl extends IPTVApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<ActivationViewModel> activationViewModelProvider;

    private Provider<AdvancedPlayerViewModel> advancedPlayerViewModelProvider;

    private Provider<CategoryViewModel> categoryViewModelProvider;

    private Provider<ContentDetailViewModel> contentDetailViewModelProvider;

    private Provider<DetailViewModel> detailViewModelProvider;

    private Provider<EpisodicPlayerViewModel> episodicPlayerViewModelProvider;

    private Provider<HomeViewModel> homeViewModelProvider;

    private Provider<LiveTVViewModel> liveTVViewModelProvider;

    private Provider<LoginViewModel> loginViewModelProvider;

    private Provider<MainViewModel> mainViewModelProvider;

    private Provider<NavigationViewModel> navigationViewModelProvider;

    private Provider<PlayerViewModel> playerViewModelProvider;

    private Provider<ProfileViewModel> profileViewModelProvider;

    private Provider<SearchViewModel> searchViewModelProvider;

    private Provider<SeasonDetailViewModel> seasonDetailViewModelProvider;

    private Provider<SeriesDetailViewModel> seriesDetailViewModelProvider;

    private Provider<SettingsViewModel> settingsViewModelProvider;

    private Provider<SplashViewModel> splashViewModelProvider;

    private Provider<UserLoginViewModel> userLoginViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    private GetSeriesDetailsByContentIdUseCase getSeriesDetailsByContentIdUseCase() {
      return new GetSeriesDetailsByContentIdUseCase(singletonCImpl.contentRepositoryImplProvider.get(), singletonCImpl.provideGetContentDetailsUseCaseProvider.get());
    }

    private GetEpisodeStreamUseCase getEpisodeStreamUseCase() {
      return new GetEpisodeStreamUseCase(singletonCImpl.contentRepositoryImplProvider.get());
    }

    private GetLiveTVChannelsUseCase getLiveTVChannelsUseCase() {
      return new GetLiveTVChannelsUseCase(singletonCImpl.contentRepositoryImplProvider.get());
    }

    private GetContentStreamUseCase getContentStreamUseCase() {
      return new GetContentStreamUseCase(singletonCImpl.contentRepositoryImplProvider.get());
    }

    private GetSeasonEpisodesUseCase getSeasonEpisodesUseCase() {
      return new GetSeasonEpisodesUseCase(singletonCImpl.contentRepositoryImplProvider.get());
    }

    private GetNovelaByContentIdUseCase getNovelaByContentIdUseCase() {
      return new GetNovelaByContentIdUseCase(singletonCImpl.contentRepositoryImplProvider.get());
    }

    private GetNovelaSeasonsUseCase getNovelaSeasonsUseCase() {
      return new GetNovelaSeasonsUseCase(singletonCImpl.contentRepositoryImplProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.activationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.advancedPlayerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.categoryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.contentDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.detailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.episodicPlayerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.homeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.liveTVViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.loginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.mainViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
      this.navigationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 10);
      this.playerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 11);
      this.profileViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 12);
      this.searchViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 13);
      this.seasonDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 14);
      this.seriesDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 15);
      this.settingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 16);
      this.splashViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 17);
      this.userLoginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 18);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return ImmutableMap.<String, Provider<ViewModel>>builderWithExpectedSize(19).put("com.iptv.android.presentation.screens.auth.ActivationViewModel", ((Provider) activationViewModelProvider)).put("com.iptv.android.presentation.player.AdvancedPlayerViewModel", ((Provider) advancedPlayerViewModelProvider)).put("com.iptv.android.presentation.screens.category.CategoryViewModel", ((Provider) categoryViewModelProvider)).put("com.iptv.android.presentation.screens.detail.ContentDetailViewModel", ((Provider) contentDetailViewModelProvider)).put("com.iptv.android.presentation.screens.detail.DetailViewModel", ((Provider) detailViewModelProvider)).put("com.iptv.android.presentation.screens.player.EpisodicPlayerViewModel", ((Provider) episodicPlayerViewModelProvider)).put("com.iptv.android.presentation.screens.home.HomeViewModel", ((Provider) homeViewModelProvider)).put("com.iptv.android.presentation.screens.livetv.LiveTVViewModel", ((Provider) liveTVViewModelProvider)).put("com.iptv.android.presentation.screens.auth.LoginViewModel", ((Provider) loginViewModelProvider)).put("com.iptv.android.presentation.MainViewModel", ((Provider) mainViewModelProvider)).put("com.iptv.android.presentation.navigation.NavigationViewModel", ((Provider) navigationViewModelProvider)).put("com.iptv.android.presentation.screens.player.PlayerViewModel", ((Provider) playerViewModelProvider)).put("com.iptv.android.presentation.screens.profile.ProfileViewModel", ((Provider) profileViewModelProvider)).put("com.iptv.android.presentation.screens.search.SearchViewModel", ((Provider) searchViewModelProvider)).put("com.iptv.android.presentation.screens.season.SeasonDetailViewModel", ((Provider) seasonDetailViewModelProvider)).put("com.iptv.android.presentation.screens.series.SeriesDetailViewModel", ((Provider) seriesDetailViewModelProvider)).put("com.iptv.android.presentation.screens.settings.SettingsViewModel", ((Provider) settingsViewModelProvider)).put("com.iptv.android.presentation.screens.splash.SplashViewModel", ((Provider) splashViewModelProvider)).put("com.iptv.android.presentation.screens.auth.UserLoginViewModel", ((Provider) userLoginViewModelProvider)).build();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.iptv.android.presentation.screens.auth.ActivationViewModel 
          return (T) new ActivationViewModel(singletonCImpl.provideActivateDeviceUseCaseProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 1: // com.iptv.android.presentation.player.AdvancedPlayerViewModel 
          return (T) new AdvancedPlayerViewModel(singletonCImpl.provideGetContentDetailsUseCaseProvider.get(), singletonCImpl.provideUpdateWatchProgressUseCaseProvider.get(), singletonCImpl.provideExoPlayerManagerProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 2: // com.iptv.android.presentation.screens.category.CategoryViewModel 
          return (T) new CategoryViewModel(singletonCImpl.provideGetContentByCategoryUseCaseProvider.get(), singletonCImpl.provideGetCategoriesUseCaseProvider.get(), singletonCImpl.provideVerifyParentalPinUseCaseProvider.get(), singletonCImpl.provideSetParentalPinUseCaseProvider.get(), singletonCImpl.provideUserPreferencesManagerProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 3: // com.iptv.android.presentation.screens.detail.ContentDetailViewModel 
          return (T) new ContentDetailViewModel(singletonCImpl.provideGetContentDetailsUseCaseProvider.get(), singletonCImpl.provideIsFavoriteUseCaseProvider.get(), singletonCImpl.provideAddToFavoritesUseCaseProvider.get(), singletonCImpl.provideRemoveFromFavoritesUseCaseProvider.get(), singletonCImpl.provideGetWatchProgressUseCaseProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 4: // com.iptv.android.presentation.screens.detail.DetailViewModel 
          return (T) new DetailViewModel(singletonCImpl.contentRepositoryImplProvider.get());

          case 5: // com.iptv.android.presentation.screens.player.EpisodicPlayerViewModel 
          return (T) new EpisodicPlayerViewModel(singletonCImpl.provideGetContentDetailsUseCaseProvider.get(), viewModelCImpl.getSeriesDetailsByContentIdUseCase(), viewModelCImpl.getEpisodeStreamUseCase(), singletonCImpl.provideExoPlayerManagerProvider.get());

          case 6: // com.iptv.android.presentation.screens.home.HomeViewModel 
          return (T) new HomeViewModel(singletonCImpl.provideGetFeaturedContentUseCaseProvider.get(), singletonCImpl.provideGetContentSectionsUseCaseProvider.get(), singletonCImpl.provideGetCategoriesUseCaseProvider.get(), singletonCImpl.provideGetContentByCategoryUseCaseProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 7: // com.iptv.android.presentation.screens.livetv.LiveTVViewModel 
          return (T) new LiveTVViewModel(viewModelCImpl.getLiveTVChannelsUseCase());

          case 8: // com.iptv.android.presentation.screens.auth.LoginViewModel 
          return (T) new LoginViewModel(singletonCImpl.provideRegisterDeviceUseCaseProvider.get(), singletonCImpl.provideGetDeviceStatusUseCaseProvider.get(), singletonCImpl.deviceIdentifierManagerProvider.get(), singletonCImpl.deviceLimitHandlerProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 9: // com.iptv.android.presentation.MainViewModel 
          return (T) new MainViewModel(singletonCImpl.provideUserPreferencesManagerProvider.get(), singletonCImpl.provideSetParentalPinUseCaseProvider.get(), singletonCImpl.provideHasParentalPinUseCaseProvider.get(), singletonCImpl.provideTokenManagerProvider.get(), singletonCImpl.provideDynamicUserAgentManagerProvider.get(), singletonCImpl.provideUserAgentMonitorServiceProvider.get(), singletonCImpl.authRepositoryImplProvider.get());

          case 10: // com.iptv.android.presentation.navigation.NavigationViewModel 
          return (T) new NavigationViewModel(singletonCImpl.provideIsAuthenticatedUseCaseProvider.get());

          case 11: // com.iptv.android.presentation.screens.player.PlayerViewModel 
          return (T) new PlayerViewModel(singletonCImpl.provideGetContentDetailsUseCaseProvider.get(), viewModelCImpl.getContentStreamUseCase(), viewModelCImpl.getEpisodeStreamUseCase(), singletonCImpl.provideUpdateWatchProgressUseCaseProvider.get(), singletonCImpl.provideGetContentByCategoryUseCaseProvider.get(), viewModelCImpl.getLiveTVChannelsUseCase(), singletonCImpl.provideExoPlayerManagerProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 12: // com.iptv.android.presentation.screens.profile.ProfileViewModel 
          return (T) new ProfileViewModel(singletonCImpl.provideGetUserProfileUseCaseProvider.get(), singletonCImpl.provideGetWatchHistoryUseCaseProvider.get(), singletonCImpl.provideGetFavoritesUseCaseProvider.get(), singletonCImpl.provideClearWatchHistoryUseCaseProvider.get(), singletonCImpl.provideRemoveDeviceUseCaseProvider.get(), singletonCImpl.provideUpdateDeviceNameUseCaseProvider.get(), singletonCImpl.provideLogoutUseCaseProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 13: // com.iptv.android.presentation.screens.search.SearchViewModel 
          return (T) new SearchViewModel(singletonCImpl.provideSearchContentUseCaseProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 14: // com.iptv.android.presentation.screens.season.SeasonDetailViewModel 
          return (T) new SeasonDetailViewModel(viewModelCImpl.getSeasonEpisodesUseCase());

          case 15: // com.iptv.android.presentation.screens.series.SeriesDetailViewModel 
          return (T) new SeriesDetailViewModel(viewModelCImpl.getSeriesDetailsByContentIdUseCase(), singletonCImpl.provideGetContentDetailsUseCaseProvider.get(), viewModelCImpl.getNovelaByContentIdUseCase(), viewModelCImpl.getNovelaSeasonsUseCase());

          case 16: // com.iptv.android.presentation.screens.settings.SettingsViewModel 
          return (T) new SettingsViewModel(singletonCImpl.provideGetUserPreferencesUseCaseProvider.get(), singletonCImpl.provideUpdateUserPreferencesUseCaseProvider.get(), singletonCImpl.provideLogoutUseCaseProvider.get(), singletonCImpl.userProfileRepositoryProvider.get(), singletonCImpl.errorHandlerProvider.get());

          case 17: // com.iptv.android.presentation.screens.splash.SplashViewModel 
          return (T) new SplashViewModel(singletonCImpl.provideTokenManagerProvider.get(), singletonCImpl.provideDynamicUserAgentManagerProvider.get(), singletonCImpl.userAgentRenewalManagerProvider.get());

          case 18: // com.iptv.android.presentation.screens.auth.UserLoginViewModel 
          return (T) new UserLoginViewModel(singletonCImpl.provideLoginUserUseCaseProvider.get(), singletonCImpl.errorHandlerProvider2.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends IPTVApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends IPTVApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends IPTVApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<NetworkSecurityManager> provideNetworkSecurityManagerProvider;

    private Provider<BackendHandler> provideBackendHandlerProvider;

    private Provider<DynamicBaseUrlInterceptor> dynamicBaseUrlInterceptorProvider;

    private Provider<DataStore<Preferences>> provideDataStoreProvider;

    private Provider<TokenManager> provideTokenManagerProvider;

    private Provider<CoroutineScope> provideApplicationCoroutineScopeProvider;

    private Provider<DynamicUserAgentManager> provideDynamicUserAgentManagerProvider;

    private Provider<UserAgentRenewalService> provideUserAgentRenewalServiceProvider;

    private Provider<DynamicUserAgentInterceptor> dynamicUserAgentInterceptorProvider;

    private Provider<ErrorInterceptor> errorInterceptorProvider;

    private Provider<HttpLoggingInterceptor> provideHttpLoggingInterceptorProvider;

    private Provider<OkHttpClient> provideRefreshTokenOkHttpClientProvider;

    private Provider<Gson> provideGsonProvider;

    private Provider<Retrofit> provideRefreshTokenRetrofitProvider;

    private Provider<RefreshTokenService> provideRefreshTokenServiceProvider;

    private Provider<AuthInterceptor> authInterceptorProvider;

    private Provider<UserAgentRenewalManager> userAgentRenewalManagerProvider;

    private Provider<UserAgentErrorInterceptor> userAgentErrorInterceptorProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<Retrofit> provideRetrofitProvider;

    private Provider<IPTVApiService> provideIPTVApiServiceProvider;

    private Provider<DeviceIdentifierManager> deviceIdentifierManagerProvider;

    private Provider<UserPreferencesManager> provideUserPreferencesManagerProvider;

    private Provider<AuthRepositoryImpl> authRepositoryImplProvider;

    private Provider<ActivateDeviceUseCase> provideActivateDeviceUseCaseProvider;

    private Provider<ErrorHandler> errorHandlerProvider;

    private Provider<IPTVDatabase> provideIPTVDatabaseProvider;

    private Provider<ContentRepositoryImpl> contentRepositoryImplProvider;

    private Provider<GetContentDetailsUseCase> provideGetContentDetailsUseCaseProvider;

    private Provider<UserRepositoryImpl> userRepositoryImplProvider;

    private Provider<UpdateWatchProgressUseCase> provideUpdateWatchProgressUseCaseProvider;

    private Provider<DrmHelper> provideDrmHelperProvider;

    private Provider<NetworkSecurityHelper> provideNetworkSecurityHelperProvider;

    private Provider<CodecCompatibilityHelper> provideCodecCompatibilityHelperProvider;

    private Provider<ExoPlayerManager> provideExoPlayerManagerProvider;

    private Provider<GetContentByCategoryUseCase> provideGetContentByCategoryUseCaseProvider;

    private Provider<GetCategoriesUseCase> provideGetCategoriesUseCaseProvider;

    private Provider<VerifyParentalPinUseCase> provideVerifyParentalPinUseCaseProvider;

    private Provider<SetParentalPinUseCase> provideSetParentalPinUseCaseProvider;

    private Provider<IsFavoriteUseCase> provideIsFavoriteUseCaseProvider;

    private Provider<AddToFavoritesUseCase> provideAddToFavoritesUseCaseProvider;

    private Provider<RemoveFromFavoritesUseCase> provideRemoveFromFavoritesUseCaseProvider;

    private Provider<GetWatchProgressUseCase> provideGetWatchProgressUseCaseProvider;

    private Provider<GetFeaturedContentUseCase> provideGetFeaturedContentUseCaseProvider;

    private Provider<GetContentSectionsUseCase> provideGetContentSectionsUseCaseProvider;

    private Provider<RegisterDeviceUseCase> provideRegisterDeviceUseCaseProvider;

    private Provider<GetDeviceStatusUseCase> provideGetDeviceStatusUseCaseProvider;

    private Provider<DeviceLimitHandler> deviceLimitHandlerProvider;

    private Provider<HasParentalPinUseCase> provideHasParentalPinUseCaseProvider;

    private Provider<UserAgentMonitorService> provideUserAgentMonitorServiceProvider;

    private Provider<IsAuthenticatedUseCase> provideIsAuthenticatedUseCaseProvider;

    private Provider<GetUserProfileUseCase> provideGetUserProfileUseCaseProvider;

    private Provider<GetWatchHistoryUseCase> provideGetWatchHistoryUseCaseProvider;

    private Provider<GetFavoritesUseCase> provideGetFavoritesUseCaseProvider;

    private Provider<ClearWatchHistoryUseCase> provideClearWatchHistoryUseCaseProvider;

    private Provider<RemoveDeviceUseCase> provideRemoveDeviceUseCaseProvider;

    private Provider<UpdateDeviceNameUseCase> provideUpdateDeviceNameUseCaseProvider;

    private Provider<LogoutUseCase> provideLogoutUseCaseProvider;

    private Provider<SearchContentUseCase> provideSearchContentUseCaseProvider;

    private Provider<GetUserPreferencesUseCase> provideGetUserPreferencesUseCaseProvider;

    private Provider<UpdateUserPreferencesUseCase> provideUpdateUserPreferencesUseCaseProvider;

    private Provider<UserProfileRepository> userProfileRepositoryProvider;

    private Provider<LoginUserUseCase> provideLoginUserUseCaseProvider;

    private Provider<com.iptv.android.core.utils.ErrorHandler> errorHandlerProvider2;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private ContentDao contentDao() {
      return DatabaseModule_ProvideContentDaoFactory.provideContentDao(provideIPTVDatabaseProvider.get());
    }

    private UserDao userDao() {
      return DatabaseModule_ProvideUserDaoFactory.provideUserDao(provideIPTVDatabaseProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideNetworkSecurityManagerProvider = DoubleCheck.provider(new SwitchingProvider<NetworkSecurityManager>(singletonCImpl, 0));
      this.provideBackendHandlerProvider = DoubleCheck.provider(new SwitchingProvider<BackendHandler>(singletonCImpl, 7));
      this.dynamicBaseUrlInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<DynamicBaseUrlInterceptor>(singletonCImpl, 6));
      this.provideDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<DataStore<Preferences>>(singletonCImpl, 10));
      this.provideTokenManagerProvider = DoubleCheck.provider(new SwitchingProvider<TokenManager>(singletonCImpl, 9));
      this.provideApplicationCoroutineScopeProvider = DoubleCheck.provider(new SwitchingProvider<CoroutineScope>(singletonCImpl, 16));
      this.provideDynamicUserAgentManagerProvider = DoubleCheck.provider(new SwitchingProvider<DynamicUserAgentManager>(singletonCImpl, 15));
      this.provideUserAgentRenewalServiceProvider = DoubleCheck.provider(new SwitchingProvider<UserAgentRenewalService>(singletonCImpl, 17));
      this.dynamicUserAgentInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<DynamicUserAgentInterceptor>(singletonCImpl, 14));
      this.errorInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<ErrorInterceptor>(singletonCImpl, 18));
      this.provideHttpLoggingInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<HttpLoggingInterceptor>(singletonCImpl, 19));
      this.provideRefreshTokenOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 13));
      this.provideGsonProvider = DoubleCheck.provider(new SwitchingProvider<Gson>(singletonCImpl, 20));
      this.provideRefreshTokenRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 12));
      this.provideRefreshTokenServiceProvider = DoubleCheck.provider(new SwitchingProvider<RefreshTokenService>(singletonCImpl, 11));
      this.authInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<AuthInterceptor>(singletonCImpl, 8));
      this.userAgentRenewalManagerProvider = DoubleCheck.provider(new SwitchingProvider<UserAgentRenewalManager>(singletonCImpl, 22));
      this.userAgentErrorInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<UserAgentErrorInterceptor>(singletonCImpl, 21));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 5));
      this.provideRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 4));
      this.provideIPTVApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<IPTVApiService>(singletonCImpl, 3));
      this.deviceIdentifierManagerProvider = DoubleCheck.provider(new SwitchingProvider<DeviceIdentifierManager>(singletonCImpl, 23));
      this.provideUserPreferencesManagerProvider = DoubleCheck.provider(new SwitchingProvider<UserPreferencesManager>(singletonCImpl, 24));
      this.authRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<AuthRepositoryImpl>(singletonCImpl, 2));
      this.provideActivateDeviceUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ActivateDeviceUseCase>(singletonCImpl, 1));
      this.errorHandlerProvider = DoubleCheck.provider(new SwitchingProvider<ErrorHandler>(singletonCImpl, 25));
      this.provideIPTVDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<IPTVDatabase>(singletonCImpl, 28));
      this.contentRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<ContentRepositoryImpl>(singletonCImpl, 27));
      this.provideGetContentDetailsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetContentDetailsUseCase>(singletonCImpl, 26));
      this.userRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserRepositoryImpl>(singletonCImpl, 30));
      this.provideUpdateWatchProgressUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<UpdateWatchProgressUseCase>(singletonCImpl, 29));
      this.provideDrmHelperProvider = DoubleCheck.provider(new SwitchingProvider<DrmHelper>(singletonCImpl, 32));
      this.provideNetworkSecurityHelperProvider = DoubleCheck.provider(new SwitchingProvider<NetworkSecurityHelper>(singletonCImpl, 33));
      this.provideCodecCompatibilityHelperProvider = DoubleCheck.provider(new SwitchingProvider<CodecCompatibilityHelper>(singletonCImpl, 34));
      this.provideExoPlayerManagerProvider = DoubleCheck.provider(new SwitchingProvider<ExoPlayerManager>(singletonCImpl, 31));
      this.provideGetContentByCategoryUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetContentByCategoryUseCase>(singletonCImpl, 35));
      this.provideGetCategoriesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetCategoriesUseCase>(singletonCImpl, 36));
      this.provideVerifyParentalPinUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<VerifyParentalPinUseCase>(singletonCImpl, 37));
      this.provideSetParentalPinUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<SetParentalPinUseCase>(singletonCImpl, 38));
      this.provideIsFavoriteUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<IsFavoriteUseCase>(singletonCImpl, 39));
      this.provideAddToFavoritesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<AddToFavoritesUseCase>(singletonCImpl, 40));
      this.provideRemoveFromFavoritesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<RemoveFromFavoritesUseCase>(singletonCImpl, 41));
      this.provideGetWatchProgressUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetWatchProgressUseCase>(singletonCImpl, 42));
      this.provideGetFeaturedContentUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetFeaturedContentUseCase>(singletonCImpl, 43));
      this.provideGetContentSectionsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetContentSectionsUseCase>(singletonCImpl, 44));
      this.provideRegisterDeviceUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<RegisterDeviceUseCase>(singletonCImpl, 45));
      this.provideGetDeviceStatusUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetDeviceStatusUseCase>(singletonCImpl, 46));
      this.deviceLimitHandlerProvider = DoubleCheck.provider(new SwitchingProvider<DeviceLimitHandler>(singletonCImpl, 47));
      this.provideHasParentalPinUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<HasParentalPinUseCase>(singletonCImpl, 48));
      this.provideUserAgentMonitorServiceProvider = DoubleCheck.provider(new SwitchingProvider<UserAgentMonitorService>(singletonCImpl, 49));
      this.provideIsAuthenticatedUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<IsAuthenticatedUseCase>(singletonCImpl, 50));
      this.provideGetUserProfileUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetUserProfileUseCase>(singletonCImpl, 51));
      this.provideGetWatchHistoryUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetWatchHistoryUseCase>(singletonCImpl, 52));
      this.provideGetFavoritesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetFavoritesUseCase>(singletonCImpl, 53));
      this.provideClearWatchHistoryUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ClearWatchHistoryUseCase>(singletonCImpl, 54));
      this.provideRemoveDeviceUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<RemoveDeviceUseCase>(singletonCImpl, 55));
      this.provideUpdateDeviceNameUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<UpdateDeviceNameUseCase>(singletonCImpl, 56));
      this.provideLogoutUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<LogoutUseCase>(singletonCImpl, 57));
      this.provideSearchContentUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<SearchContentUseCase>(singletonCImpl, 58));
      this.provideGetUserPreferencesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetUserPreferencesUseCase>(singletonCImpl, 59));
      this.provideUpdateUserPreferencesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<UpdateUserPreferencesUseCase>(singletonCImpl, 60));
      this.userProfileRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<UserProfileRepository>(singletonCImpl, 61));
      this.provideLoginUserUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<LoginUserUseCase>(singletonCImpl, 62));
      this.errorHandlerProvider2 = DoubleCheck.provider(new SwitchingProvider<com.iptv.android.core.utils.ErrorHandler>(singletonCImpl, 63));
    }

    @Override
    public void injectIPTVApplication(IPTVApplication iPTVApplication) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.iptv.android.core.security.NetworkSecurityManager 
          return (T) SecurityModule_ProvideNetworkSecurityManagerFactory.provideNetworkSecurityManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 1: // com.iptv.android.domain.usecase.auth.ActivateDeviceUseCase 
          return (T) UseCaseModule_ProvideActivateDeviceUseCaseFactory.provideActivateDeviceUseCase(singletonCImpl.authRepositoryImplProvider.get());

          case 2: // com.iptv.android.data.repository.AuthRepositoryImpl 
          return (T) new AuthRepositoryImpl(singletonCImpl.provideIPTVApiServiceProvider.get(), singletonCImpl.provideTokenManagerProvider.get(), singletonCImpl.deviceIdentifierManagerProvider.get(), singletonCImpl.provideUserPreferencesManagerProvider.get(), singletonCImpl.provideDynamicUserAgentManagerProvider.get(), singletonCImpl.provideBackendHandlerProvider.get());

          case 3: // com.iptv.android.data.remote.api.IPTVApiService 
          return (T) NetworkModule_ProvideIPTVApiServiceFactory.provideIPTVApiService(singletonCImpl.provideRetrofitProvider.get());

          case 4: // retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRetrofitFactory.provideRetrofit(singletonCImpl.provideOkHttpClientProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 5: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient(singletonCImpl.dynamicBaseUrlInterceptorProvider.get(), singletonCImpl.authInterceptorProvider.get(), singletonCImpl.dynamicUserAgentInterceptorProvider.get(), singletonCImpl.userAgentErrorInterceptorProvider.get(), singletonCImpl.errorInterceptorProvider.get(), singletonCImpl.provideHttpLoggingInterceptorProvider.get());

          case 6: // com.iptv.android.data.remote.interceptor.DynamicBaseUrlInterceptor 
          return (T) new DynamicBaseUrlInterceptor(singletonCImpl.provideBackendHandlerProvider.get());

          case 7: // com.iptv.android.core.network.BackendHandler 
          return (T) SecurityModule_ProvideBackendHandlerFactory.provideBackendHandler(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.iptv.android.data.remote.interceptor.AuthInterceptor 
          return (T) new AuthInterceptor(singletonCImpl.provideTokenManagerProvider.get(), singletonCImpl.provideRefreshTokenServiceProvider.get());

          case 9: // com.iptv.android.data.local.datastore.TokenManager 
          return (T) DataStoreModule_ProvideTokenManagerFactory.provideTokenManager(singletonCImpl.provideDataStoreProvider.get());

          case 10: // androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> 
          return (T) DataStoreModule_ProvideDataStoreFactory.provideDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 11: // com.iptv.android.data.remote.api.RefreshTokenService 
          return (T) NetworkModule_ProvideRefreshTokenServiceFactory.provideRefreshTokenService(singletonCImpl.provideRefreshTokenRetrofitProvider.get());

          case 12: // @javax.inject.Named("RefreshTokenRetrofit") retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRefreshTokenRetrofitFactory.provideRefreshTokenRetrofit(singletonCImpl.provideRefreshTokenOkHttpClientProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 13: // @javax.inject.Named("RefreshTokenClient") okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideRefreshTokenOkHttpClientFactory.provideRefreshTokenOkHttpClient(singletonCImpl.dynamicBaseUrlInterceptorProvider.get(), singletonCImpl.dynamicUserAgentInterceptorProvider.get(), singletonCImpl.errorInterceptorProvider.get(), singletonCImpl.provideHttpLoggingInterceptorProvider.get());

          case 14: // com.iptv.android.data.remote.interceptor.DynamicUserAgentInterceptor 
          return (T) new DynamicUserAgentInterceptor(singletonCImpl.provideDynamicUserAgentManagerProvider.get(), singletonCImpl.provideUserAgentRenewalServiceProvider.get());

          case 15: // com.iptv.android.core.security.DynamicUserAgentManager 
          return (T) SecurityModule_ProvideDynamicUserAgentManagerFactory.provideDynamicUserAgentManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideApplicationCoroutineScopeProvider.get());

          case 16: // kotlinx.coroutines.CoroutineScope 
          return (T) SecurityModule_ProvideApplicationCoroutineScopeFactory.provideApplicationCoroutineScope();

          case 17: // com.iptv.android.core.security.UserAgentRenewalService 
          return (T) SecurityModule_ProvideUserAgentRenewalServiceFactory.provideUserAgentRenewalService(singletonCImpl.provideTokenManagerProvider.get(), singletonCImpl.provideBackendHandlerProvider.get(), singletonCImpl.provideDynamicUserAgentManagerProvider.get());

          case 18: // com.iptv.android.data.remote.interceptor.ErrorInterceptor 
          return (T) new ErrorInterceptor();

          case 19: // okhttp3.logging.HttpLoggingInterceptor 
          return (T) NetworkModule_ProvideHttpLoggingInterceptorFactory.provideHttpLoggingInterceptor();

          case 20: // com.google.gson.Gson 
          return (T) NetworkModule_ProvideGsonFactory.provideGson();

          case 21: // com.iptv.android.data.remote.interceptor.UserAgentErrorInterceptor 
          return (T) new UserAgentErrorInterceptor(singletonCImpl.provideDynamicUserAgentManagerProvider.get(), singletonCImpl.userAgentRenewalManagerProvider.get());

          case 22: // com.iptv.android.core.security.UserAgentRenewalManager 
          return (T) new UserAgentRenewalManager(singletonCImpl.provideTokenManagerProvider.get(), singletonCImpl.provideDynamicUserAgentManagerProvider.get());

          case 23: // com.iptv.android.core.device.DeviceIdentifierManager 
          return (T) new DeviceIdentifierManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 24: // com.iptv.android.data.local.datastore.UserPreferencesManager 
          return (T) DataStoreModule_ProvideUserPreferencesManagerFactory.provideUserPreferencesManager(singletonCImpl.provideDataStoreProvider.get());

          case 25: // com.iptv.android.core.error.ErrorHandler 
          return (T) new ErrorHandler();

          case 26: // com.iptv.android.domain.usecase.content.GetContentDetailsUseCase 
          return (T) UseCaseModule_ProvideGetContentDetailsUseCaseFactory.provideGetContentDetailsUseCase(singletonCImpl.contentRepositoryImplProvider.get());

          case 27: // com.iptv.android.data.repository.ContentRepositoryImpl 
          return (T) new ContentRepositoryImpl(singletonCImpl.provideIPTVApiServiceProvider.get(), singletonCImpl.contentDao());

          case 28: // com.iptv.android.data.local.database.IPTVDatabase 
          return (T) DatabaseModule_ProvideIPTVDatabaseFactory.provideIPTVDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 29: // com.iptv.android.domain.usecase.user.UpdateWatchProgressUseCase 
          return (T) UseCaseModule_ProvideUpdateWatchProgressUseCaseFactory.provideUpdateWatchProgressUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 30: // com.iptv.android.data.repository.UserRepositoryImpl 
          return (T) new UserRepositoryImpl(singletonCImpl.provideIPTVApiServiceProvider.get(), singletonCImpl.userDao(), singletonCImpl.contentDao(), singletonCImpl.provideUserPreferencesManagerProvider.get());

          case 31: // com.iptv.android.presentation.player.ExoPlayerManager 
          return (T) PlayerModule_ProvideExoPlayerManagerFactory.provideExoPlayerManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideDrmHelperProvider.get(), singletonCImpl.provideNetworkSecurityHelperProvider.get(), singletonCImpl.provideCodecCompatibilityHelperProvider.get());

          case 32: // com.iptv.android.presentation.player.DrmHelper 
          return (T) PlayerModule_ProvideDrmHelperFactory.provideDrmHelper();

          case 33: // com.iptv.android.core.network.NetworkSecurityHelper 
          return (T) PlayerModule_ProvideNetworkSecurityHelperFactory.provideNetworkSecurityHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 34: // com.iptv.android.core.media.CodecCompatibilityHelper 
          return (T) PlayerModule_ProvideCodecCompatibilityHelperFactory.provideCodecCompatibilityHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 35: // com.iptv.android.domain.usecase.content.GetContentByCategoryUseCase 
          return (T) UseCaseModule_ProvideGetContentByCategoryUseCaseFactory.provideGetContentByCategoryUseCase(singletonCImpl.contentRepositoryImplProvider.get());

          case 36: // com.iptv.android.domain.usecase.content.GetCategoriesUseCase 
          return (T) UseCaseModule_ProvideGetCategoriesUseCaseFactory.provideGetCategoriesUseCase(singletonCImpl.contentRepositoryImplProvider.get());

          case 37: // com.iptv.android.domain.usecase.user.VerifyParentalPinUseCase 
          return (T) UseCaseModule_ProvideVerifyParentalPinUseCaseFactory.provideVerifyParentalPinUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 38: // com.iptv.android.domain.usecase.user.SetParentalPinUseCase 
          return (T) UseCaseModule_ProvideSetParentalPinUseCaseFactory.provideSetParentalPinUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 39: // com.iptv.android.domain.usecase.user.IsFavoriteUseCase 
          return (T) UseCaseModule_ProvideIsFavoriteUseCaseFactory.provideIsFavoriteUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 40: // com.iptv.android.domain.usecase.user.AddToFavoritesUseCase 
          return (T) UseCaseModule_ProvideAddToFavoritesUseCaseFactory.provideAddToFavoritesUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 41: // com.iptv.android.domain.usecase.user.RemoveFromFavoritesUseCase 
          return (T) UseCaseModule_ProvideRemoveFromFavoritesUseCaseFactory.provideRemoveFromFavoritesUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 42: // com.iptv.android.domain.usecase.user.GetWatchProgressUseCase 
          return (T) UseCaseModule_ProvideGetWatchProgressUseCaseFactory.provideGetWatchProgressUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 43: // com.iptv.android.domain.usecase.content.GetFeaturedContentUseCase 
          return (T) UseCaseModule_ProvideGetFeaturedContentUseCaseFactory.provideGetFeaturedContentUseCase(singletonCImpl.contentRepositoryImplProvider.get());

          case 44: // com.iptv.android.domain.usecase.content.GetContentSectionsUseCase 
          return (T) UseCaseModule_ProvideGetContentSectionsUseCaseFactory.provideGetContentSectionsUseCase(singletonCImpl.contentRepositoryImplProvider.get());

          case 45: // com.iptv.android.domain.usecase.auth.RegisterDeviceUseCase 
          return (T) UseCaseModule_ProvideRegisterDeviceUseCaseFactory.provideRegisterDeviceUseCase(singletonCImpl.authRepositoryImplProvider.get());

          case 46: // com.iptv.android.domain.usecase.auth.GetDeviceStatusUseCase 
          return (T) UseCaseModule_ProvideGetDeviceStatusUseCaseFactory.provideGetDeviceStatusUseCase(singletonCImpl.authRepositoryImplProvider.get());

          case 47: // com.iptv.android.domain.usecase.device.DeviceLimitHandler 
          return (T) new DeviceLimitHandler(singletonCImpl.userRepositoryImplProvider.get(), singletonCImpl.authRepositoryImplProvider.get(), singletonCImpl.deviceIdentifierManagerProvider.get());

          case 48: // com.iptv.android.domain.usecase.user.HasParentalPinUseCase 
          return (T) UseCaseModule_ProvideHasParentalPinUseCaseFactory.provideHasParentalPinUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 49: // com.iptv.android.core.security.UserAgentMonitorService 
          return (T) SecurityModule_ProvideUserAgentMonitorServiceFactory.provideUserAgentMonitorService(singletonCImpl.provideDynamicUserAgentManagerProvider.get(), singletonCImpl.authRepositoryImplProvider.get(), singletonCImpl.provideTokenManagerProvider.get());

          case 50: // com.iptv.android.domain.usecase.auth.IsAuthenticatedUseCase 
          return (T) UseCaseModule_ProvideIsAuthenticatedUseCaseFactory.provideIsAuthenticatedUseCase(singletonCImpl.authRepositoryImplProvider.get());

          case 51: // com.iptv.android.domain.usecase.user.GetUserProfileUseCase 
          return (T) UseCaseModule_ProvideGetUserProfileUseCaseFactory.provideGetUserProfileUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 52: // com.iptv.android.domain.usecase.user.GetWatchHistoryUseCase 
          return (T) UseCaseModule_ProvideGetWatchHistoryUseCaseFactory.provideGetWatchHistoryUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 53: // com.iptv.android.domain.usecase.user.GetFavoritesUseCase 
          return (T) UseCaseModule_ProvideGetFavoritesUseCaseFactory.provideGetFavoritesUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 54: // com.iptv.android.domain.usecase.user.ClearWatchHistoryUseCase 
          return (T) UseCaseModule_ProvideClearWatchHistoryUseCaseFactory.provideClearWatchHistoryUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 55: // com.iptv.android.domain.usecase.user.RemoveDeviceUseCase 
          return (T) UseCaseModule_ProvideRemoveDeviceUseCaseFactory.provideRemoveDeviceUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 56: // com.iptv.android.domain.usecase.user.UpdateDeviceNameUseCase 
          return (T) UseCaseModule_ProvideUpdateDeviceNameUseCaseFactory.provideUpdateDeviceNameUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 57: // com.iptv.android.domain.usecase.auth.LogoutUseCase 
          return (T) UseCaseModule_ProvideLogoutUseCaseFactory.provideLogoutUseCase(singletonCImpl.authRepositoryImplProvider.get());

          case 58: // com.iptv.android.domain.usecase.content.SearchContentUseCase 
          return (T) UseCaseModule_ProvideSearchContentUseCaseFactory.provideSearchContentUseCase(singletonCImpl.contentRepositoryImplProvider.get());

          case 59: // com.iptv.android.domain.usecase.user.GetUserPreferencesUseCase 
          return (T) UseCaseModule_ProvideGetUserPreferencesUseCaseFactory.provideGetUserPreferencesUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 60: // com.iptv.android.domain.usecase.user.UpdateUserPreferencesUseCase 
          return (T) UseCaseModule_ProvideUpdateUserPreferencesUseCaseFactory.provideUpdateUserPreferencesUseCase(singletonCImpl.userRepositoryImplProvider.get());

          case 61: // com.iptv.android.data.repository.UserProfileRepository 
          return (T) new UserProfileRepository(singletonCImpl.provideIPTVApiServiceProvider.get());

          case 62: // com.iptv.android.domain.usecase.auth.LoginUserUseCase 
          return (T) UseCaseModule_ProvideLoginUserUseCaseFactory.provideLoginUserUseCase(singletonCImpl.authRepositoryImplProvider.get());

          case 63: // com.iptv.android.core.utils.ErrorHandler 
          return (T) new com.iptv.android.core.utils.ErrorHandler();

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
