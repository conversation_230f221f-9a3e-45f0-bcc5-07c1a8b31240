1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.iptv.android.dev"
4    android:versionCode="1"
5    android:versionName="1.0.0-dev" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:6:5-67
12-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:7:5-79
13-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:8:5-68
14-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:8:22-65
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:9:5-77
15-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:9:22-74
16
17    <!-- TV Support -->
18    <uses-feature
18-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:12:5-14:36
19        android:name="android.software.leanback"
19-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:13:9-49
20        android:required="false" />
20-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:14:9-33
21    <uses-feature
21-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:15:5-17:36
22        android:name="android.hardware.touchscreen"
22-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:16:9-52
23        android:required="false" />
23-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:17:9-33
24
25    <permission
25-->[androidx.core:core:1.12.0] /home/<USER>/.gradle/caches/transforms-3/fbeddf5ce9272df7a319e79ff274b3f1/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
26        android:name="com.iptv.android.dev.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.12.0] /home/<USER>/.gradle/caches/transforms-3/fbeddf5ce9272df7a319e79ff274b3f1/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.12.0] /home/<USER>/.gradle/caches/transforms-3/fbeddf5ce9272df7a319e79ff274b3f1/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.iptv.android.dev.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.12.0] /home/<USER>/.gradle/caches/transforms-3/fbeddf5ce9272df7a319e79ff274b3f1/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.12.0] /home/<USER>/.gradle/caches/transforms-3/fbeddf5ce9272df7a319e79ff274b3f1/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
30
31    <application
31-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:19:5-63:19
32        android:name="com.iptv.android.IPTVApplication"
32-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:20:9-40
33        android:allowBackup="true"
33-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:21:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.12.0] /home/<USER>/.gradle/caches/transforms-3/fbeddf5ce9272df7a319e79ff274b3f1/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:22:9-65
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:fullBackupContent="@xml/backup_rules"
38-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:23:9-54
39        android:icon="@android:drawable/ic_media_play"
39-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:24:9-55
40        android:label="@string/app_name"
40-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:25:9-41
41        android:networkSecurityConfig="@xml/network_security_config"
41-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:29:9-69
42        android:roundIcon="@android:drawable/ic_media_play"
42-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:26:9-60
43        android:supportsRtl="true"
43-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:27:9-35
44        android:testOnly="true"
45        android:theme="@style/Theme.IPTVAndroid"
45-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:28:9-49
46        android:usesCleartextTraffic="true" >
46-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:30:9-44
47
48        <!-- Main Activity -->
49        <activity
49-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:34:9-50:20
50            android:name="com.iptv.android.presentation.MainActivity"
50-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:35:13-54
51            android:configChanges="orientation|screenSize|keyboardHidden"
51-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:39:13-74
52            android:exported="true"
52-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:36:13-36
53            android:screenOrientation="landscape"
53-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:38:13-50
54            android:theme="@style/Theme.IPTVAndroid.SplashScreen" >
54-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:37:13-66
55            <intent-filter>
55-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:40:13-43:29
56                <action android:name="android.intent.action.MAIN" />
56-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:41:17-69
56-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:41:25-66
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:42:17-77
58-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:42:27-74
59            </intent-filter>
60
61            <!-- TV Support -->
62            <intent-filter>
62-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:46:13-49:29
63                <action android:name="android.intent.action.MAIN" />
63-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:41:17-69
63-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:41:25-66
64
65                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
65-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:48:17-86
65-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:48:27-83
66            </intent-filter>
67        </activity>
68
69        <!-- File Provider for sharing -->
70        <provider
71            android:name="androidx.core.content.FileProvider"
71-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:54:13-62
72            android:authorities="com.iptv.android.dev.fileprovider"
72-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:55:13-64
73            android:exported="false"
73-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:56:13-37
74            android:grantUriPermissions="true" >
74-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:57:13-47
75            <meta-data
75-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:58:13-60:54
76                android:name="android.support.FILE_PROVIDER_PATHS"
76-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:59:17-67
77                android:resource="@xml/file_paths" />
77-->/home/<USER>/Escritorio/Ubuntu/app_panel/app_android/project/app/src/main/AndroidManifest.xml:60:17-51
78        </provider>
79
80        <activity
80-->[androidx.compose.ui:ui-test-manifest:1.5.4] /home/<USER>/.gradle/caches/transforms-3/85ec3a23ae21e83f03c4dad68b79631f/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:23:9-25:39
81            android:name="androidx.activity.ComponentActivity"
81-->[androidx.compose.ui:ui-test-manifest:1.5.4] /home/<USER>/.gradle/caches/transforms-3/85ec3a23ae21e83f03c4dad68b79631f/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:24:13-63
82            android:exported="true" />
82-->[androidx.compose.ui:ui-test-manifest:1.5.4] /home/<USER>/.gradle/caches/transforms-3/85ec3a23ae21e83f03c4dad68b79631f/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:25:13-36
83        <activity
83-->[androidx.compose.ui:ui-tooling-android:1.5.4] /home/<USER>/.gradle/caches/transforms-3/503c016fae56466ae03433f5cb6b891a/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
84            android:name="androidx.compose.ui.tooling.PreviewActivity"
84-->[androidx.compose.ui:ui-tooling-android:1.5.4] /home/<USER>/.gradle/caches/transforms-3/503c016fae56466ae03433f5cb6b891a/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
85            android:exported="true" />
85-->[androidx.compose.ui:ui-tooling-android:1.5.4] /home/<USER>/.gradle/caches/transforms-3/503c016fae56466ae03433f5cb6b891a/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
86
87        <provider
87-->[androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/transforms-3/f7a2300cf82819650b57522988ebba51/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
88            android:name="androidx.startup.InitializationProvider"
88-->[androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/transforms-3/f7a2300cf82819650b57522988ebba51/transformed/emoji2-1.4.0/AndroidManifest.xml:25:13-67
89            android:authorities="com.iptv.android.dev.androidx-startup"
89-->[androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/transforms-3/f7a2300cf82819650b57522988ebba51/transformed/emoji2-1.4.0/AndroidManifest.xml:26:13-68
90            android:exported="false" >
90-->[androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/transforms-3/f7a2300cf82819650b57522988ebba51/transformed/emoji2-1.4.0/AndroidManifest.xml:27:13-37
91            <meta-data
91-->[androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/transforms-3/f7a2300cf82819650b57522988ebba51/transformed/emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
92                android:name="androidx.emoji2.text.EmojiCompatInitializer"
92-->[androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/transforms-3/f7a2300cf82819650b57522988ebba51/transformed/emoji2-1.4.0/AndroidManifest.xml:30:17-75
93                android:value="androidx.startup" />
93-->[androidx.emoji2:emoji2:1.4.0] /home/<USER>/.gradle/caches/transforms-3/f7a2300cf82819650b57522988ebba51/transformed/emoji2-1.4.0/AndroidManifest.xml:31:17-49
94            <meta-data
94-->[androidx.lifecycle:lifecycle-process:2.7.0] /home/<USER>/.gradle/caches/transforms-3/dcaaf7a581ff0d03fc650710b766ba72/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
95                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
95-->[androidx.lifecycle:lifecycle-process:2.7.0] /home/<USER>/.gradle/caches/transforms-3/dcaaf7a581ff0d03fc650710b766ba72/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
96                android:value="androidx.startup" />
96-->[androidx.lifecycle:lifecycle-process:2.7.0] /home/<USER>/.gradle/caches/transforms-3/dcaaf7a581ff0d03fc650710b766ba72/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
98                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
99                android:value="androidx.startup" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
100        </provider>
101
102        <service
102-->[androidx.room:room-runtime:2.6.0] /home/<USER>/.gradle/caches/transforms-3/744fce450e77df837b20f28b9ff50ec6/transformed/room-runtime-2.6.0/AndroidManifest.xml:24:9-28:63
103            android:name="androidx.room.MultiInstanceInvalidationService"
103-->[androidx.room:room-runtime:2.6.0] /home/<USER>/.gradle/caches/transforms-3/744fce450e77df837b20f28b9ff50ec6/transformed/room-runtime-2.6.0/AndroidManifest.xml:25:13-74
104            android:directBootAware="true"
104-->[androidx.room:room-runtime:2.6.0] /home/<USER>/.gradle/caches/transforms-3/744fce450e77df837b20f28b9ff50ec6/transformed/room-runtime-2.6.0/AndroidManifest.xml:26:13-43
105            android:exported="false" />
105-->[androidx.room:room-runtime:2.6.0] /home/<USER>/.gradle/caches/transforms-3/744fce450e77df837b20f28b9ff50ec6/transformed/room-runtime-2.6.0/AndroidManifest.xml:27:13-37
106
107        <receiver
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
108            android:name="androidx.profileinstaller.ProfileInstallReceiver"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
109            android:directBootAware="false"
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
110            android:enabled="true"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
111            android:exported="true"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
112            android:permission="android.permission.DUMP" >
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
114                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
114-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
114-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
117                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
117-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
117-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
118            </intent-filter>
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
120                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
120-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
120-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
121            </intent-filter>
122            <intent-filter>
122-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
123                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
123-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/transforms-3/4a7eaffefe7e70e13b47e5d50eef6463/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
124            </intent-filter>
125        </receiver>
126    </application>
127
128</manifest>
