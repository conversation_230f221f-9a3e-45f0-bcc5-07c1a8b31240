com.iptv.android.dev:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f110445
com.iptv.android.dev:styleable/MaterialTextView = 0x7f12005b
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f110319
com.iptv.android.dev:style/Widget.Material3.Button.ElevatedButton = 0x7f110389
com.iptv.android.dev:styleable/TextInputEditText = 0x7f120093
com.iptv.android.dev:styleable/TabItem = 0x7f120090
com.iptv.android.dev:styleable/Toolbar = 0x7f120096
com.iptv.android.dev:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f110062
com.iptv.android.dev:string/settings_account_title = 0x7f100178
com.iptv.android.dev:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f11005d
com.iptv.android.dev:id/dragUp = 0x7f0a0097
com.iptv.android.dev:styleable/CoordinatorLayout_Layout = 0x7f12002c
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f110072
com.iptv.android.dev:styleable/Chip = 0x7f12001e
com.iptv.android.dev:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f11042e
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f110213
com.iptv.android.dev:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f1103ac
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0e007f
com.iptv.android.dev:dimen/mtrl_navigation_rail_margin = 0x7f0702ed
com.iptv.android.dev:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f11026e
com.iptv.android.dev:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702c4
com.iptv.android.dev:style/Widget.Material3.Toolbar.Surface = 0x7f110418
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1103c0
com.iptv.android.dev:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0e012e
com.iptv.android.dev:dimen/m3_comp_divider_thickness = 0x7f07012b
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f110029
com.iptv.android.dev:string/player_backward = 0x7f100158
com.iptv.android.dev:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1102ea
com.iptv.android.dev:styleable/ButtonBarLayout = 0x7f12001a
com.iptv.android.dev:styleable/AppBarLayoutStates = 0x7f12000c
com.iptv.android.dev:style/Theme.AppCompat = 0x7f110243
com.iptv.android.dev:styleable/KeyTrigger = 0x7f120046
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1101ef
com.iptv.android.dev:id/skipCollapsed = 0x7f0a01ad
com.iptv.android.dev:style/CardView.Light = 0x7f110128
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f11030f
com.iptv.android.dev:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f11040b
com.iptv.android.dev:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f110494
com.iptv.android.dev:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.iptv.android.dev:styleable/RecyclerView = 0x7f12007c
com.iptv.android.dev:styleable/ConstraintLayout_Layout = 0x7f120028
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0e0067
com.iptv.android.dev:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f11047a
com.iptv.android.dev:macro/m3_comp_assist_chip_label_text_type = 0x7f0e0001
com.iptv.android.dev:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f11043a
com.iptv.android.dev:style/Widget.AppCompat.ProgressBar = 0x7f110359
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Subhead = 0x7f11002d
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1101fa
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f110490
com.iptv.android.dev:style/Widget.Material3.Button.TextButton.Dialog = 0x7f110393
com.iptv.android.dev:dimen/abc_dialog_min_width_major = 0x7f070022
com.iptv.android.dev:macro/m3_comp_filter_chip_label_text_type = 0x7f0e0059
com.iptv.android.dev:id/snapMargins = 0x7f0a01b2
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1101c5
com.iptv.android.dev:string/path_password_eye_mask_strike_through = 0x7f100155
com.iptv.android.dev:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0e010f
com.iptv.android.dev:string/abc_searchview_description_voice = 0x7f100017
com.iptv.android.dev:string/search_bar_search = 0x7f100167
com.iptv.android.dev:styleable/ActionBar = 0x7f120000
com.iptv.android.dev:style/ThemeOverlay.Material3.Chip = 0x7f1102cc
com.iptv.android.dev:string/bottomsheet_action_collapse = 0x7f100039
com.iptv.android.dev:styleable/CoordinatorLayout = 0x7f12002b
com.iptv.android.dev:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1100d3
com.iptv.android.dev:id/mtrl_picker_header_toggle = 0x7f0a014b
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1103ea
com.iptv.android.dev:styleable/GradientColorItem = 0x7f12003c
com.iptv.android.dev:string/error_title = 0x7f100090
com.iptv.android.dev:layout/m3_side_sheet_dialog = 0x7f0d003e
com.iptv.android.dev:style/Widget.AppCompat.ActionMode = 0x7f110329
com.iptv.android.dev:string/template_percent = 0x7f100199
com.iptv.android.dev:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f11026b
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f11028b
com.iptv.android.dev:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1102b2
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f11031e
com.iptv.android.dev:style/Theme.SplashScreen.Common = 0x7f1102ad
com.iptv.android.dev:dimen/m3_small_fab_size = 0x7f070209
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f110485
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1102fc
com.iptv.android.dev:style/Widget.Material3.TabLayout.OnSurface = 0x7f110408
com.iptv.android.dev:style/TextAppearance.Material3.SearchView = 0x7f11022a
com.iptv.android.dev:macro/m3_comp_dialog_container_color = 0x7f0e0023
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1103ee
com.iptv.android.dev:macro/m3_comp_search_bar_leading_icon_color = 0x7f0e00ec
com.iptv.android.dev:macro/m3_comp_search_view_divider_color = 0x7f0e00f4
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Caption = 0x7f110233
com.iptv.android.dev:dimen/m3_card_elevated_elevation = 0x7f07010b
com.iptv.android.dev:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f110085
com.iptv.android.dev:drawable/exo_icon_shuffle_on = 0x7f0800b5
com.iptv.android.dev:style/ThemeOverlay.Material3.TabLayout = 0x7f1102f1
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f110283
com.iptv.android.dev:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f110438
com.iptv.android.dev:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f110299
com.iptv.android.dev:string/settings_video = 0x7f10018e
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f11018a
com.iptv.android.dev:styleable/CustomAttribute = 0x7f12002d
com.iptv.android.dev:styleable/PopupWindowBackgroundState = 0x7f120077
com.iptv.android.dev:macro/m3_comp_filled_card_container_shape = 0x7f0e0048
com.iptv.android.dev:styleable/PlayerControlView = 0x7f120074
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0e00cd
com.iptv.android.dev:string/mtrl_switch_thumb_path_name = 0x7f10013c
com.iptv.android.dev:styleable/CardView = 0x7f12001c
com.iptv.android.dev:styleable/MenuItem = 0x7f12005f
com.iptv.android.dev:styleable/RecycleListView = 0x7f12007b
com.iptv.android.dev:id/search_close_btn = 0x7f0a019e
com.iptv.android.dev:style/Base.v21.Theme.SplashScreen.Light = 0x7f110123
com.iptv.android.dev:id/screen = 0x7f0a0195
com.iptv.android.dev:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1102e3
com.iptv.android.dev:styleable/ActivityChooserView = 0x7f120005
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f11048f
com.iptv.android.dev:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f11042b
com.iptv.android.dev:layout/notification_media_action = 0x7f0d006f
com.iptv.android.dev:id/layout = 0x7f0a010c
com.iptv.android.dev:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1102d0
com.iptv.android.dev:styleable/Capability = 0x7f12001b
com.iptv.android.dev:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f0701b6
com.iptv.android.dev:styleable/ActionMenuView = 0x7f120003
com.iptv.android.dev:string/m3_sys_motion_easing_linear = 0x7f1000ea
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker = 0x7f1103e6
com.iptv.android.dev:styleable/ConstraintLayout_placeholder = 0x7f120029
com.iptv.android.dev:drawable/exo_styled_controls_pause = 0x7f0800c9
com.iptv.android.dev:styleable/TabLayout = 0x7f120091
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f11017f
com.iptv.android.dev:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f11011b
com.iptv.android.dev:style/Widget.AppCompat.AutoCompleteTextView = 0x7f11032b
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1103c3
com.iptv.android.dev:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f1103a8
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0e0065
com.iptv.android.dev:dimen/tooltip_corner_radius = 0x7f07033d
com.iptv.android.dev:style/Widget.AppCompat.RatingBar.Small = 0x7f11035d
com.iptv.android.dev:id/startHorizontal = 0x7f0a01c1
com.iptv.android.dev:style/Widget.Material3.DrawerLayout = 0x7f1103b5
com.iptv.android.dev:style/Widget.Design.TabLayout = 0x7f110374
com.iptv.android.dev:styleable/TextAppearance = 0x7f120092
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1100e1
com.iptv.android.dev:style/Theme.AppCompat.Dialog.MinWidth = 0x7f11024e
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1103bd
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f11003a
com.iptv.android.dev:style/Widget.Compat.NotificationActionContainer = 0x7f11036a
com.iptv.android.dev:style/Widget.AppCompat.PopupWindow = 0x7f110358
com.iptv.android.dev:macro/m3_comp_search_bar_container_color = 0x7f0e00e6
com.iptv.android.dev:style/Widget.Material3.Chip.Suggestion = 0x7f1103a7
com.iptv.android.dev:id/ghost_view_holder = 0x7f0a00ee
com.iptv.android.dev:styleable/MaterialToolbar = 0x7f12005d
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialDivider = 0x7f110468
com.iptv.android.dev:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f110391
com.iptv.android.dev:styleable/CompoundButton = 0x7f120026
com.iptv.android.dev:style/Widget.Material3.CircularProgressIndicator = 0x7f1103aa
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Display1 = 0x7f11001b
com.iptv.android.dev:style/Theme.AppCompat.NoActionBar = 0x7f110258
com.iptv.android.dev:styleable/ThemeEnforcement = 0x7f120095
com.iptv.android.dev:style/Widget.Material3.Chip.Filter = 0x7f1103a1
com.iptv.android.dev:plurals/exo_controls_rewind_by_amount_description = 0x7f0f0001
com.iptv.android.dev:dimen/mtrl_btn_pressed_z = 0x7f070285
com.iptv.android.dev:dimen/m3_searchbar_text_size = 0x7f0701fc
com.iptv.android.dev:style/Widget.Material3.CardView.Filled = 0x7f11039c
com.iptv.android.dev:styleable/NavigationBarView = 0x7f12006e
com.iptv.android.dev:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0e0056
com.iptv.android.dev:style/ThemeOverlay.Design.TextInputEditText = 0x7f1102b8
com.iptv.android.dev:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1100d7
com.iptv.android.dev:style/Widget.AppCompat.ActivityChooserView = 0x7f11032a
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1100e4
com.iptv.android.dev:style/Widget.Material3.Button.TextButton.Icon = 0x7f110396
com.iptv.android.dev:dimen/design_fab_translation_z_hovered_focused = 0x7f070073
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Animation = 0x7f110156
com.iptv.android.dev:style/Widget.Material3.Chip.Assist.Elevated = 0x7f1103a0
com.iptv.android.dev:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0e0008
com.iptv.android.dev:style/Platform.V21.AppCompat.Light = 0x7f110173
com.iptv.android.dev:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f11038e
com.iptv.android.dev:color/mtrl_switch_track_decoration_tint = 0x7f0602da
com.iptv.android.dev:style/Theme.Material3.Light.BottomSheetDialog = 0x7f110276
com.iptv.android.dev:string/mtrl_checkbox_button_path_unchecked = 0x7f10010d
com.iptv.android.dev:style/Widget.Material3.Snackbar = 0x7f110404
com.iptv.android.dev:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f110049
com.iptv.android.dev:id/navigation_bar_item_labels_group = 0x7f0a0156
com.iptv.android.dev:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f110471
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1103b8
com.iptv.android.dev:string/loading = 0x7f1000da
com.iptv.android.dev:dimen/exo_small_icon_height = 0x7f0700a3
com.iptv.android.dev:layout/abc_action_mode_close_item_material = 0x7f0d0005
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog = 0x7f11029f
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1103e7
com.iptv.android.dev:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1102c2
com.iptv.android.dev:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f1101aa
com.iptv.android.dev:id/list_item = 0x7f0a0114
com.iptv.android.dev:macro/m3_sys_color_dark_surface_tint = 0x7f0e0175
com.iptv.android.dev:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f110255
com.iptv.android.dev:style/Widget.AppCompat.Spinner.Underlined = 0x7f110365
com.iptv.android.dev:styleable/KeyFramesAcceleration = 0x7f120042
com.iptv.android.dev:style/Widget.AppCompat.ButtonBar = 0x7f110332
com.iptv.android.dev:id/radio = 0x7f0a0186
com.iptv.android.dev:style/TextAppearance.Material3.ActionBar.Title = 0x7f11021b
com.iptv.android.dev:style/Widget.Material3.PopupMenu = 0x7f1103f3
com.iptv.android.dev:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f110422
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Line2 = 0x7f1101f9
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1103d4
com.iptv.android.dev:id/exo_shutter = 0x7f0a00d0
com.iptv.android.dev:macro/m3_comp_search_view_docked_container_shape = 0x7f0e00f5
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f110342
com.iptv.android.dev:style/Theme.Material3.DynamicColors.Dark = 0x7f110272
com.iptv.android.dev:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1100cf
com.iptv.android.dev:style/Widget.AppCompat.Light.SearchView = 0x7f11034f
com.iptv.android.dev:macro/m3_comp_search_bar_supporting_text_color = 0x7f0e00ef
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f110306
com.iptv.android.dev:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702c9
com.iptv.android.dev:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1102dd
com.iptv.android.dev:drawable/abc_list_focused_holo = 0x7f08004e
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1103b9
com.iptv.android.dev:layout/mtrl_alert_dialog = 0x7f0d004d
com.iptv.android.dev:id/stretch = 0x7f0a01c8
com.iptv.android.dev:integer/mtrl_switch_thumb_motion_duration = 0x7f0b0039
com.iptv.android.dev:dimen/m3_comp_outlined_button_outline_width = 0x7f070174
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f11047c
com.iptv.android.dev:style/Widget.AppCompat.ListView = 0x7f110353
com.iptv.android.dev:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f110334
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f1102a0
com.iptv.android.dev:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f110050
com.iptv.android.dev:color/switch_thumb_material_dark = 0x7f06030c
com.iptv.android.dev:id/tag_accessibility_pane_title = 0x7f0a01d0
com.iptv.android.dev:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f11027e
com.iptv.android.dev:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0e00bc
com.iptv.android.dev:style/ThemeOverlay.Material3.Dark = 0x7f1102ce
com.iptv.android.dev:string/app_name_full = 0x7f100025
com.iptv.android.dev:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f110335
com.iptv.android.dev:integer/mtrl_calendar_year_selector_span = 0x7f0b0035
com.iptv.android.dev:style/Widget.AppCompat.Button.Colored = 0x7f110330
com.iptv.android.dev:drawable/abc_seekbar_thumb_material = 0x7f080062
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f11030a
com.iptv.android.dev:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0e00c0
com.iptv.android.dev:layout/design_navigation_item_header = 0x7f0d0025
com.iptv.android.dev:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f11032e
com.iptv.android.dev:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f110421
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f110285
com.iptv.android.dev:id/SHOW_PROGRESS = 0x7f0a000a
com.iptv.android.dev:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1103f0
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f110287
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Light = 0x7f11030b
com.iptv.android.dev:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f11019e
com.iptv.android.dev:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f11019c
com.iptv.android.dev:id/with_icon = 0x7f0a020a
com.iptv.android.dev:id/noScroll = 0x7f0a015b
com.iptv.android.dev:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1100f3
com.iptv.android.dev:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f11008b
com.iptv.android.dev:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f11046d
com.iptv.android.dev:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1100da
com.iptv.android.dev:styleable/RangeSlider = 0x7f12007a
com.iptv.android.dev:styleable/ScrimInsetsFrameLayout = 0x7f12007d
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1103dc
com.iptv.android.dev:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f11010e
com.iptv.android.dev:id/startVertical = 0x7f0a01c3
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f110298
com.iptv.android.dev:style/Base.Widget.MaterialComponents.Snackbar = 0x7f11011e
com.iptv.android.dev:string/mtrl_picker_toggle_to_year_selection = 0x7f100138
com.iptv.android.dev:id/none = 0x7f0a015c
com.iptv.android.dev:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f070149
com.iptv.android.dev:string/mtrl_checkbox_button_path_checked = 0x7f10010a
com.iptv.android.dev:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1102d4
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f110411
com.iptv.android.dev:string/mtrl_picker_announce_current_selection_none = 0x7f100118
com.iptv.android.dev:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702db
com.iptv.android.dev:string/exposed_dropdown_menu_content_description = 0x7f1000c9
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f110313
com.iptv.android.dev:layout/exo_styled_settings_list = 0x7f0d0033
com.iptv.android.dev:id/accessibility_custom_action_8 = 0x7f0a002e
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080017
com.iptv.android.dev:style/TextAppearance.Design.Snackbar.Message = 0x7f110208
com.iptv.android.dev:styleable/AppCompatTextView = 0x7f120012
com.iptv.android.dev:dimen/notification_small_icon_size_as_large = 0x7f070332
com.iptv.android.dev:styleable/Slider = 0x7f120084
com.iptv.android.dev:color/material_on_background_disabled = 0x7f060267
com.iptv.android.dev:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0e004b
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f070224
com.iptv.android.dev:color/material_personalized_color_on_tertiary = 0x7f060283
com.iptv.android.dev:style/TextAppearance.Design.HelperText = 0x7f110204
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1101e6
com.iptv.android.dev:string/m3_ref_typeface_plain_regular = 0x7f1000e2
com.iptv.android.dev:string/cd_user_avatar = 0x7f10004e
com.iptv.android.dev:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f110118
com.iptv.android.dev:drawable/exo_styled_controls_shuffle_on = 0x7f0800d2
com.iptv.android.dev:macro/m3_comp_filled_card_container_color = 0x7f0e0047
com.iptv.android.dev:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1102f6
com.iptv.android.dev:dimen/notification_main_column_padding_top = 0x7f07032d
com.iptv.android.dev:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f11021a
com.iptv.android.dev:id/labeled = 0x7f0a010b
com.iptv.android.dev:dimen/mtrl_calendar_day_corner = 0x7f070292
com.iptv.android.dev:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0e00ac
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f110077
com.iptv.android.dev:styleable/ViewStubCompat = 0x7f12009e
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f110320
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1103d3
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1101c3
com.iptv.android.dev:styleable/ActionBarLayout = 0x7f120001
com.iptv.android.dev:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1102ef
com.iptv.android.dev:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1102eb
com.iptv.android.dev:color/mtrl_filled_icon_tint = 0x7f0602c8
com.iptv.android.dev:string/time_format_hours_minutes = 0x7f10019a
com.iptv.android.dev:style/ExoMediaButton = 0x7f11012a
com.iptv.android.dev:id/exo_progress = 0x7f0a00c8
com.iptv.android.dev:id/TOP_START = 0x7f0a000d
com.iptv.android.dev:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1102d6
com.iptv.android.dev:style/Theme.AppCompat.Dialog.Alert = 0x7f11024d
com.iptv.android.dev:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1103f5
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1103d0
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f11003b
com.iptv.android.dev:styleable/PlayerView = 0x7f120075
com.iptv.android.dev:style/Theme.AppCompat.Empty = 0x7f110250
com.iptv.android.dev:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1102d1
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar = 0x7f110450
com.iptv.android.dev:style/Platform.V21.AppCompat = 0x7f110172
com.iptv.android.dev:layout/mtrl_calendar_month_labeled = 0x7f0d0059
com.iptv.android.dev:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1102be
com.iptv.android.dev:styleable/StyledPlayerView = 0x7f12008d
com.iptv.android.dev:integer/material_motion_duration_short_1 = 0x7f0b002d
com.iptv.android.dev:color/material_dynamic_secondary0 = 0x7f060242
com.iptv.android.dev:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f11038a
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f07017f
com.iptv.android.dev:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08005d
com.iptv.android.dev:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f11046c
com.iptv.android.dev:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0e00c3
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f1102a1
com.iptv.android.dev:xml/backup_rules = 0x7f130000
com.iptv.android.dev:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1102b6
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1103e2
com.iptv.android.dev:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f1103a6
com.iptv.android.dev:string/content_more_info = 0x7f10005a
com.iptv.android.dev:drawable/navigation_empty_icon = 0x7f080129
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f110022
com.iptv.android.dev:dimen/design_snackbar_elevation = 0x7f070081
com.iptv.android.dev:style/Base.TextAppearance.Material3.Search = 0x7f110043
com.iptv.android.dev:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0e0132
com.iptv.android.dev:string/settings_subtitles = 0x7f100189
com.iptv.android.dev:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f110372
com.iptv.android.dev:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f110439
com.iptv.android.dev:id/home = 0x7f0a00f8
com.iptv.android.dev:integer/abc_config_activityDefaultDur = 0x7f0b0000
com.iptv.android.dev:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f07030f
com.iptv.android.dev:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1102c7
com.iptv.android.dev:string/material_minute_suffix = 0x7f1000f5
com.iptv.android.dev:layout/m3_alert_dialog = 0x7f0d003a
com.iptv.android.dev:styleable/ActionMenuItemView = 0x7f120002
com.iptv.android.dev:layout/abc_list_menu_item_radio = 0x7f0d0011
com.iptv.android.dev:id/fixed = 0x7f0a00e5
com.iptv.android.dev:style/ThemeOverlay.Material3 = 0x7f1102b9
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f110487
com.iptv.android.dev:string/player_exit_fullscreen = 0x7f100159
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f110216
com.iptv.android.dev:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f07026c
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f11031b
com.iptv.android.dev:style/Theme.Material3.Dark.SideSheetDialog = 0x7f110269
com.iptv.android.dev:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f110056
com.iptv.android.dev:style/Widget.AppCompat.ListMenuView = 0x7f110351
com.iptv.android.dev:dimen/m3_comp_menu_container_elevation = 0x7f07015a
com.iptv.android.dev:string/mtrl_picker_text_input_month_abbr = 0x7f100132
com.iptv.android.dev:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f07018d
com.iptv.android.dev:string/exo_track_surround_5_point_1 = 0x7f1000c5
com.iptv.android.dev:style/Theme.Material3.DynamicColors.Light = 0x7f110274
com.iptv.android.dev:style/Widget.MaterialComponents.PopupMenu = 0x7f11046f
com.iptv.android.dev:style/Theme.Material3.DayNight.Dialog = 0x7f11026c
com.iptv.android.dev:dimen/m3_badge_with_text_vertical_offset = 0x7f0700dd
com.iptv.android.dev:layout/mtrl_picker_text_input_date_range = 0x7f0d006a
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0e0092
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Bridge = 0x7f110066
com.iptv.android.dev:style/Theme.Material3.Dark.NoActionBar = 0x7f110268
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f11048b
com.iptv.android.dev:drawable/notification_bg_low_pressed = 0x7f08012e
com.iptv.android.dev:string/abc_activity_chooser_view_see_all = 0x7f100004
com.iptv.android.dev:dimen/tooltip_precise_anchor_extra_offset = 0x7f070340
com.iptv.android.dev:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1102e1
com.iptv.android.dev:styleable/MenuGroup = 0x7f12005e
com.iptv.android.dev:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f110327
com.iptv.android.dev:string/bottomsheet_drag_handle_clicked = 0x7f10003c
com.iptv.android.dev:id/jumpToStart = 0x7f0a010a
com.iptv.android.dev:style/Widget.Material3.Slider.Label = 0x7f110403
com.iptv.android.dev:integer/material_motion_duration_medium_2 = 0x7f0b002c
com.iptv.android.dev:styleable/StateListDrawableItem = 0x7f12008a
com.iptv.android.dev:styleable/PropertySet = 0x7f120078
com.iptv.android.dev:id/fitXY = 0x7f0a00e4
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1103eb
com.iptv.android.dev:style/ThemeOverlay.AppCompat = 0x7f1102af
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Year = 0x7f1103e0
com.iptv.android.dev:string/player_pause = 0x7f10015d
com.iptv.android.dev:string/search_no_results_suggestions = 0x7f10016c
com.iptv.android.dev:drawable/abc_action_bar_item_background_material = 0x7f080029
com.iptv.android.dev:string/content_download = 0x7f100058
com.iptv.android.dev:style/Base.Widget.Material3.Snackbar = 0x7f110110
com.iptv.android.dev:id/exo_buffering = 0x7f0a00ac
com.iptv.android.dev:style/Base.Theme.MaterialComponents = 0x7f110065
com.iptv.android.dev:style/Widget.MaterialComponents.Chip.Choice = 0x7f11043e
com.iptv.android.dev:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f11024b
com.iptv.android.dev:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1100d1
com.iptv.android.dev:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0e00f8
com.iptv.android.dev:style/Theme.Design = 0x7f110259
com.iptv.android.dev:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1103b2
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f1101b8
com.iptv.android.dev:id/NO_DEBUG = 0x7f0a0006
com.iptv.android.dev:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f11037a
com.iptv.android.dev:id/accessibility_custom_action_30 = 0x7f0a0028
com.iptv.android.dev:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0e0147
com.iptv.android.dev:id/mtrl_picker_header_title_and_selection = 0x7f0a014a
com.iptv.android.dev:dimen/mtrl_slider_label_square_side = 0x7f070304
com.iptv.android.dev:id/matrix = 0x7f0a012d
com.iptv.android.dev:string/time_format_less_minute = 0x7f10019b
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0e008c
com.iptv.android.dev:style/Theme.AppCompat.CompactMenu = 0x7f110244
com.iptv.android.dev:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f110448
com.iptv.android.dev:string/tooltip_pane_description = 0x7f1001aa
com.iptv.android.dev:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f0701a2
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110165
com.iptv.android.dev:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f11037b
com.iptv.android.dev:layout/design_navigation_item = 0x7f0d0024
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f110211
com.iptv.android.dev:layout/exo_styled_settings_list_item = 0x7f0d0034
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Headline4 = 0x7f110238
com.iptv.android.dev:string/mtrl_switch_track_path = 0x7f100140
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f110460
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f110461
com.iptv.android.dev:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f110228
com.iptv.android.dev:style/Widget.AppCompat.ActionButton.Overflow = 0x7f110328
com.iptv.android.dev:styleable/StateSet = 0x7f12008b
com.iptv.android.dev:dimen/abc_panel_menu_list_width = 0x7f070034
com.iptv.android.dev:macro/m3_comp_snackbar_supporting_text_color = 0x7f0e0116
com.iptv.android.dev:dimen/m3_simple_item_color_selected_alpha = 0x7f070205
com.iptv.android.dev:string/content_play_now = 0x7f10005c
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1100e5
com.iptv.android.dev:style/TextAppearance.Material3.DisplayMedium = 0x7f110220
com.iptv.android.dev:color/ripple_material_dark = 0x7f0602fd
com.iptv.android.dev:style/TextAppearance.AppCompat.Body1 = 0x7f1101c7
com.iptv.android.dev:xml/file_paths = 0x7f130002
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f110311
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f110215
com.iptv.android.dev:string/nav_kids = 0x7f100147
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f1102a4
com.iptv.android.dev:layout/notification_template_media = 0x7f0d0078
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1100cb
com.iptv.android.dev:id/stop = 0x7f0a01c7
com.iptv.android.dev:id/CTRL = 0x7f0a0003
com.iptv.android.dev:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f11032f
com.iptv.android.dev:id/chronometer = 0x7f0a0068
com.iptv.android.dev:style/TextAppearance.Design.Suffix = 0x7f110209
com.iptv.android.dev:style/Widget.Material3.Button.TonalButton = 0x7f110398
com.iptv.android.dev:styleable/AnimatedStateListDrawableItem = 0x7f120009
com.iptv.android.dev:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f110435
com.iptv.android.dev:styleable/MaterialTextAppearance = 0x7f12005a
com.iptv.android.dev:style/TextAppearance.Design.Prefix = 0x7f110207
com.iptv.android.dev:layout/exo_styled_player_control_view = 0x7f0d0031
com.iptv.android.dev:style/TextAppearance.Design.Counter.Overflow = 0x7f110202
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f11031f
com.iptv.android.dev:interpolator/m3_sys_motion_easing_emphasized = 0x7f0c0007
com.iptv.android.dev:styleable/SearchBar = 0x7f12007f
com.iptv.android.dev:styleable/LinearProgressIndicator = 0x7f12004a
com.iptv.android.dev:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f110087
com.iptv.android.dev:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f110051
com.iptv.android.dev:id/uniform = 0x7f0a01fc
com.iptv.android.dev:style/Animation.AppCompat.Tooltip = 0x7f110004
com.iptv.android.dev:style/TextAppearance.Design.Counter = 0x7f110201
com.iptv.android.dev:string/m3_ref_typeface_brand_medium = 0x7f1000df
com.iptv.android.dev:id/selected = 0x7f0a01a6
com.iptv.android.dev:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1100eb
com.iptv.android.dev:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f1102a9
com.iptv.android.dev:dimen/m3_comp_navigation_rail_icon_size = 0x7f070170
com.iptv.android.dev:macro/m3_comp_elevated_card_container_color = 0x7f0e002b
com.iptv.android.dev:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f110200
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f11033e
com.iptv.android.dev:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f110083
com.iptv.android.dev:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f110240
com.iptv.android.dev:id/channel_number = 0x7f0a0210
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f11028e
com.iptv.android.dev:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1102d3
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1103c9
com.iptv.android.dev:id/wrap_content = 0x7f0a020d
com.iptv.android.dev:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1101e1
com.iptv.android.dev:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f11011f
com.iptv.android.dev:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0e0134
com.iptv.android.dev:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701e8
com.iptv.android.dev:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1101d6
com.iptv.android.dev:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1101d3
com.iptv.android.dev:style/Theme.IPTVAndroid.SplashScreen = 0x7f110261
com.iptv.android.dev:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1101d2
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1103e8
com.iptv.android.dev:dimen/m3_divider_heavy_thickness = 0x7f0701cb
com.iptv.android.dev:layout/mtrl_picker_text_input_date = 0x7f0d0069
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f110464
com.iptv.android.dev:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f110093
com.iptv.android.dev:string/navigation_menu = 0x7f10014f
com.iptv.android.dev:style/Widget.Material3.CollapsingToolbar = 0x7f1103ae
com.iptv.android.dev:id/accessibility_custom_action_26 = 0x7f0a0023
com.iptv.android.dev:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0e00e4
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1103ec
com.iptv.android.dev:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f11024a
com.iptv.android.dev:style/TextAppearance.AppCompat.Body2 = 0x7f1101c8
com.iptv.android.dev:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.iptv.android.dev:id/invisible = 0x7f0a0104
com.iptv.android.dev:drawable/exo_styled_controls_repeat_off = 0x7f0800cd
com.iptv.android.dev:styleable/AspectRatioFrameLayout = 0x7f120014
com.iptv.android.dev:string/bottomsheet_action_expand_halfway = 0x7f10003b
com.iptv.android.dev:style/Platform.ThemeOverlay.AppCompat = 0x7f11016f
com.iptv.android.dev:style/Widget.Material3.BottomSheet.DragHandle = 0x7f110386
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1101c0
com.iptv.android.dev:string/player_stop = 0x7f100161
com.iptv.android.dev:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1100b7
com.iptv.android.dev:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f11005e
com.iptv.android.dev:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1100a0
com.iptv.android.dev:id/design_menu_item_text = 0x7f0a0089
com.iptv.android.dev:string/material_motion_easing_emphasized = 0x7f1000f8
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Medium = 0x7f110025
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f1101be
com.iptv.android.dev:dimen/mtrl_switch_thumb_size = 0x7f070314
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f1101ba
com.iptv.android.dev:style/TextAppearance.Design.Error = 0x7f110203
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1103ba
com.iptv.android.dev:style/Platform.Widget.AppCompat.Spinner = 0x7f110176
com.iptv.android.dev:dimen/splashscreen_icon_size = 0x7f07033a
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f11030d
com.iptv.android.dev:layout/material_timepicker_textinput_display = 0x7f0d004c
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionMode = 0x7f1100cd
com.iptv.android.dev:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f110420
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0e0061
com.iptv.android.dev:style/ShapeAppearance.Material3.LargeComponent = 0x7f1101a8
com.iptv.android.dev:string/settings_adult_content_subtitle = 0x7f10017a
com.iptv.android.dev:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f1101a1
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f11048d
com.iptv.android.dev:string/dropdown_menu = 0x7f100085
com.iptv.android.dev:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f11019f
com.iptv.android.dev:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f11010f
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f110198
com.iptv.android.dev:style/Widget.MaterialComponents.Badge = 0x7f110426
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1103e3
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f110193
com.iptv.android.dev:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1100d9
com.iptv.android.dev:style/CardView.Dark = 0x7f110127
com.iptv.android.dev:id/autoCompleteToEnd = 0x7f0a0051
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f11018c
com.iptv.android.dev:string/action_ok = 0x7f10001f
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom = 0x7f110134
com.iptv.android.dev:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f110177
com.iptv.android.dev:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1100a3
com.iptv.android.dev:style/Platform.V25.AppCompat = 0x7f110174
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f110456
com.iptv.android.dev:integer/mtrl_view_invisible = 0x7f0b0043
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f110166
com.iptv.android.dev:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f110106
com.iptv.android.dev:style/Theme.AppCompat.Light.DarkActionBar = 0x7f110252
com.iptv.android.dev:string/mtrl_exceed_max_badge_number_suffix = 0x7f100113
com.iptv.android.dev:string/exo_controls_time_placeholder = 0x7f1000ab
com.iptv.android.dev:style/DialogWindowTheme = 0x7f110129
com.iptv.android.dev:id/mtrl_anchor_parent = 0x7f0a013a
com.iptv.android.dev:style/Animation.Material3.BottomSheetDialog = 0x7f110006
com.iptv.android.dev:color/material_timepicker_clock_text_color = 0x7f0602ad
com.iptv.android.dev:string/settings_video_title = 0x7f100191
com.iptv.android.dev:dimen/m3_chip_corner_size = 0x7f070117
com.iptv.android.dev:string/exo_track_selection_title_audio = 0x7f1000c0
com.iptv.android.dev:style/Widget.MaterialComponents.Button.TextButton = 0x7f110433
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Headline6 = 0x7f11023a
com.iptv.android.dev:color/primary_material_dark = 0x7f0602f7
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Body.Text = 0x7f110157
com.iptv.android.dev:style/Base.Widget.Material3.Chip = 0x7f110104
com.iptv.android.dev:string/settings_privacy_policy = 0x7f100186
com.iptv.android.dev:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0e00e2
com.iptv.android.dev:macro/m3_comp_time_picker_headline_type = 0x7f0e0152
com.iptv.android.dev:style/Widget.AppCompat.SeekBar = 0x7f110360
com.iptv.android.dev:style/TextAppearance.Material3.DisplaySmall = 0x7f110221
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1103be
com.iptv.android.dev:style/FloatingDialogTheme = 0x7f11014a
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1102fa
com.iptv.android.dev:styleable/MotionTelltales = 0x7f120066
com.iptv.android.dev:style/ExoStyledControls.TimeText.Position = 0x7f110148
com.iptv.android.dev:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f11034b
com.iptv.android.dev:drawable/abc_vector_test = 0x7f080076
com.iptv.android.dev:integer/mtrl_chip_anim_duration = 0x7f0b0038
com.iptv.android.dev:styleable/ActivityNavigator = 0x7f120006
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f110314
com.iptv.android.dev:style/ExoStyledControls.Button.Center.Previous = 0x7f110143
com.iptv.android.dev:macro/m3_comp_elevated_card_container_shape = 0x7f0e002c
com.iptv.android.dev:style/ExoStyledControls.Button.Center = 0x7f11013f
com.iptv.android.dev:string/exo_track_selection_title_text = 0x7f1000c1
com.iptv.android.dev:dimen/material_clock_hand_padding = 0x7f070243
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.OverflowHide = 0x7f110138
com.iptv.android.dev:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f08007e
com.iptv.android.dev:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f110496
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f11018b
com.iptv.android.dev:id/status_bar_latest_event_content = 0x7f0a01c6
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f110480
com.iptv.android.dev:string/nav_anime = 0x7f100143
com.iptv.android.dev:dimen/m3_card_hovered_z = 0x7f07010e
com.iptv.android.dev:attr/background = 0x7f04004a
com.iptv.android.dev:string/error_network = 0x7f10008b
com.iptv.android.dev:style/TextAppearance.Material3.HeadlineMedium = 0x7f110223
com.iptv.android.dev:styleable/SearchView = 0x7f120080
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.FullScreen = 0x7f110137
com.iptv.android.dev:style/Widget.AppCompat.ActionBar.TabText = 0x7f110324
com.iptv.android.dev:id/forever = 0x7f0a00ea
com.iptv.android.dev:color/material_personalized_color_on_primary = 0x7f06027c
com.iptv.android.dev:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f110447
com.iptv.android.dev:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1103b1
com.iptv.android.dev:string/mtrl_picker_cancel = 0x7f100119
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f110466
com.iptv.android.dev:style/ExoMediaButton.VR = 0x7f110131
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f110347
com.iptv.android.dev:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1100d2
com.iptv.android.dev:style/ExoMediaButton.Rewind = 0x7f110130
com.iptv.android.dev:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1100ab
com.iptv.android.dev:styleable/State = 0x7f120088
com.iptv.android.dev:style/Base.Widget.AppCompat.Spinner = 0x7f1100f9
com.iptv.android.dev:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f08010d
com.iptv.android.dev:style/Widget.MaterialComponents.Snackbar = 0x7f110476
com.iptv.android.dev:macro/m3_comp_badge_large_label_text_color = 0x7f0e0003
com.iptv.android.dev:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f070156
com.iptv.android.dev:id/material_clock_period_am_button = 0x7f0a011e
com.iptv.android.dev:style/ExoMediaButton.Previous = 0x7f11012f
com.iptv.android.dev:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0e0054
com.iptv.android.dev:layout/design_navigation_menu_item = 0x7f0d0029
com.iptv.android.dev:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f110470
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1101c2
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f1101b4
com.iptv.android.dev:style/Widget.AppCompat.ImageButton = 0x7f11033a
com.iptv.android.dev:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0e009a
com.iptv.android.dev:styleable/OnSwipe = 0x7f120073
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0e016b
com.iptv.android.dev:dimen/m3_chip_icon_size = 0x7f07011c
com.iptv.android.dev:attr/colorOnContainer = 0x7f0400ef
com.iptv.android.dev:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f110120
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f11006f
com.iptv.android.dev:style/Base.Widget.MaterialComponents.Slider = 0x7f11011d
com.iptv.android.dev:style/TextAppearance.Material3.SearchView.Prefix = 0x7f11022b
com.iptv.android.dev:id/outward = 0x7f0a0173
com.iptv.android.dev:id/cancel_button = 0x7f0a005c
com.iptv.android.dev:macro/m3_comp_navigation_drawer_headline_color = 0x7f0e0087
com.iptv.android.dev:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0e016e
com.iptv.android.dev:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f07017d
com.iptv.android.dev:id/withText = 0x7f0a0209
com.iptv.android.dev:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f11009d
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f11028d
com.iptv.android.dev:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f110434
com.iptv.android.dev:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.iptv.android.dev:color/abc_hint_foreground_material_light = 0x7f060008
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f110414
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1102fd
com.iptv.android.dev:macro/m3_comp_dialog_headline_type = 0x7f0e0026
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1103d2
com.iptv.android.dev:color/notification_material_background_media_default_color = 0x7f0602e9
com.iptv.android.dev:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1103af
com.iptv.android.dev:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1100dc
com.iptv.android.dev:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f110114
com.iptv.android.dev:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f110112
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f11031c
com.iptv.android.dev:id/exo_time = 0x7f0a00d5
com.iptv.android.dev:layout/material_clockface_view = 0x7f0d0045
com.iptv.android.dev:id/mtrl_calendar_months = 0x7f0a013f
com.iptv.android.dev:styleable/AppBarLayout = 0x7f12000b
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f1101b5
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f1102a2
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f110168
com.iptv.android.dev:id/material_clock_period_pm_button = 0x7f0a011f
com.iptv.android.dev:style/TextAppearance.AppCompat.Menu = 0x7f1101d9
com.iptv.android.dev:string/player_loading = 0x7f10015c
com.iptv.android.dev:dimen/m3_comp_bottom_app_bar_container_height = 0x7f070125
com.iptv.android.dev:style/Widget.Material3.CompoundButton.Switch = 0x7f1103b4
com.iptv.android.dev:id/report_drawn = 0x7f0a0189
com.iptv.android.dev:string/live_tv_next = 0x7f1000d8
com.iptv.android.dev:id/inward = 0x7f0a0105
com.iptv.android.dev:style/Widget.AppCompat.CompoundButton.Switch = 0x7f110336
com.iptv.android.dev:drawable/notification_action_background = 0x7f08012a
com.iptv.android.dev:string/exo_download_paused = 0x7f1000b2
com.iptv.android.dev:color/on_primary_container = 0x7f0602ec
com.iptv.android.dev:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702e5
com.iptv.android.dev:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0e0043
com.iptv.android.dev:style/ThemeOverlay.Material3.Light = 0x7f1102e2
com.iptv.android.dev:style/Widget.AppCompat.Light.PopupMenu = 0x7f11034d
com.iptv.android.dev:id/spherical_gl_surface_view = 0x7f0a01b5
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1101bf
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f110459
com.iptv.android.dev:id/visible = 0x7f0a0206
com.iptv.android.dev:style/ExoStyledControls.Button.Center.Next = 0x7f110141
com.iptv.android.dev:layout/custom_dialog = 0x7f0d001c
com.iptv.android.dev:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1100f4
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f1102a6
com.iptv.android.dev:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f11010d
com.iptv.android.dev:string/language_dialog_message = 0x7f1000d2
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f110293
com.iptv.android.dev:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1100f1
com.iptv.android.dev:style/TextAppearance.Material3.TitleMedium = 0x7f11022d
com.iptv.android.dev:id/mtrl_picker_text_input_range_end = 0x7f0a014d
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents = 0x7f110094
com.iptv.android.dev:style/Theme.Material3.Light.Dialog.Alert = 0x7f110278
com.iptv.android.dev:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1100fe
com.iptv.android.dev:style/TextAppearance.AppCompat = 0x7f1101c6
com.iptv.android.dev:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0e0057
com.iptv.android.dev:style/Widget.Material3.Slider = 0x7f110402
com.iptv.android.dev:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f1101af
com.iptv.android.dev:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f110109
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1101ff
com.iptv.android.dev:string/material_timepicker_select_time = 0x7f100103
com.iptv.android.dev:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f11029e
com.iptv.android.dev:style/ExoStyledControls.TimeText.Duration = 0x7f110147
com.iptv.android.dev:style/Base.Widget.Material3.CardView = 0x7f110103
com.iptv.android.dev:style/Base.Widget.Material3.ActionBar.Solid = 0x7f110100
com.iptv.android.dev:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f11046a
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f11006b
com.iptv.android.dev:style/Base.AlertDialog.AppCompat = 0x7f11000b
com.iptv.android.dev:layout/mtrl_alert_select_dialog_multichoice = 0x7f0d0051
com.iptv.android.dev:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1102e5
com.iptv.android.dev:style/Widget.Material3.CardView.Elevated = 0x7f11039b
com.iptv.android.dev:style/Base.Widget.AppCompat.TextView = 0x7f1100fb
com.iptv.android.dev:dimen/material_helper_text_font_1_3_padding_top = 0x7f07025a
com.iptv.android.dev:style/Base.Animation.AppCompat.Tooltip = 0x7f11000f
com.iptv.android.dev:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f11007f
com.iptv.android.dev:style/Base.Widget.AppCompat.RatingBar = 0x7f1100f2
com.iptv.android.dev:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f11044a
com.iptv.android.dev:attr/floatingActionButtonSmallStyle = 0x7f0401d8
com.iptv.android.dev:drawable/exo_styled_controls_check = 0x7f0800c2
com.iptv.android.dev:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1100ee
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f1101bb
com.iptv.android.dev:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f110333
com.iptv.android.dev:string/exo_track_role_supplementary = 0x7f1000bd
com.iptv.android.dev:style/Base.Widget.AppCompat.EditText = 0x7f1100de
com.iptv.android.dev:id/position = 0x7f0a0181
com.iptv.android.dev:string/mtrl_checkbox_state_description_unchecked = 0x7f100110
com.iptv.android.dev:id/accessibility_custom_action_22 = 0x7f0a001f
com.iptv.android.dev:string/side_sheet_behavior = 0x7f100193
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.Button = 0x7f1101ec
com.iptv.android.dev:styleable/Navigator = 0x7f120071
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1100c9
com.iptv.android.dev:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f11004f
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1100c7
com.iptv.android.dev:style/Widget.AppCompat.Spinner.DropDown = 0x7f110363
com.iptv.android.dev:style/CardView = 0x7f110126
com.iptv.android.dev:string/loading_video = 0x7f1000dd
com.iptv.android.dev:style/Base.Widget.AppCompat.ButtonBar = 0x7f1100d6
com.iptv.android.dev:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1100c4
com.iptv.android.dev:string/mtrl_timepicker_confirm = 0x7f100142
com.iptv.android.dev:style/Base.V7.Theme.AppCompat = 0x7f1100bd
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f110316
com.iptv.android.dev:layout/abc_expanded_menu_layout = 0x7f0d000d
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f110076
com.iptv.android.dev:dimen/mtrl_slider_tick_radius = 0x7f070307
com.iptv.android.dev:color/m3_sys_color_dark_surface_container_highest = 0x7f06017e
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1101f3
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1103d1
com.iptv.android.dev:id/spread = 0x7f0a01b9
com.iptv.android.dev:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1100c1
com.iptv.android.dev:layout/mtrl_navigation_rail_item = 0x7f0d0060
com.iptv.android.dev:styleable/RadialViewGroup = 0x7f120079
com.iptv.android.dev:integer/exo_media_button_opacity_percentage_enabled = 0x7f0b000a
com.iptv.android.dev:style/Base.V7.Theme.AppCompat.Light = 0x7f1100bf
com.iptv.android.dev:id/mtrl_picker_text_input_date = 0x7f0a014c
com.iptv.android.dev:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1100b5
com.iptv.android.dev:string/mtrl_checkbox_button_icon_path_checked = 0x7f100106
com.iptv.android.dev:color/material_dynamic_secondary50 = 0x7f060248
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f11040e
com.iptv.android.dev:style/Theme.AppCompat.Dialog = 0x7f11024c
com.iptv.android.dev:style/Base.V23.Theme.AppCompat.Light = 0x7f1100b3
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Info = 0x7f1101f7
com.iptv.android.dev:id/message = 0x7f0a0130
com.iptv.android.dev:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1100ae
com.iptv.android.dev:style/Theme.AppCompat.DialogWhenLarge = 0x7f11024f
com.iptv.android.dev:styleable/AppBarLayout_Layout = 0x7f12000d
com.iptv.android.dev:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0e00b1
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11009c
com.iptv.android.dev:string/player_quality = 0x7f100160
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1103b6
com.iptv.android.dev:string/material_motion_easing_standard = 0x7f1000fa
com.iptv.android.dev:string/close_sheet = 0x7f100054
com.iptv.android.dev:style/Theme.Design.NoActionBar = 0x7f11025e
com.iptv.android.dev:string/mtrl_checkbox_button_icon_path_name = 0x7f100109
com.iptv.android.dev:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f11008e
com.iptv.android.dev:style/Widget.AppCompat.Button.Small = 0x7f110331
com.iptv.android.dev:style/Widget.Material3.ActionBar.Solid = 0x7f110377
com.iptv.android.dev:id/open_search_view_divider = 0x7f0a0168
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0e00c9
com.iptv.android.dev:attr/layout_goneMarginEnd = 0x7f040295
com.iptv.android.dev:drawable/exo_icon_repeat_all = 0x7f0800b0
com.iptv.android.dev:plurals/exo_controls_fastforward_by_amount_description = 0x7f0f0000
com.iptv.android.dev:style/Base.Theme.Material3.Light.Dialog = 0x7f110061
com.iptv.android.dev:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f11034c
com.iptv.android.dev:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f11008f
com.iptv.android.dev:string/player_fullscreen = 0x7f10015b
com.iptv.android.dev:string/mtrl_picker_range_header_only_end_selected = 0x7f100127
com.iptv.android.dev:drawable/tooltip_frame_light = 0x7f080139
com.iptv.android.dev:style/TextAppearance.AppCompat.Inverse = 0x7f1101d0
com.iptv.android.dev:id/exo_pause = 0x7f0a00c2
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f11023d
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0e0160
com.iptv.android.dev:dimen/exo_styled_progress_enabled_thumb_size = 0x7f0700af
com.iptv.android.dev:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f11004e
com.iptv.android.dev:style/Widget.Material3.SearchView = 0x7f1103fb
com.iptv.android.dev:integer/m3_sys_motion_path = 0x7f0b0022
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0e00d3
com.iptv.android.dev:dimen/abc_control_inset_material = 0x7f070019
com.iptv.android.dev:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f110047
com.iptv.android.dev:integer/m3_sys_motion_duration_medium2 = 0x7f0b001b
com.iptv.android.dev:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0b003d
com.iptv.android.dev:string/nav_search = 0x7f10014c
com.iptv.android.dev:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f110449
com.iptv.android.dev:style/Widget.Material3.Chip.Input = 0x7f1103a3
com.iptv.android.dev:style/TextAppearance.Material3.LabelLarge = 0x7f110225
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f11003f
com.iptv.android.dev:style/Base.V21.Theme.MaterialComponents = 0x7f1100a8
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f11015e
com.iptv.android.dev:drawable/$avd_show_password__1 = 0x7f080004
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f110343
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0e0073
com.iptv.android.dev:layout/design_text_input_end_icon = 0x7f0d002a
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f110033
com.iptv.android.dev:id/cradle = 0x7f0a007b
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f110032
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f110030
com.iptv.android.dev:style/Widget.Material3.TabLayout = 0x7f110407
com.iptv.android.dev:style/TextAppearance.Material3.TitleSmall = 0x7f11022e
com.iptv.android.dev:style/Widget.MaterialComponents.Toolbar = 0x7f110493
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f11002a
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f110026
com.iptv.android.dev:style/Widget.Material3.Button.Icon = 0x7f11038b
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f110024
com.iptv.android.dev:id/linear = 0x7f0a0112
com.iptv.android.dev:style/Widget.Design.NavigationView = 0x7f110371
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f11015a
com.iptv.android.dev:string/time_picker_minute_text_field = 0x7f1001a6
com.iptv.android.dev:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f11037d
com.iptv.android.dev:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700eb
com.iptv.android.dev:layout/abc_popup_menu_item_layout = 0x7f0d0013
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f110482
com.iptv.android.dev:style/Base.TextAppearance.AppCompat = 0x7f110016
com.iptv.android.dev:style/Platform.MaterialComponents.Light = 0x7f11016d
com.iptv.android.dev:layout/mtrl_picker_header_title_text = 0x7f0d0067
com.iptv.android.dev:style/Widget.Material3.SideSheet.Detached = 0x7f1103ff
com.iptv.android.dev:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110013
com.iptv.android.dev:style/ThemeOverlay.Material3.Snackbar = 0x7f1102f0
com.iptv.android.dev:style/Base.DialogWindowTitle.AppCompat = 0x7f110011
com.iptv.android.dev:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f110088
com.iptv.android.dev:style/Base.Theme.Material3.Light = 0x7f11005f
com.iptv.android.dev:style/Base.Animation.AppCompat.DropDownUp = 0x7f11000e
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f110483
com.iptv.android.dev:style/Base.AlertDialog.AppCompat.Light = 0x7f11000c
com.iptv.android.dev:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f07019b
com.iptv.android.dev:id/special_effects_controller_view_tag = 0x7f0a01b4
com.iptv.android.dev:id/scrollable = 0x7f0a019a
com.iptv.android.dev:style/Animation.AppCompat.DropDownUp = 0x7f110003
com.iptv.android.dev:string/date_picker_scroll_to_earlier_years = 0x7f10006f
com.iptv.android.dev:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f110058
com.iptv.android.dev:styleable/LinearLayoutCompat_Layout = 0x7f120049
com.iptv.android.dev:style/Widget.MaterialComponents.Chip.Action = 0x7f11043d
com.iptv.android.dev:styleable/MaterialButton = 0x7f12004f
com.iptv.android.dev:dimen/m3_btn_text_btn_icon_padding_left = 0x7f070101
com.iptv.android.dev:string/tooltip_long_press_label = 0x7f1001a9
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Display3 = 0x7f11001d
com.iptv.android.dev:style/Base.V21.Theme.AppCompat.Light = 0x7f1100a6
com.iptv.android.dev:color/mtrl_chip_text_color = 0x7f0602bf
com.iptv.android.dev:style/Widget.AppCompat.Button.Borderless = 0x7f11032d
com.iptv.android.dev:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0e014b
com.iptv.android.dev:styleable/MaterialDivider = 0x7f120056
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Headline = 0x7f11001f
com.iptv.android.dev:macro/m3_comp_slider_disabled_handle_color = 0x7f0e010e
com.iptv.android.dev:color/mtrl_navigation_item_icon_tint = 0x7f0602d0
com.iptv.android.dev:color/mtrl_btn_text_color_selector = 0x7f0602b6
com.iptv.android.dev:style/Theme.Material3.Dark.Dialog = 0x7f110264
com.iptv.android.dev:string/time_picker_period_toggle_description = 0x7f1001a7
com.iptv.android.dev:style/Widget.AppCompat.SeekBar.Discrete = 0x7f110361
com.iptv.android.dev:drawable/mtrl_ic_check_mark = 0x7f080114
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e0105
com.iptv.android.dev:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1103ca
com.iptv.android.dev:string/time_picker_minute = 0x7f1001a3
com.iptv.android.dev:string/time_picker_hour_suffix = 0x7f1001a1
com.iptv.android.dev:string/action_back = 0x7f10001b
com.iptv.android.dev:string/time_format_minutes = 0x7f10019c
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f110295
com.iptv.android.dev:id/action_menu_divider = 0x7f0a003c
com.iptv.android.dev:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0e00bd
com.iptv.android.dev:id/top = 0x7f0a01f0
com.iptv.android.dev:string/path_password_eye_mask_visible = 0x7f100156
com.iptv.android.dev:color/notification_icon_bg_color = 0x7f0602e8
com.iptv.android.dev:id/outline = 0x7f0a0172
com.iptv.android.dev:macro/m3_comp_filled_tonal_button_container_color = 0x7f0e0053
com.iptv.android.dev:dimen/notification_subtext_size = 0x7f070333
com.iptv.android.dev:id/all = 0x7f0a0047
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.OverflowShow = 0x7f110139
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Tooltip = 0x7f11023f
com.iptv.android.dev:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f11046b
com.iptv.android.dev:style/Theme.SplashScreen = 0x7f1102ac
com.iptv.android.dev:id/mtrl_picker_fullscreen = 0x7f0a0147
com.iptv.android.dev:string/exo_track_surround_7_point_1 = 0x7f1000c6
com.iptv.android.dev:id/textTop = 0x7f0a01e0
com.iptv.android.dev:id/enterAlways = 0x7f0a00a4
com.iptv.android.dev:styleable/FragmentContainerView = 0x7f12003a
com.iptv.android.dev:id/open_search_view_scrim = 0x7f0a016d
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1101f5
com.iptv.android.dev:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0e00da
com.iptv.android.dev:string/material_motion_easing_accelerated = 0x7f1000f6
com.iptv.android.dev:string/not_selected = 0x7f100150
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1103c1
com.iptv.android.dev:string/settings_terms_subtitle = 0x7f10018c
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1101e8
com.iptv.android.dev:styleable/ExtendedFloatingActionButton = 0x7f120031
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Display4 = 0x7f11001e
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Item = 0x7f1103dd
com.iptv.android.dev:string/settings_terms = 0x7f10018b
com.iptv.android.dev:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1101df
com.iptv.android.dev:dimen/exo_styled_progress_dragged_thumb_size = 0x7f0700ae
com.iptv.android.dev:string/mtrl_picker_date_header_selected = 0x7f10011b
com.iptv.android.dev:layout/mtrl_auto_complete_simple_item = 0x7f0d0053
com.iptv.android.dev:string/settings_language = 0x7f100181
com.iptv.android.dev:styleable/FloatingActionButton = 0x7f120033
com.iptv.android.dev:style/Platform.AppCompat.Light = 0x7f11016a
com.iptv.android.dev:string/settings_autoplay = 0x7f10017e
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Badge = 0x7f11022f
com.iptv.android.dev:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f110263
com.iptv.android.dev:styleable/MotionScene = 0x7f120065
com.iptv.android.dev:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0e00f1
com.iptv.android.dev:dimen/abc_text_size_display_2_material = 0x7f070044
com.iptv.android.dev:string/settings_audio = 0x7f10017d
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f11033c
com.iptv.android.dev:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f08011f
com.iptv.android.dev:string/mtrl_picker_today_description = 0x7f100134
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionBar = 0x7f1100c5
com.iptv.android.dev:string/settings_app_version = 0x7f10017c
com.iptv.android.dev:style/Base.Widget.AppCompat.PopupMenu = 0x7f1100ed
com.iptv.android.dev:string/settings_about_title = 0x7f100177
com.iptv.android.dev:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1102e7
com.iptv.android.dev:style/Widget.AppCompat.SearchView = 0x7f11035e
com.iptv.android.dev:layout/abc_select_dialog_material = 0x7f0d001a
com.iptv.android.dev:integer/cancel_button_image_alpha = 0x7f0b0004
com.iptv.android.dev:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0702ac
com.iptv.android.dev:string/search_searching = 0x7f100170
com.iptv.android.dev:macro/m3_comp_circular_progress_indicator_active_indicator_color = 0x7f0e000d
com.iptv.android.dev:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f07014b
com.iptv.android.dev:style/ThemeOverlay.AppCompat.Dark = 0x7f1102b1
com.iptv.android.dev:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f110443
com.iptv.android.dev:string/search_no_results = 0x7f10016a
com.iptv.android.dev:string/search_menu_title = 0x7f100169
com.iptv.android.dev:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1101d8
com.iptv.android.dev:layout/abc_popup_menu_header_item_layout = 0x7f0d0012
com.iptv.android.dev:dimen/mtrl_btn_text_btn_padding_right = 0x7f07028a
com.iptv.android.dev:string/range_start = 0x7f100166
com.iptv.android.dev:string/exo_controls_shuffle_off_description = 0x7f1000a8
com.iptv.android.dev:string/profile_title = 0x7f100164
com.iptv.android.dev:string/player_volume = 0x7f100163
com.iptv.android.dev:style/Base.Widget.AppCompat.ProgressBar = 0x7f1100f0
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f11018f
com.iptv.android.dev:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f110349
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f110184
com.iptv.android.dev:drawable/mtrl_navigation_bar_item_background = 0x7f080119
com.iptv.android.dev:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f11040c
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1103c2
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents = 0x7f11015f
com.iptv.android.dev:string/nav_profile = 0x7f10014b
com.iptv.android.dev:string/nav_movies = 0x7f100149
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Dark = 0x7f110305
com.iptv.android.dev:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f110091
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1101e5
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0e0062
com.iptv.android.dev:string/material_slider_range_start = 0x7f1000fc
com.iptv.android.dev:id/tag_unhandled_key_listeners = 0x7f0a01d8
com.iptv.android.dev:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700e5
com.iptv.android.dev:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1100ac
com.iptv.android.dev:string/mtrl_switch_thumb_path_checked = 0x7f10013a
com.iptv.android.dev:dimen/m3_comp_time_picker_container_elevation = 0x7f0701bd
com.iptv.android.dev:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1102c0
com.iptv.android.dev:string/mtrl_switch_thumb_group_name = 0x7f100139
com.iptv.android.dev:style/Base.Animation.AppCompat.Dialog = 0x7f11000d
com.iptv.android.dev:string/mtrl_picker_range_header_unselected = 0x7f10012b
com.iptv.android.dev:string/exo_controls_settings_description = 0x7f1000a6
com.iptv.android.dev:string/mtrl_picker_range_header_title = 0x7f10012a
com.iptv.android.dev:string/mtrl_picker_invalid_range = 0x7f100123
com.iptv.android.dev:style/ThemeOverlay.Material3.NavigationView = 0x7f1102ec
com.iptv.android.dev:string/mtrl_picker_end_date_description = 0x7f10011f
com.iptv.android.dev:style/Widget.AppCompat.PopupMenu = 0x7f110356
com.iptv.android.dev:string/mtrl_picker_day_of_week_column_header = 0x7f10011e
com.iptv.android.dev:dimen/m3_comp_filled_button_container_elevation = 0x7f070146
com.iptv.android.dev:string/mtrl_picker_date_header_unselected = 0x7f10011d
com.iptv.android.dev:style/Widget.AppCompat.Spinner = 0x7f110362
com.iptv.android.dev:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1100fa
com.iptv.android.dev:style/Theme.MaterialComponents.Light = 0x7f11029a
com.iptv.android.dev:attr/triggerSlack = 0x7f04049e
com.iptv.android.dev:dimen/exo_small_icon_padding_vertical = 0x7f0700a6
com.iptv.android.dev:string/mtrl_picker_confirm = 0x7f10011a
com.iptv.android.dev:string/material_slider_value = 0x7f1000fd
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0e0165
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0e0169
com.iptv.android.dev:layout/design_navigation_menu = 0x7f0d0028
com.iptv.android.dev:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f070151
com.iptv.android.dev:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701ed
com.iptv.android.dev:id/submit_area = 0x7f0a01ca
com.iptv.android.dev:string/bottomsheet_action_expand = 0x7f10003a
com.iptv.android.dev:style/Base.V21.Theme.AppCompat = 0x7f1100a4
com.iptv.android.dev:string/material_motion_easing_decelerated = 0x7f1000f7
com.iptv.android.dev:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080052
com.iptv.android.dev:attr/materialCalendarMonthNavigationButton = 0x7f0402d2
com.iptv.android.dev:color/material_dynamic_neutral30 = 0x7f06021f
com.iptv.android.dev:id/accessibility_custom_action_6 = 0x7f0a002c
com.iptv.android.dev:string/settings_devices = 0x7f100180
com.iptv.android.dev:id/src_over = 0x7f0a01be
com.iptv.android.dev:string/material_hour_selection = 0x7f1000f2
com.iptv.android.dev:string/material_hour_24h_suffix = 0x7f1000f1
com.iptv.android.dev:string/material_clock_toggle_content_description = 0x7f1000f0
com.iptv.android.dev:style/ThemeOverlay.Material3.Button = 0x7f1102c4
com.iptv.android.dev:string/material_clock_display_divider = 0x7f1000ef
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1103ed
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1103c4
com.iptv.android.dev:string/m3c_bottom_sheet_pane_title = 0x7f1000ee
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f11045b
com.iptv.android.dev:styleable/MaterialCardView = 0x7f120053
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1101fd
com.iptv.android.dev:string/m3_sys_motion_easing_legacy = 0x7f1000e7
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Display2 = 0x7f11001c
com.iptv.android.dev:style/TextAppearance.AppCompat.Large = 0x7f1101d1
com.iptv.android.dev:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f06029f
com.iptv.android.dev:string/live_tv_now_playing = 0x7f1000d9
com.iptv.android.dev:style/Widget.AppCompat.ActionBar = 0x7f110321
com.iptv.android.dev:style/Widget.AppCompat.Toolbar = 0x7f110368
com.iptv.android.dev:string/nav_home = 0x7f100146
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker = 0x7f110489
com.iptv.android.dev:id/fitToContents = 0x7f0a00e3
com.iptv.android.dev:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0e00e0
com.iptv.android.dev:string/language_restart_required = 0x7f1000d5
com.iptv.android.dev:id/embed = 0x7f0a00a0
com.iptv.android.dev:string/player_forward = 0x7f10015a
com.iptv.android.dev:string/item_view_role_description = 0x7f1000d0
com.iptv.android.dev:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1100a1
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f11009a
com.iptv.android.dev:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0e00de
com.iptv.android.dev:id/on = 0x7f0a0162
com.iptv.android.dev:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701c4
com.iptv.android.dev:string/abc_action_menu_overflow_description = 0x7f100002
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Headline3 = 0x7f110237
com.iptv.android.dev:string/fab_transformation_sheet_behavior = 0x7f1000cb
com.iptv.android.dev:style/ThemeOverlay.AppCompat.DayNight = 0x7f1102b3
com.iptv.android.dev:styleable/Transform = 0x7f120098
com.iptv.android.dev:string/expanded = 0x7f1000c8
com.iptv.android.dev:id/exo_play_pause = 0x7f0a00c4
com.iptv.android.dev:string/date_input_invalid_year_range = 0x7f100067
com.iptv.android.dev:dimen/highlight_alpha_material_colored = 0x7f0700b6
com.iptv.android.dev:layout/material_radial_view_group = 0x7f0d0046
com.iptv.android.dev:style/Theme.MaterialComponents.NoActionBar = 0x7f1102aa
com.iptv.android.dev:string/exo_track_selection_auto = 0x7f1000be
com.iptv.android.dev:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0e0119
com.iptv.android.dev:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0e0096
com.iptv.android.dev:string/exo_track_bitrate = 0x7f1000b7
com.iptv.android.dev:style/ExoStyledControls.Button.Center.RewWithAmount = 0x7f110144
com.iptv.android.dev:string/mtrl_picker_start_date_description = 0x7f10012d
com.iptv.android.dev:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f110248
com.iptv.android.dev:string/exo_download_paused_for_wifi = 0x7f1000b4
com.iptv.android.dev:string/exo_download_notification_channel_name = 0x7f1000b1
com.iptv.android.dev:style/Widget.Design.TextInputLayout = 0x7f110376
com.iptv.android.dev:string/exo_controls_stop_description = 0x7f1000aa
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f110454
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f110481
com.iptv.android.dev:string/exo_controls_shuffle_on_description = 0x7f1000a9
com.iptv.android.dev:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0e0123
com.iptv.android.dev:string/exo_controls_repeat_all_description = 0x7f1000a1
com.iptv.android.dev:id/expand_activities_button = 0x7f0a00d8
com.iptv.android.dev:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080061
com.iptv.android.dev:layout/mtrl_calendar_year = 0x7f0d005d
com.iptv.android.dev:string/mtrl_picker_announce_current_selection = 0x7f100117
com.iptv.android.dev:styleable/ChipGroup = 0x7f12001f
com.iptv.android.dev:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1102c8
com.iptv.android.dev:string/exo_controls_playback_speed = 0x7f10009f
com.iptv.android.dev:style/Theme.Design.Light.NoActionBar = 0x7f11025d
com.iptv.android.dev:drawable/tooltip_frame_dark = 0x7f080138
com.iptv.android.dev:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f110297
com.iptv.android.dev:string/settings_video_quality_subtitle = 0x7f100190
com.iptv.android.dev:dimen/m3_comp_snackbar_container_elevation = 0x7f0701a6
com.iptv.android.dev:string/exo_controls_play_description = 0x7f10009e
com.iptv.android.dev:string/exo_controls_overflow_hide_description = 0x7f10009b
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f110189
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f110036
com.iptv.android.dev:style/Theme.IPTVAndroid = 0x7f11025f
com.iptv.android.dev:id/path = 0x7f0a017b
com.iptv.android.dev:id/design_menu_item_action_area_stub = 0x7f0a0088
com.iptv.android.dev:string/exo_controls_fullscreen_exit_description = 0x7f100098
com.iptv.android.dev:style/Base.v27.Theme.SplashScreen = 0x7f110124
com.iptv.android.dev:drawable/m3_selection_control_ripple = 0x7f0800f5
com.iptv.android.dev:string/exo_controls_custom_playback_speed = 0x7f100095
com.iptv.android.dev:dimen/design_bottom_navigation_icon_size = 0x7f070064
com.iptv.android.dev:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700e1
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f110294
com.iptv.android.dev:string/exo_controls_cc_disabled_description = 0x7f100093
com.iptv.android.dev:drawable/mtrl_switch_thumb_checked_pressed = 0x7f08011e
com.iptv.android.dev:string/error_unknown = 0x7f100092
com.iptv.android.dev:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0e011f
com.iptv.android.dev:string/error_unauthorized = 0x7f100091
com.iptv.android.dev:style/Base.v27.Theme.SplashScreen.Light = 0x7f110125
com.iptv.android.dev:string/error_retry = 0x7f10008e
com.iptv.android.dev:id/time = 0x7f0a01eb
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f110288
com.iptv.android.dev:style/TextAppearance.AppCompat.Display4 = 0x7f1101ce
com.iptv.android.dev:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0e00ba
com.iptv.android.dev:layout/mtrl_alert_dialog_title = 0x7f0d004f
com.iptv.android.dev:string/exo_controls_show = 0x7f1000a7
com.iptv.android.dev:string/dialog = 0x7f100084
com.iptv.android.dev:id/action0 = 0x7f0a0030
com.iptv.android.dev:string/date_range_picker_scroll_to_previous_month = 0x7f10007f
com.iptv.android.dev:string/time_picker_hour = 0x7f10019e
com.iptv.android.dev:string/date_range_picker_end_headline = 0x7f10007d
com.iptv.android.dev:style/Base.Widget.Material3.TabLayout = 0x7f110111
com.iptv.android.dev:style/Widget.Material3.Snackbar.TextView = 0x7f110406
com.iptv.android.dev:string/date_range_input_title = 0x7f10007b
com.iptv.android.dev:string/date_picker_switch_to_year_selection = 0x7f100076
com.iptv.android.dev:color/material_dynamic_neutral95 = 0x7f060226
com.iptv.android.dev:string/date_picker_switch_to_next_month = 0x7f100074
com.iptv.android.dev:id/clip_horizontal = 0x7f0a006b
com.iptv.android.dev:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1102c3
com.iptv.android.dev:style/Theme.Material3.Light.Dialog = 0x7f110277
com.iptv.android.dev:string/date_input_title = 0x7f10006a
com.iptv.android.dev:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080025
com.iptv.android.dev:layout/material_textinput_timepicker = 0x7f0d0047
com.iptv.android.dev:id/clip_vertical = 0x7f0a006c
com.iptv.android.dev:id/month_navigation_previous = 0x7f0a0137
com.iptv.android.dev:style/Widget.Material3.BottomAppBar.Legacy = 0x7f110382
com.iptv.android.dev:string/date_picker_navigate_to_year_description = 0x7f10006d
com.iptv.android.dev:string/date_input_no_input_description = 0x7f100069
com.iptv.android.dev:id/mtrl_calendar_selection_frame = 0x7f0a0140
com.iptv.android.dev:string/date_input_label = 0x7f100068
com.iptv.android.dev:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f110397
com.iptv.android.dev:color/material_slider_active_track_color = 0x7f0602a6
com.iptv.android.dev:dimen/mtrl_alert_dialog_background_inset_end = 0x7f070263
com.iptv.android.dev:color/material_dynamic_neutral10 = 0x7f06021c
com.iptv.android.dev:string/m3_sys_motion_easing_emphasized_path_data = 0x7f1000e6
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f1102a3
com.iptv.android.dev:string/content_recommended_series = 0x7f10005f
com.iptv.android.dev:style/Widget.AppCompat.EditText = 0x7f110339
com.iptv.android.dev:string/loading_search = 0x7f1000dc
com.iptv.android.dev:string/settings_subtitles_subtitle = 0x7f10018a
com.iptv.android.dev:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f110367
com.iptv.android.dev:string/content_continue_watching = 0x7f100057
com.iptv.android.dev:integer/m3_sys_shape_corner_large_corner_family = 0x7f0b0026
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1103cf
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f110303
com.iptv.android.dev:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f11029d
com.iptv.android.dev:id/aligned = 0x7f0a0046
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1103bb
com.iptv.android.dev:style/Base.Widget.Material3.FloatingActionButton = 0x7f11010b
com.iptv.android.dev:string/clear_text_end_icon_content_description = 0x7f100052
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f110307
com.iptv.android.dev:string/character_counter_pattern = 0x7f100051
com.iptv.android.dev:macro/m3_comp_filled_button_label_text_color = 0x7f0e0045
com.iptv.android.dev:dimen/abc_text_size_caption_material = 0x7f070042
com.iptv.android.dev:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f1101b2
com.iptv.android.dev:string/cd_back_button = 0x7f100046
com.iptv.android.dev:drawable/notify_panel_notification_icon_bg = 0x7f080136
com.iptv.android.dev:style/TextAppearance.AppCompat.Small = 0x7f1101dc
com.iptv.android.dev:attr/listPreferredItemHeight = 0x7f0402af
com.iptv.android.dev:dimen/exo_styled_bottom_bar_margin_top = 0x7f0700a9
com.iptv.android.dev:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f11042c
com.iptv.android.dev:string/mtrl_chip_close_icon_content_description = 0x7f100111
com.iptv.android.dev:string/call_notification_hang_up_action = 0x7f100041
com.iptv.android.dev:integer/bottom_sheet_slide_duration = 0x7f0b0003
com.iptv.android.dev:drawable/exo_icon_shuffle_off = 0x7f0800b4
com.iptv.android.dev:string/call_notification_answer_action = 0x7f10003e
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f110179
com.iptv.android.dev:style/IPTVText.Headline = 0x7f110153
com.iptv.android.dev:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0e00ae
com.iptv.android.dev:string/bottom_sheet_drag_handle_description = 0x7f100037
com.iptv.android.dev:string/bottom_sheet_collapse_description = 0x7f100035
com.iptv.android.dev:style/Widget.Design.BottomNavigationView = 0x7f11036d
com.iptv.android.dev:macro/m3_comp_extended_fab_primary_container_color = 0x7f0e002d
com.iptv.android.dev:drawable/exo_controls_repeat_one = 0x7f080091
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_surface = 0x7f0601ac
com.iptv.android.dev:string/auth_welcome = 0x7f100032
com.iptv.android.dev:id/cancel_action = 0x7f0a005b
com.iptv.android.dev:string/auth_activating = 0x7f10002a
com.iptv.android.dev:string/auth_activate_device = 0x7f100028
com.iptv.android.dev:string/androidx_startup = 0x7f100023
com.iptv.android.dev:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0800fe
com.iptv.android.dev:string/action_retry = 0x7f100020
com.iptv.android.dev:string/abc_shareactionprovider_share_with = 0x7f100018
com.iptv.android.dev:string/abc_searchview_description_submit = 0x7f100016
com.iptv.android.dev:string/abc_searchview_description_query = 0x7f100014
com.iptv.android.dev:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1100a5
com.iptv.android.dev:string/abc_searchview_description_clear = 0x7f100013
com.iptv.android.dev:id/navigation_bar_item_active_indicator_view = 0x7f0a0153
com.iptv.android.dev:style/Widget.Material3.Button.TonalButton.Icon = 0x7f110399
com.iptv.android.dev:string/settings_about = 0x7f100176
com.iptv.android.dev:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f110254
com.iptv.android.dev:layout/ime_base_split_test_activity = 0x7f0d0037
com.iptv.android.dev:string/abc_prepend_shortcut_label = 0x7f100011
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f11030e
com.iptv.android.dev:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f110431
com.iptv.android.dev:string/abc_menu_enter_shortcut_label = 0x7f10000b
com.iptv.android.dev:id/material_clock_hand = 0x7f0a011c
com.iptv.android.dev:id/navigation_bar_item_large_label_view = 0x7f0a0157
com.iptv.android.dev:attr/show_shuffle_button = 0x7f0403b7
com.iptv.android.dev:dimen/m3_searchbar_height = 0x7f0701f6
com.iptv.android.dev:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f110369
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f11028f
com.iptv.android.dev:drawable/mtrl_ic_indeterminate = 0x7f080118
com.iptv.android.dev:layout/m3_auto_complete_simple_item = 0x7f0d003d
com.iptv.android.dev:string/abc_menu_delete_shortcut_label = 0x7f10000a
com.iptv.android.dev:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f11011a
com.iptv.android.dev:string/abc_menu_alt_shortcut_label = 0x7f100008
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f110073
com.iptv.android.dev:string/abc_action_bar_up_description = 0x7f100001
com.iptv.android.dev:dimen/design_bottom_sheet_modal_elevation = 0x7f07006c
com.iptv.android.dev:layout/material_clock_period_toggle_land = 0x7f0d0043
com.iptv.android.dev:plurals/mtrl_badge_content_description = 0x7f0f0002
com.iptv.android.dev:macro/m3_sys_color_light_surface_tint = 0x7f0e0176
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f110159
com.iptv.android.dev:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0e00ed
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0e016a
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0e0161
com.iptv.android.dev:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1102e8
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0e015f
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0e015a
com.iptv.android.dev:string/mtrl_picker_toggle_to_text_input_mode = 0x7f100137
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f110315
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0e0154
com.iptv.android.dev:style/Widget.Material3.Button.IconButton.Outlined = 0x7f11038f
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f110039
com.iptv.android.dev:id/exo_controller = 0x7f0a00b0
com.iptv.android.dev:style/Widget.Material3.AppBarLayout = 0x7f110379
com.iptv.android.dev:style/Widget.Design.TextInputEditText = 0x7f110375
com.iptv.android.dev:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0e0144
com.iptv.android.dev:macro/m3_comp_switch_unselected_track_color = 0x7f0e0141
com.iptv.android.dev:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0e013e
com.iptv.android.dev:style/TextAppearance.AppCompat.Caption = 0x7f1101ca
com.iptv.android.dev:layout/mtrl_alert_dialog_actions = 0x7f0d004e
com.iptv.android.dev:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f120032
com.iptv.android.dev:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0e013c
com.iptv.android.dev:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0e0139
com.iptv.android.dev:dimen/m3_comp_outlined_card_container_elevation = 0x7f070175
com.iptv.android.dev:macro/m3_comp_text_button_label_text_type = 0x7f0e0146
com.iptv.android.dev:dimen/mtrl_badge_text_size = 0x7f07026b
com.iptv.android.dev:string/auth_register_device = 0x7f10002f
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.PlaybackSpeed = 0x7f11013a
com.iptv.android.dev:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0e0136
com.iptv.android.dev:color/primary_text_default_material_dark = 0x7f0602f9
com.iptv.android.dev:macro/m3_comp_suggestion_chip_container_shape = 0x7f0e0118
com.iptv.android.dev:id/password_toggle = 0x7f0a017a
com.iptv.android.dev:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0e0130
com.iptv.android.dev:id/textinput_helper_text = 0x7f0a01e6
com.iptv.android.dev:macro/m3_comp_switch_selected_track_color = 0x7f0e012f
com.iptv.android.dev:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0e00d6
com.iptv.android.dev:style/TextAppearance.Material3.HeadlineLarge = 0x7f110222
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0e007c
com.iptv.android.dev:dimen/m3_btn_disabled_translation_z = 0x7f0700f1
com.iptv.android.dev:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0e012d
com.iptv.android.dev:style/Base.ThemeOverlay.AppCompat = 0x7f11007b
com.iptv.android.dev:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1100ba
com.iptv.android.dev:dimen/m3_card_elevated_dragged_z = 0x7f07010a
com.iptv.android.dev:macro/m3_comp_switch_selected_icon_color = 0x7f0e012a
com.iptv.android.dev:id/open_search_view_status_bar_spacer = 0x7f0a016f
com.iptv.android.dev:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f070145
com.iptv.android.dev:styleable/SideSheetBehavior_Layout = 0x7f120083
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f110034
com.iptv.android.dev:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700e0
com.iptv.android.dev:style/Widget.Material3.Button = 0x7f110388
com.iptv.android.dev:macro/m3_comp_switch_selected_hover_track_color = 0x7f0e0129
com.iptv.android.dev:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0e0128
com.iptv.android.dev:interpolator/fast_out_slow_in = 0x7f0c0006
com.iptv.android.dev:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0e0122
com.iptv.android.dev:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0e0120
com.iptv.android.dev:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0e011d
com.iptv.android.dev:string/exo_download_completed = 0x7f1000ad
com.iptv.android.dev:dimen/m3_comp_slider_inactive_track_height = 0x7f0701a5
com.iptv.android.dev:macro/m3_comp_snackbar_supporting_text_type = 0x7f0e0117
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1103b7
com.iptv.android.dev:layout/notification_action_tombstone = 0x7f0d006e
com.iptv.android.dev:macro/m3_comp_snackbar_container_color = 0x7f0e0114
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f110340
com.iptv.android.dev:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0e0108
com.iptv.android.dev:string/exo_track_selection_title_video = 0x7f1000c2
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0e0102
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0e00fd
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f110160
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f110074
com.iptv.android.dev:style/Widget.MaterialComponents.NavigationRailView = 0x7f110469
com.iptv.android.dev:macro/m3_comp_divider_color = 0x7f0e0029
com.iptv.android.dev:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f110055
com.iptv.android.dev:macro/m3_comp_search_view_header_input_text_color = 0x7f0e00f6
com.iptv.android.dev:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0e016c
com.iptv.android.dev:drawable/notification_bg_normal = 0x7f08012f
com.iptv.android.dev:dimen/m3_comp_fab_primary_large_container_height = 0x7f07013f
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0e00fe
com.iptv.android.dev:style/ExoStyledControls = 0x7f110132
com.iptv.android.dev:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1102bb
com.iptv.android.dev:id/exo_subtitle = 0x7f0a00d2
com.iptv.android.dev:style/TextAppearance.AppCompat.Title = 0x7f1101e0
com.iptv.android.dev:string/error_device_registration = 0x7f100088
com.iptv.android.dev:style/Base.V23.Theme.AppCompat = 0x7f1100b2
com.iptv.android.dev:macro/m3_comp_search_view_container_color = 0x7f0e00f2
com.iptv.android.dev:attr/badgeWidth = 0x7f040060
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f07022a
com.iptv.android.dev:macro/m3_comp_search_bar_input_text_type = 0x7f0e00eb
com.iptv.android.dev:string/mtrl_picker_announce_current_range_selection = 0x7f100116
com.iptv.android.dev:layout/item_channel = 0x7f0d0039
com.iptv.android.dev:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0e00e1
com.iptv.android.dev:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0e00df
com.iptv.android.dev:style/Base.V24.Theme.Material3.Light = 0x7f1100b6
com.iptv.android.dev:id/exo_vr = 0x7f0a00d7
com.iptv.android.dev:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f110428
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Headline5 = 0x7f110239
com.iptv.android.dev:string/m3_sys_motion_easing_legacy_decelerate = 0x7f1000e9
com.iptv.android.dev:id/sin = 0x7f0a01ac
com.iptv.android.dev:string/default_error_message = 0x7f100082
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0e00d5
com.iptv.android.dev:drawable/exo_styled_controls_repeat_one = 0x7f0800ce
com.iptv.android.dev:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f070198
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0e00d4
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0e00cf
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0e00ca
com.iptv.android.dev:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0e00c8
com.iptv.android.dev:string/range_end = 0x7f100165
com.iptv.android.dev:macro/m3_comp_outlined_text_field_outline_color = 0x7f0e00c5
com.iptv.android.dev:macro/m3_comp_time_picker_container_shape = 0x7f0e0150
com.iptv.android.dev:macro/m3_comp_time_picker_headline_color = 0x7f0e0151
com.iptv.android.dev:style/Widget.MaterialComponents.ShapeableImageView = 0x7f110474
com.iptv.android.dev:id/scrollView = 0x7f0a0199
com.iptv.android.dev:style/Widget.AppCompat.ActionBar.TabBar = 0x7f110323
com.iptv.android.dev:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0e00b9
com.iptv.android.dev:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0e0138
com.iptv.android.dev:macro/m3_comp_outlined_text_field_container_shape = 0x7f0e00b3
com.iptv.android.dev:drawable/abc_ic_menu_overflow_material = 0x7f080044
com.iptv.android.dev:color/material_personalized_color_surface_dim = 0x7f060297
com.iptv.android.dev:styleable/AlertDialog = 0x7f120007
com.iptv.android.dev:id/center_vertical = 0x7f0a0061
com.iptv.android.dev:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f11029b
com.iptv.android.dev:color/material_on_surface_disabled = 0x7f06026d
com.iptv.android.dev:macro/m3_comp_outlined_card_container_color = 0x7f0e00aa
com.iptv.android.dev:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0e00a7
com.iptv.android.dev:styleable/FlowLayout = 0x7f120035
com.iptv.android.dev:layout/mtrl_picker_actions = 0x7f0d0061
com.iptv.android.dev:macro/m3_comp_text_button_label_text_color = 0x7f0e0145
com.iptv.android.dev:string/auth_welcome_subtitle = 0x7f100033
com.iptv.android.dev:id/month_title = 0x7f0a0138
com.iptv.android.dev:string/mtrl_checkbox_state_description_checked = 0x7f10010e
com.iptv.android.dev:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0e00a6
com.iptv.android.dev:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0e00a5
com.iptv.android.dev:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1100db
com.iptv.android.dev:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0e009f
com.iptv.android.dev:string/mtrl_picker_invalid_format_use = 0x7f100122
com.iptv.android.dev:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0e009d
com.iptv.android.dev:id/open_search_view_header_container = 0x7f0a016b
com.iptv.android.dev:integer/abc_config_activityShortDur = 0x7f0b0001
com.iptv.android.dev:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0e0094
com.iptv.android.dev:style/Base.Theme.AppCompat.Light.Dialog = 0x7f110054
com.iptv.android.dev:layout/material_time_chip = 0x7f0d0048
com.iptv.android.dev:id/ignore = 0x7f0a00fe
com.iptv.android.dev:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1102e9
com.iptv.android.dev:style/ExoStyledControls.TimeText.Separator = 0x7f110149
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0e0093
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0e008e
com.iptv.android.dev:dimen/m3_card_elevated_disabled_z = 0x7f070109
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_background = 0x7f060189
com.iptv.android.dev:color/material_dynamic_primary40 = 0x7f06023a
com.iptv.android.dev:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1102ed
com.iptv.android.dev:attr/materialCalendarHeaderCancelButton = 0x7f0402ca
com.iptv.android.dev:color/mtrl_btn_stroke_color_selector = 0x7f0602b2
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f11020d
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f110465
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0e008d
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0e0089
com.iptv.android.dev:style/Widget.MaterialComponents.BottomAppBar = 0x7f110427
com.iptv.android.dev:style/IPTVText.Caption = 0x7f110152
com.iptv.android.dev:drawable/mtrl_ic_checkbox_checked = 0x7f080115
com.iptv.android.dev:dimen/mtrl_navigation_rail_text_size = 0x7f0702ef
com.iptv.android.dev:drawable/abc_ic_go_search_api_material = 0x7f080041
com.iptv.android.dev:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f11006d
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0e0085
com.iptv.android.dev:layout/m3_alert_dialog_actions = 0x7f0d003b
com.iptv.android.dev:id/exo_overlay = 0x7f0a00c1
com.iptv.android.dev:string/m3_exceed_max_badge_text_suffix = 0x7f1000de
com.iptv.android.dev:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f1103ab
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0e0082
com.iptv.android.dev:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f110338
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0e0064
com.iptv.android.dev:macro/m3_comp_outlined_card_container_shape = 0x7f0e00ab
com.iptv.android.dev:style/Widget.AppCompat.ActionBar.Solid = 0x7f110322
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0e007e
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Title = 0x7f1101fe
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0e007d
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0e006a
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0e007b
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0e007a
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0e0101
com.iptv.android.dev:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1100f8
com.iptv.android.dev:macro/m3_comp_navigation_bar_label_text_type = 0x7f0e0079
com.iptv.android.dev:string/call_notification_decline_action = 0x7f100040
com.iptv.android.dev:style/Base.Widget.AppCompat.SearchView = 0x7f1100f5
com.iptv.android.dev:style/TextAppearance.AppCompat.Headline = 0x7f1101cf
com.iptv.android.dev:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.iptv.android.dev:dimen/m3_btn_text_btn_padding_left = 0x7f070103
com.iptv.android.dev:id/navigation_bar_item_icon_container = 0x7f0a0154
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar = 0x7f1103cd
com.iptv.android.dev:string/m3_ref_typeface_brand_regular = 0x7f1000e0
com.iptv.android.dev:string/appbar_scrolling_view_behavior = 0x7f100026
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0e0077
com.iptv.android.dev:dimen/mtrl_calendar_header_text_padding = 0x7f0702a0
com.iptv.android.dev:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700cb
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0e006c
com.iptv.android.dev:string/exo_controls_hide = 0x7f100099
com.iptv.android.dev:id/m3_side_sheet = 0x7f0a0115
com.iptv.android.dev:style/Widget.MaterialComponents.Button = 0x7f11042f
com.iptv.android.dev:integer/m3_sys_motion_duration_short4 = 0x7f0b0021
com.iptv.android.dev:string/abc_shareactionprovider_share_with_application = 0x7f100019
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0e006b
com.iptv.android.dev:string/settings_profile = 0x7f100188
com.iptv.android.dev:layout/mtrl_picker_header_selection_text = 0x7f0d0066
com.iptv.android.dev:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1100a7
com.iptv.android.dev:id/barrier = 0x7f0a0053
com.iptv.android.dev:style/ExoMediaButton.Next = 0x7f11012c
com.iptv.android.dev:style/Widget.Material3.CheckedTextView = 0x7f11039e
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Button = 0x7f110232
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0e0069
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f110217
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0e0066
com.iptv.android.dev:macro/m3_comp_linear_progress_indicator_active_indicator_color = 0x7f0e005e
com.iptv.android.dev:string/time_picker_hour_24h_suffix = 0x7f10019f
com.iptv.android.dev:macro/m3_comp_input_chip_label_text_type = 0x7f0e005d
com.iptv.android.dev:macro/m3_comp_input_chip_container_shape = 0x7f0e005c
com.iptv.android.dev:id/row_index_key = 0x7f0a0190
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f07021f
com.iptv.android.dev:layout/mtrl_picker_fullscreen = 0x7f0d0063
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f110491
com.iptv.android.dev:styleable/AppCompatSeekBar = 0x7f120010
com.iptv.android.dev:macro/m3_comp_icon_button_selected_icon_color = 0x7f0e005a
com.iptv.android.dev:id/pin = 0x7f0a017f
com.iptv.android.dev:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0e00c1
com.iptv.android.dev:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0e0055
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Day = 0x7f1103ce
com.iptv.android.dev:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1100a9
com.iptv.android.dev:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1103e9
com.iptv.android.dev:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0c0008
com.iptv.android.dev:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0e0052
com.iptv.android.dev:style/FloatingDialogWindowTheme = 0x7f11014b
com.iptv.android.dev:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f11034a
com.iptv.android.dev:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f110364
com.iptv.android.dev:macro/m3_comp_filled_text_field_input_text_type = 0x7f0e0051
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0e015c
com.iptv.android.dev:id/mtrl_card_checked_layer_id = 0x7f0a0143
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Overline = 0x7f11023b
com.iptv.android.dev:string/time_picker_minute_suffix = 0x7f1001a5
com.iptv.android.dev:macro/m3_comp_filled_text_field_container_color = 0x7f0e004c
com.iptv.android.dev:string/nav_favorites = 0x7f100145
com.iptv.android.dev:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000
com.iptv.android.dev:styleable/AnimatedStateListDrawableCompat = 0x7f120008
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0e0090
com.iptv.android.dev:macro/m3_comp_filled_button_container_color = 0x7f0e0044
com.iptv.android.dev:styleable/MaterialSwitch = 0x7f120059
com.iptv.android.dev:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f110246
com.iptv.android.dev:macro/m3_comp_filled_autocomplete_menu_list_item_selected_container_color = 0x7f0e0042
com.iptv.android.dev:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1102c9
com.iptv.android.dev:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0e00d7
com.iptv.android.dev:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f110444
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f110183
com.iptv.android.dev:macro/m3_comp_fab_primary_small_container_shape = 0x7f0e003b
com.iptv.android.dev:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0702ae
com.iptv.android.dev:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0e0034
com.iptv.android.dev:macro/m3_comp_extended_fab_surface_container_color = 0x7f0e0033
com.iptv.android.dev:macro/m3_comp_elevated_button_container_color = 0x7f0e002a
com.iptv.android.dev:macro/m3_comp_dialog_supporting_text_color = 0x7f0e0027
com.iptv.android.dev:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1102b0
com.iptv.android.dev:macro/m3_comp_dialog_headline_color = 0x7f0e0025
com.iptv.android.dev:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0e014e
com.iptv.android.dev:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0e0022
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0e008a
com.iptv.android.dev:styleable/ActionMode = 0x7f120004
com.iptv.android.dev:color/secondary_text_disabled_material_light = 0x7f060304
com.iptv.android.dev:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0e001e
com.iptv.android.dev:interpolator/mtrl_linear = 0x7f0c0010
com.iptv.android.dev:id/graph_wrap = 0x7f0a00f1
com.iptv.android.dev:color/m3_sys_color_dark_surface_dim = 0x7f060181
com.iptv.android.dev:dimen/design_snackbar_padding_vertical = 0x7f070086
com.iptv.android.dev:attr/tint = 0x7f04046b
com.iptv.android.dev:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f110115
com.iptv.android.dev:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0e001c
com.iptv.android.dev:string/date_range_picker_start_headline = 0x7f100080
com.iptv.android.dev:string/mtrl_picker_text_input_date_range_start_hint = 0x7f100130
com.iptv.android.dev:id/gone = 0x7f0a00ef
com.iptv.android.dev:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0e00be
com.iptv.android.dev:string/material_timepicker_hour = 0x7f100100
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1103d6
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f11018e
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f11045a
com.iptv.android.dev:string/abc_search_hint = 0x7f100012
com.iptv.android.dev:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0e001a
com.iptv.android.dev:style/Base.Widget.Material3.BottomNavigationView = 0x7f110102
com.iptv.android.dev:styleable/AppCompatImageView = 0x7f12000f
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f11047e
com.iptv.android.dev:style/Widget.Material3.Chip.Input.Icon = 0x7f1103a5
com.iptv.android.dev:string/date_picker_switch_to_day_selection = 0x7f100072
com.iptv.android.dev:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0e0019
com.iptv.android.dev:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0e0017
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1100e3
com.iptv.android.dev:id/view_offset_helper = 0x7f0a0201
com.iptv.android.dev:id/is_pooling_container_tag = 0x7f0a0106
com.iptv.android.dev:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0e0015
com.iptv.android.dev:id/search_badge = 0x7f0a019b
com.iptv.android.dev:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f1000e4
com.iptv.android.dev:styleable/Layout = 0x7f120047
com.iptv.android.dev:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f11047b
com.iptv.android.dev:attr/textLocale = 0x7f040451
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Button = 0x7f110019
com.iptv.android.dev:color/mtrl_navigation_bar_item_tint = 0x7f0602cd
com.iptv.android.dev:color/design_default_color_on_primary = 0x7f06004c
com.iptv.android.dev:color/overlay_dark = 0x7f0602f1
com.iptv.android.dev:macro/m3_comp_switch_selected_handle_color = 0x7f0e0125
com.iptv.android.dev:dimen/mtrl_card_checked_icon_margin = 0x7f0702b7
com.iptv.android.dev:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0e0014
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0e0078
com.iptv.android.dev:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0e0012
com.iptv.android.dev:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f110048
com.iptv.android.dev:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0e0011
com.iptv.android.dev:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0e000c
com.iptv.android.dev:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1102bf
com.iptv.android.dev:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0e0020
com.iptv.android.dev:color/primary_dark_material_dark = 0x7f0602f5
com.iptv.android.dev:styleable/Fragment = 0x7f120039
com.iptv.android.dev:macro/m3_comp_checkbox_selected_container_color = 0x7f0e0006
com.iptv.android.dev:style/Widget.MaterialComponents.NavigationView = 0x7f11046e
com.iptv.android.dev:macro/m3_comp_sheet_side_docked_container_color = 0x7f0e010a
com.iptv.android.dev:layout/splash_screen_view = 0x7f0d007f
com.iptv.android.dev:layout/select_dialog_multichoice_material = 0x7f0d007d
com.iptv.android.dev:layout/notification_template_part_chronometer = 0x7f0d007a
com.iptv.android.dev:string/icon_content_description = 0x7f1000cd
com.iptv.android.dev:id/transition_position = 0x7f0a01f7
com.iptv.android.dev:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0e00b8
com.iptv.android.dev:dimen/m3_comp_elevated_card_container_elevation = 0x7f07012e
com.iptv.android.dev:layout/notification_template_lines_media = 0x7f0d0077
com.iptv.android.dev:layout/notification_template_big_media_narrow = 0x7f0d0073
com.iptv.android.dev:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0e00fb
com.iptv.android.dev:style/Widget.Material3.Button.UnelevatedButton = 0x7f11039a
com.iptv.android.dev:string/abc_capital_off = 0x7f100006
com.iptv.android.dev:layout/notification_template_big_media_custom = 0x7f0d0072
com.iptv.android.dev:string/material_timepicker_minute = 0x7f100101
com.iptv.android.dev:id/auto = 0x7f0a004f
com.iptv.android.dev:id/showHome = 0x7f0a01aa
com.iptv.android.dev:dimen/splashscreen_icon_mask_size_no_background = 0x7f070336
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600c7
com.iptv.android.dev:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702cd
com.iptv.android.dev:layout/notification_action = 0x7f0d006d
com.iptv.android.dev:layout/mtrl_picker_header_toggle = 0x7f0d0068
com.iptv.android.dev:style/ShapeAppearance.Material3.Corner.Small = 0x7f1101a7
com.iptv.android.dev:id/staticPostLayout = 0x7f0a01c5
com.iptv.android.dev:id/src_atop = 0x7f0a01bc
com.iptv.android.dev:layout/mtrl_picker_header_fullscreen = 0x7f0d0065
com.iptv.android.dev:layout/mtrl_picker_dialog = 0x7f0d0062
com.iptv.android.dev:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0e00c2
com.iptv.android.dev:layout/mtrl_calendar_day = 0x7f0d0054
com.iptv.android.dev:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0d0052
com.iptv.android.dev:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f080109
com.iptv.android.dev:macro/m3_comp_filled_icon_button_container_color = 0x7f0e0049
com.iptv.android.dev:integer/m3_sys_motion_duration_long3 = 0x7f0b0018
com.iptv.android.dev:layout/material_clock_display = 0x7f0d0040
com.iptv.android.dev:layout/m3_alert_dialog_title = 0x7f0d003c
com.iptv.android.dev:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f110401
com.iptv.android.dev:drawable/exo_ic_subtitle_off = 0x7f0800a6
com.iptv.android.dev:style/Widget.Material3.Button.TextButton = 0x7f110392
com.iptv.android.dev:dimen/mtrl_btn_letter_spacing = 0x7f07027f
com.iptv.android.dev:dimen/hint_pressed_alpha_material_light = 0x7f0700bc
com.iptv.android.dev:layout/ime_secondary_split_test_activity = 0x7f0d0038
com.iptv.android.dev:layout/exo_track_selection_dialog = 0x7f0d0036
com.iptv.android.dev:layout/exo_styled_player_control_ffwd_button = 0x7f0d002f
com.iptv.android.dev:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f11005c
com.iptv.android.dev:styleable/MaterialTimePicker = 0x7f12005c
com.iptv.android.dev:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f070121
com.iptv.android.dev:layout/exo_player_control_view = 0x7f0d002d
com.iptv.android.dev:layout/design_text_input_start_icon = 0x7f0d002b
com.iptv.android.dev:color/material_dynamic_tertiary60 = 0x7f060256
com.iptv.android.dev:layout/design_bottom_navigation_item = 0x7f0d001d
com.iptv.android.dev:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f11044d
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f11048e
com.iptv.android.dev:style/ThemeOverlay.Material3.Dialog = 0x7f1102d2
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.Settings = 0x7f11013c
com.iptv.android.dev:style/ShapeAppearance.Material3.Corner.None = 0x7f1101a6
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Inverse = 0x7f110020
com.iptv.android.dev:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1102ca
com.iptv.android.dev:layout/abc_screen_toolbar = 0x7f0d0017
com.iptv.android.dev:style/TextAppearance.Compat.Notification = 0x7f1101f6
com.iptv.android.dev:layout/abc_list_menu_item_checkbox = 0x7f0d000e
com.iptv.android.dev:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1102f2
com.iptv.android.dev:style/TextAppearance.Material3.SearchBar = 0x7f110229
com.iptv.android.dev:layout/abc_cascading_menu_item_layout = 0x7f0d000b
com.iptv.android.dev:styleable/ScrollingViewBehavior_Layout = 0x7f12007e
com.iptv.android.dev:layout/abc_action_mode_bar = 0x7f0d0004
com.iptv.android.dev:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0e0106
com.iptv.android.dev:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0e013f
com.iptv.android.dev:layout/abc_action_menu_item_layout = 0x7f0d0002
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog.Alert = 0x7f110292
com.iptv.android.dev:interpolator/mtrl_fast_out_slow_in = 0x7f0c000f
com.iptv.android.dev:interpolator/mtrl_fast_out_linear_in = 0x7f0c000e
com.iptv.android.dev:interpolator/m3_sys_motion_easing_linear = 0x7f0c000a
com.iptv.android.dev:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1100be
com.iptv.android.dev:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005
com.iptv.android.dev:integer/mtrl_switch_thumb_viewport_size = 0x7f0b003e
com.iptv.android.dev:layout/mtrl_search_view = 0x7f0d006c
com.iptv.android.dev:integer/mtrl_calendar_selection_text_lines = 0x7f0b0034
com.iptv.android.dev:integer/material_motion_duration_short_2 = 0x7f0b002e
com.iptv.android.dev:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0b0027
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f110188
com.iptv.android.dev:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0b0023
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0e008f
com.iptv.android.dev:macro/m3_comp_slider_inactive_track_color = 0x7f0e0111
com.iptv.android.dev:integer/m3_sys_motion_duration_short2 = 0x7f0b001f
com.iptv.android.dev:integer/m3_sys_motion_duration_short1 = 0x7f0b001e
com.iptv.android.dev:integer/mtrl_badge_max_character_count = 0x7f0b0030
com.iptv.android.dev:drawable/mtrl_switch_thumb_checked = 0x7f08011d
com.iptv.android.dev:string/nav_live_tv = 0x7f100148
com.iptv.android.dev:string/language_spanish = 0x7f1000d6
com.iptv.android.dev:integer/m3_sys_motion_duration_extra_long4 = 0x7f0b0015
com.iptv.android.dev:integer/m3_sys_motion_duration_extra_long2 = 0x7f0b0013
com.iptv.android.dev:styleable/NavigationView = 0x7f120070
com.iptv.android.dev:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f11034e
com.iptv.android.dev:style/Widget.Material3.Tooltip = 0x7f110419
com.iptv.android.dev:macro/m3_comp_navigation_drawer_headline_type = 0x7f0e0088
com.iptv.android.dev:layout/abc_activity_chooser_view_list_item = 0x7f0d0007
com.iptv.android.dev:id/homeAsUp = 0x7f0a00f9
com.iptv.android.dev:integer/m3_chip_anim_duration = 0x7f0b0011
com.iptv.android.dev:id/rounded = 0x7f0a018f
com.iptv.android.dev:string/exo_download_downloading = 0x7f1000af
com.iptv.android.dev:integer/m3_card_anim_duration_ms = 0x7f0b0010
com.iptv.android.dev:string/call_notification_answer_video_action = 0x7f10003f
com.iptv.android.dev:integer/m3_btn_anim_duration_ms = 0x7f0b000e
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f110099
com.iptv.android.dev:styleable/DefaultTimeBar = 0x7f12002e
com.iptv.android.dev:integer/design_snackbar_text_max_lines = 0x7f0b0007
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f110182
com.iptv.android.dev:id/text_input_error_icon = 0x7f0a01e2
com.iptv.android.dev:integer/default_icon_animation_duration = 0x7f0b0006
com.iptv.android.dev:dimen/material_textinput_max_width = 0x7f07025d
com.iptv.android.dev:string/status_bar_notification_info_overflow = 0x7f100195
com.iptv.android.dev:string/mtrl_checkbox_state_description_indeterminate = 0x7f10010f
com.iptv.android.dev:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f110423
com.iptv.android.dev:string/auth_activate = 0x7f100027
com.iptv.android.dev:integer/config_tooltipAnimTime = 0x7f0b0005
com.iptv.android.dev:style/Base.Widget.AppCompat.Button.Colored = 0x7f1100d4
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Dialog = 0x7f110068
com.iptv.android.dev:string/settings_adult_content = 0x7f100179
com.iptv.android.dev:id/withinBounds = 0x7f0a020b
com.iptv.android.dev:id/when_playing = 0x7f0a0208
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1102fb
com.iptv.android.dev:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0e004e
com.iptv.android.dev:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f110384
com.iptv.android.dev:macro/m3_comp_switch_unselected_handle_color = 0x7f0e0135
com.iptv.android.dev:id/useLogo = 0x7f0a01ff
com.iptv.android.dev:id/transition_scene_layoutid_cache = 0x7f0a01f8
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f110037
com.iptv.android.dev:id/transitionToStart = 0x7f0a01f4
com.iptv.android.dev:id/toggle = 0x7f0a01ef
com.iptv.android.dev:string/auth_registering = 0x7f100030
com.iptv.android.dev:id/title_template = 0x7f0a01ee
com.iptv.android.dev:style/Widget.MaterialComponents.Slider = 0x7f110475
com.iptv.android.dev:string/date_input_headline_description = 0x7f100064
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1103c8
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f11017a
com.iptv.android.dev:color/mtrl_tabs_icon_color_selector = 0x7f0602dd
com.iptv.android.dev:drawable/exo_icon_previous = 0x7f0800af
com.iptv.android.dev:attr/keyboardIcon = 0x7f040254
com.iptv.android.dev:id/textinput_error = 0x7f0a01e5
com.iptv.android.dev:id/textinput_counter = 0x7f0a01e4
com.iptv.android.dev:color/material_personalized_color_outline = 0x7f060285
com.iptv.android.dev:id/text_input_end_icon = 0x7f0a01e1
com.iptv.android.dev:id/textEnd = 0x7f0a01dc
com.iptv.android.dev:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f08001f
com.iptv.android.dev:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701d4
com.iptv.android.dev:attr/materialTimePickerTheme = 0x7f0402eb
com.iptv.android.dev:id/tag_state_description = 0x7f0a01d5
com.iptv.android.dev:id/tag_on_receive_content_mime_types = 0x7f0a01d3
com.iptv.android.dev:id/tag_on_receive_content_listener = 0x7f0a01d2
com.iptv.android.dev:id/spline = 0x7f0a01b7
com.iptv.android.dev:id/tabMode = 0x7f0a01cc
com.iptv.android.dev:styleable/DrawerLayout = 0x7f120030
com.iptv.android.dev:integer/mtrl_btn_anim_duration_ms = 0x7f0b0032
com.iptv.android.dev:macro/m3_comp_filled_text_field_container_shape = 0x7f0e004d
com.iptv.android.dev:id/peekHeight = 0x7f0a017d
com.iptv.android.dev:layout/abc_list_menu_item_layout = 0x7f0d0010
com.iptv.android.dev:id/start = 0x7f0a01c0
com.iptv.android.dev:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f110436
com.iptv.android.dev:string/settings_logout = 0x7f100183
com.iptv.android.dev:id/standard = 0x7f0a01bf
com.iptv.android.dev:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f1103ad
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080016
com.iptv.android.dev:string/nav_eventos = 0x7f100144
com.iptv.android.dev:layout/abc_alert_dialog_material = 0x7f0d0009
com.iptv.android.dev:id/spread_inside = 0x7f0a01ba
com.iptv.android.dev:layout/abc_activity_chooser_view = 0x7f0d0006
com.iptv.android.dev:id/split_action_bar = 0x7f0a01b8
com.iptv.android.dev:layout/abc_search_dropdown_item_icons_2line = 0x7f0d0018
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f11045e
com.iptv.android.dev:id/snackbar_action = 0x7f0a01af
com.iptv.android.dev:id/slide = 0x7f0a01ae
com.iptv.android.dev:id/exo_overflow_show = 0x7f0a00c0
com.iptv.android.dev:string/mtrl_picker_range_header_only_start_selected = 0x7f100128
com.iptv.android.dev:style/Base.CardView = 0x7f110010
com.iptv.android.dev:id/showTitle = 0x7f0a01ab
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f11033f
com.iptv.android.dev:string/mtrl_picker_a11y_next_month = 0x7f100114
com.iptv.android.dev:id/textStart = 0x7f0a01df
com.iptv.android.dev:styleable/BottomAppBar = 0x7f120017
com.iptv.android.dev:id/selection_type = 0x7f0a01a7
com.iptv.android.dev:id/search_button = 0x7f0a019d
com.iptv.android.dev:string/side_sheet_accessibility_pane_title = 0x7f100192
com.iptv.android.dev:id/scroll = 0x7f0a0196
com.iptv.android.dev:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0e014a
com.iptv.android.dev:id/sawtooth = 0x7f0a0193
com.iptv.android.dev:id/save_non_transition_alpha = 0x7f0a0191
com.iptv.android.dev:id/right_icon = 0x7f0a018d
com.iptv.android.dev:dimen/mtrl_calendar_navigation_top_padding = 0x7f0702a9
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1100e6
com.iptv.android.dev:id/scrollIndicatorDown = 0x7f0a0197
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f110028
com.iptv.android.dev:drawable/$avd_hide_password__1 = 0x7f080001
com.iptv.android.dev:style/Widget.MaterialComponents.BottomNavigationView = 0x7f11042a
com.iptv.android.dev:dimen/design_bottom_sheet_elevation = 0x7f07006b
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f110219
com.iptv.android.dev:id/rightToLeft = 0x7f0a018c
com.iptv.android.dev:id/reverseSawtooth = 0x7f0a018a
com.iptv.android.dev:id/rectangles = 0x7f0a0188
com.iptv.android.dev:id/ratio = 0x7f0a0187
com.iptv.android.dev:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1102df
com.iptv.android.dev:styleable/MenuView = 0x7f120060
com.iptv.android.dev:id/progress_circular = 0x7f0a0184
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1103d8
com.iptv.android.dev:id/pressed = 0x7f0a0183
com.iptv.android.dev:id/search_edit_frame = 0x7f0a019f
com.iptv.android.dev:id/postLayout = 0x7f0a0182
com.iptv.android.dev:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0e0121
com.iptv.android.dev:drawable/design_fab_background = 0x7f080083
com.iptv.android.dev:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0e014c
com.iptv.android.dev:id/pooling_container_listener_holder_tag = 0x7f0a0180
com.iptv.android.dev:id/easeIn = 0x7f0a0099
com.iptv.android.dev:id/parent = 0x7f0a0176
com.iptv.android.dev:layout/notification_template_icon_group = 0x7f0d0076
com.iptv.android.dev:drawable/notification_bg_low_normal = 0x7f08012d
com.iptv.android.dev:id/parallax = 0x7f0a0175
com.iptv.android.dev:style/AlertDialog.AppCompat.Light = 0x7f110001
com.iptv.android.dev:macro/m3_comp_top_app_bar_small_container_color = 0x7f0e0170
com.iptv.android.dev:style/Widget.AppCompat.TextView = 0x7f110366
com.iptv.android.dev:id/open_search_view_toolbar_container = 0x7f0a0171
com.iptv.android.dev:integer/m3_sys_motion_duration_long4 = 0x7f0b0019
com.iptv.android.dev:dimen/mtrl_textinput_box_stroke_width_default = 0x7f07031a
com.iptv.android.dev:attr/startIconTintMode = 0x7f0403db
com.iptv.android.dev:dimen/m3_badge_horizontal_offset = 0x7f0700d6
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f110455
com.iptv.android.dev:style/Theme.AppCompat.Light = 0x7f110251
com.iptv.android.dev:macro/m3_comp_snackbar_container_shape = 0x7f0e0115
com.iptv.android.dev:id/open_search_view_search_prefix = 0x7f0a016e
com.iptv.android.dev:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1102f7
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f11045f
com.iptv.android.dev:id/open_search_view_edit_text = 0x7f0a016a
com.iptv.android.dev:string/date_input_invalid_for_pattern = 0x7f100065
com.iptv.android.dev:id/open_search_view_dummy_toolbar = 0x7f0a0169
com.iptv.android.dev:string/m3_sys_motion_easing_standard_decelerate = 0x7f1000ed
com.iptv.android.dev:id/open_search_bar_text_view = 0x7f0a0164
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f070182
com.iptv.android.dev:id/one = 0x7f0a0163
com.iptv.android.dev:id/notification_main_column_container = 0x7f0a0160
com.iptv.android.dev:string/mtrl_checkbox_button_path_name = 0x7f10010c
com.iptv.android.dev:color/switch_thumb_disabled_material_light = 0x7f06030b
com.iptv.android.dev:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f070339
com.iptv.android.dev:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001d
com.iptv.android.dev:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0e0173
com.iptv.android.dev:id/never = 0x7f0a015a
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f11015b
com.iptv.android.dev:style/ShapeAppearance.Material3.Corner.Large = 0x7f1101a4
com.iptv.android.dev:style/Widget.Material3.NavigationRailView.Badge = 0x7f1103f1
com.iptv.android.dev:id/mtrl_picker_header_selection_text = 0x7f0a0149
com.iptv.android.dev:styleable/ViewBackgroundHelper = 0x7f12009c
com.iptv.android.dev:id/mtrl_picker_header = 0x7f0a0148
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Time = 0x7f1101fc
com.iptv.android.dev:id/mtrl_motion_snapshot_view = 0x7f0a0146
com.iptv.android.dev:style/Base.ThemeOverlay.AppCompat.Light = 0x7f110081
com.iptv.android.dev:id/mtrl_child_content_container = 0x7f0a0144
com.iptv.android.dev:string/m3_sys_motion_easing_standard = 0x7f1000eb
com.iptv.android.dev:string/time_picker_minute_selection = 0x7f1001a4
com.iptv.android.dev:id/mtrl_calendar_year_selector_frame = 0x7f0a0142
com.iptv.android.dev:id/mtrl_calendar_main_pane = 0x7f0a013e
com.iptv.android.dev:styleable/ListPopupWindow = 0x7f12004b
com.iptv.android.dev:id/mtrl_calendar_days_of_week = 0x7f0a013c
com.iptv.android.dev:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f11041f
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1101e7
com.iptv.android.dev:id/month_navigation_fragment_toggle = 0x7f0a0135
com.iptv.android.dev:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0b0041
com.iptv.android.dev:dimen/m3_comp_switch_track_height = 0x7f0701b4
com.iptv.android.dev:color/mtrl_textinput_filled_box_default_background_color = 0x7f0602e4
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0e008b
com.iptv.android.dev:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0e00b4
com.iptv.android.dev:style/Base.V14.Theme.Material3.Light = 0x7f110090
com.iptv.android.dev:layout/design_layout_tab_text = 0x7f0d0022
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f11020f
com.iptv.android.dev:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f1000e5
com.iptv.android.dev:layout/notification_template_part_time = 0x7f0d007b
com.iptv.android.dev:id/month_navigation_bar = 0x7f0a0134
com.iptv.android.dev:id/month_grid = 0x7f0a0133
com.iptv.android.dev:string/material_hour_suffix = 0x7f1000f3
com.iptv.android.dev:id/middle = 0x7f0a0131
com.iptv.android.dev:id/mtrl_view_tag_bottom_padding = 0x7f0a0150
com.iptv.android.dev:id/media_actions = 0x7f0a012e
com.iptv.android.dev:id/right_side = 0x7f0a018e
com.iptv.android.dev:string/date_range_input_invalid_range_input = 0x7f10007a
com.iptv.android.dev:id/material_timepicker_view = 0x7f0a012b
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f110075
com.iptv.android.dev:dimen/mtrl_slider_thumb_radius = 0x7f070306
com.iptv.android.dev:color/m3_ref_palette_secondary20 = 0x7f060143
com.iptv.android.dev:id/material_minute_text_input = 0x7f0a0124
com.iptv.android.dev:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0e0172
com.iptv.android.dev:id/ifRoom = 0x7f0a00fd
com.iptv.android.dev:id/material_clock_display_and_toggle = 0x7f0a011a
com.iptv.android.dev:id/material_clock_display = 0x7f0a0119
com.iptv.android.dev:string/date_picker_year_picker_pane_title = 0x7f100079
com.iptv.android.dev:id/checked = 0x7f0a0067
com.iptv.android.dev:style/Widget.Material3.SideSheet.Modal = 0x7f110400
com.iptv.android.dev:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f11019b
com.iptv.android.dev:id/masked = 0x7f0a0117
com.iptv.android.dev:id/line1 = 0x7f0a0110
com.iptv.android.dev:string/mtrl_picker_navigate_to_current_year_description = 0x7f100124
com.iptv.android.dev:id/leftToRight = 0x7f0a010e
com.iptv.android.dev:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1102c5
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.AudioTrack = 0x7f110135
com.iptv.android.dev:id/jumpToEnd = 0x7f0a0109
com.iptv.android.dev:color/mtrl_filled_stroke_color = 0x7f0602c9
com.iptv.android.dev:id/exo_extra_controls = 0x7f0a00b5
com.iptv.android.dev:id/action_bar_root = 0x7f0a0034
com.iptv.android.dev:id/inspection_slot_table_set = 0x7f0a0103
com.iptv.android.dev:dimen/m3_comp_fab_primary_small_container_height = 0x7f070143
com.iptv.android.dev:dimen/exo_error_message_height = 0x7f070090
com.iptv.android.dev:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1102cb
com.iptv.android.dev:id/image = 0x7f0a0100
com.iptv.android.dev:string/call_notification_ongoing_text = 0x7f100043
com.iptv.android.dev:style/Base.V26.Theme.AppCompat = 0x7f1100b8
com.iptv.android.dev:id/ignoreRequest = 0x7f0a00ff
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f110212
com.iptv.android.dev:id/icon_group = 0x7f0a00fc
com.iptv.android.dev:string/mtrl_switch_thumb_path_pressed = 0x7f10013d
com.iptv.android.dev:macro/m3_comp_navigation_rail_container_color = 0x7f0e009b
com.iptv.android.dev:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f11041d
com.iptv.android.dev:id/honorRequest = 0x7f0a00fa
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1101f2
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f1101b9
com.iptv.android.dev:id/hide_in_inspector_tag = 0x7f0a00f6
com.iptv.android.dev:id/hide_ime_id = 0x7f0a00f5
com.iptv.android.dev:id/edge = 0x7f0a009c
com.iptv.android.dev:id/header_title = 0x7f0a00f4
com.iptv.android.dev:id/group_divider = 0x7f0a00f2
com.iptv.android.dev:string/time_picker_am = 0x7f10019d
com.iptv.android.dev:styleable/Constraint = 0x7f120027
com.iptv.android.dev:id/graph = 0x7f0a00f0
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f11020e
com.iptv.android.dev:color/on_surface = 0x7f0602ef
com.iptv.android.dev:id/fragment_container_view_tag = 0x7f0a00eb
com.iptv.android.dev:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f110424
com.iptv.android.dev:id/floating = 0x7f0a00e9
com.iptv.android.dev:id/fitStart = 0x7f0a00e2
com.iptv.android.dev:xml/network_security_config = 0x7f130003
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070219
com.iptv.android.dev:id/fixed_width = 0x7f0a00e7
com.iptv.android.dev:styleable/KeyPosition = 0x7f120044
com.iptv.android.dev:id/fit = 0x7f0a00df
com.iptv.android.dev:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0e011b
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1101e4
com.iptv.android.dev:styleable/FontFamily = 0x7f120036
com.iptv.android.dev:style/TextAppearance.AppCompat.Display1 = 0x7f1101cb
com.iptv.android.dev:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0602cc
com.iptv.android.dev:id/listMode = 0x7f0a0113
com.iptv.android.dev:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f11037c
com.iptv.android.dev:id/filled = 0x7f0a00de
com.iptv.android.dev:id/fill_horizontal = 0x7f0a00dc
com.iptv.android.dev:id/fill = 0x7f0a00db
com.iptv.android.dev:color/material_personalized_color_text_primary_inverse = 0x7f06029d
com.iptv.android.dev:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.iptv.android.dev:id/fade = 0x7f0a00da
com.iptv.android.dev:id/exo_subtitles = 0x7f0a00d3
com.iptv.android.dev:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0e0032
com.iptv.android.dev:id/exo_sub_text = 0x7f0a00d1
com.iptv.android.dev:string/exo_track_role_closed_captions = 0x7f1000bb
com.iptv.android.dev:id/exo_settings = 0x7f0a00cd
com.iptv.android.dev:styleable/FloatingActionButton_Behavior_Layout = 0x7f120034
com.iptv.android.dev:id/exo_repeat_toggle = 0x7f0a00ca
com.iptv.android.dev:id/exo_prev = 0x7f0a00c7
com.iptv.android.dev:id/exo_playback_speed = 0x7f0a00c5
com.iptv.android.dev:id/exo_controller_placeholder = 0x7f0a00b1
com.iptv.android.dev:style/TextAppearance.Design.Hint = 0x7f110205
com.iptv.android.dev:id/exo_minimal_fullscreen = 0x7f0a00bd
com.iptv.android.dev:id/exo_minimal_controls = 0x7f0a00bc
com.iptv.android.dev:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f110395
com.iptv.android.dev:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0e0131
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionButton = 0x7f1100ca
com.iptv.android.dev:id/packed = 0x7f0a0174
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f11023c
com.iptv.android.dev:id/exo_icon = 0x7f0a00ba
com.iptv.android.dev:string/search_placeholder = 0x7f10016e
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1102fe
com.iptv.android.dev:id/exo_ffwd = 0x7f0a00b7
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f110031
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f11002e
com.iptv.android.dev:string/action_cancel = 0x7f10001c
com.iptv.android.dev:dimen/design_navigation_icon_size = 0x7f070077
com.iptv.android.dev:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0e001b
com.iptv.android.dev:id/exo_extra_controls_scroll_view = 0x7f0a00b6
com.iptv.android.dev:id/exo_basic_controls = 0x7f0a00aa
com.iptv.android.dev:macro/m3_comp_outlined_text_field_caret_color = 0x7f0e00b2
com.iptv.android.dev:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f110446
com.iptv.android.dev:string/password_toggle_content_description = 0x7f100153
com.iptv.android.dev:id/end = 0x7f0a00a1
com.iptv.android.dev:style/Base.Theme.AppCompat = 0x7f11004b
com.iptv.android.dev:macro/m3_comp_slider_disabled_active_track_color = 0x7f0e010d
com.iptv.android.dev:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f11009e
com.iptv.android.dev:id/elastic = 0x7f0a009f
com.iptv.android.dev:id/confirm_button = 0x7f0a0072
com.iptv.android.dev:id/edit_text_id = 0x7f0a009e
com.iptv.android.dev:string/error_server = 0x7f10008f
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1101e9
com.iptv.android.dev:id/easeOut = 0x7f0a009b
com.iptv.android.dev:id/easeInOut = 0x7f0a009a
com.iptv.android.dev:string/switch_role = 0x7f100197
com.iptv.android.dev:id/dragDown = 0x7f0a0092
com.iptv.android.dev:id/disableScroll = 0x7f0a0090
com.iptv.android.dev:integer/m3_sys_motion_duration_long2 = 0x7f0b0017
com.iptv.android.dev:id/disablePostScroll = 0x7f0a008f
com.iptv.android.dev:id/direct = 0x7f0a008d
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0e0159
com.iptv.android.dev:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702e0
com.iptv.android.dev:id/dimensions = 0x7f0a008c
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f11017c
com.iptv.android.dev:id/dialog_button = 0x7f0a008b
com.iptv.android.dev:id/design_bottom_sheet = 0x7f0a0086
com.iptv.android.dev:styleable/MockView = 0x7f120061
com.iptv.android.dev:id/default_activity_button = 0x7f0a0084
com.iptv.android.dev:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f07015f
com.iptv.android.dev:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0e00bf
com.iptv.android.dev:id/decor_content_parent = 0x7f0a0083
com.iptv.android.dev:id/decelerateAndComplete = 0x7f0a0082
com.iptv.android.dev:styleable/ClockHandView = 0x7f120022
com.iptv.android.dev:id/custom = 0x7f0a007d
com.iptv.android.dev:id/contiguous = 0x7f0a0077
com.iptv.android.dev:id/compress = 0x7f0a0071
com.iptv.android.dev:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0e0091
com.iptv.android.dev:id/compose_view_saveable_id_tag = 0x7f0a0070
com.iptv.android.dev:id/collapseActionView = 0x7f0a006f
com.iptv.android.dev:id/clockwise = 0x7f0a006d
com.iptv.android.dev:id/clear_text = 0x7f0a006a
com.iptv.android.dev:xml/data_extraction_rules = 0x7f130001
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f070213
com.iptv.android.dev:id/circle_center = 0x7f0a0069
com.iptv.android.dev:id/checkbox = 0x7f0a0066
com.iptv.android.dev:id/channel_logo = 0x7f0a0064
com.iptv.android.dev:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080043
com.iptv.android.dev:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f110080
com.iptv.android.dev:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f070197
com.iptv.android.dev:attr/tabIndicatorFullWidth = 0x7f040407
com.iptv.android.dev:style/Theme.Material3.Light.NoActionBar = 0x7f11027b
com.iptv.android.dev:id/chains = 0x7f0a0063
com.iptv.android.dev:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f110267
com.iptv.android.dev:id/touch_outside = 0x7f0a01f2
com.iptv.android.dev:id/chain = 0x7f0a0062
com.iptv.android.dev:id/centerInside = 0x7f0a005f
com.iptv.android.dev:id/centerCrop = 0x7f0a005e
com.iptv.android.dev:id/center = 0x7f0a005d
com.iptv.android.dev:string/abc_action_bar_home_description = 0x7f100000
com.iptv.android.dev:id/buttonPanel = 0x7f0a005a
com.iptv.android.dev:id/bounce = 0x7f0a0059
com.iptv.android.dev:string/abc_menu_ctrl_shortcut_label = 0x7f100009
com.iptv.android.dev:drawable/avd_show_password = 0x7f080078
com.iptv.android.dev:string/exo_track_unknown = 0x7f1000c7
com.iptv.android.dev:id/bottom = 0x7f0a0058
com.iptv.android.dev:integer/m3_sys_motion_duration_extra_long3 = 0x7f0b0014
com.iptv.android.dev:id/blocking = 0x7f0a0057
com.iptv.android.dev:id/beginning = 0x7f0a0056
com.iptv.android.dev:dimen/design_navigation_elevation = 0x7f070075
com.iptv.android.dev:id/beginOnFirstDraw = 0x7f0a0055
com.iptv.android.dev:string/error_device_activation = 0x7f100087
com.iptv.android.dev:id/center_horizontal = 0x7f0a0060
com.iptv.android.dev:string/searchbar_scrolling_view_behavior = 0x7f100172
com.iptv.android.dev:id/baseline = 0x7f0a0054
com.iptv.android.dev:id/autoComplete = 0x7f0a0050
com.iptv.android.dev:dimen/m3_navigation_rail_default_width = 0x7f0701e5
com.iptv.android.dev:layout/abc_screen_simple_overlay_action_mode = 0x7f0d0016
com.iptv.android.dev:id/async = 0x7f0a004e
com.iptv.android.dev:id/asConfigured = 0x7f0a004d
com.iptv.android.dev:string/settings_language_subtitle = 0x7f100182
com.iptv.android.dev:id/arc = 0x7f0a004c
com.iptv.android.dev:id/animateToEnd = 0x7f0a004a
com.iptv.android.dev:id/androidx_compose_ui_view_composition_context = 0x7f0a0049
com.iptv.android.dev:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1100e9
com.iptv.android.dev:id/alertTitle = 0x7f0a0045
com.iptv.android.dev:id/activity_chooser_view_content = 0x7f0a0043
com.iptv.android.dev:id/mtrl_picker_text_input_range_start = 0x7f0a014e
com.iptv.android.dev:id/actions = 0x7f0a0042
com.iptv.android.dev:id/text2 = 0x7f0a01db
com.iptv.android.dev:id/action_text = 0x7f0a0041
com.iptv.android.dev:id/action_mode_bar = 0x7f0a003e
com.iptv.android.dev:id/action_menu_presenter = 0x7f0a003d
com.iptv.android.dev:id/action_divider = 0x7f0a003a
com.iptv.android.dev:id/action_container = 0x7f0a0038
com.iptv.android.dev:id/action_bar_container = 0x7f0a0033
com.iptv.android.dev:styleable/MaterialAlertDialog = 0x7f12004c
com.iptv.android.dev:id/action_bar = 0x7f0a0031
com.iptv.android.dev:macro/m3_comp_linear_progress_indicator_track_color = 0x7f0e005f
com.iptv.android.dev:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f07018a
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f07022f
com.iptv.android.dev:id/accessibility_custom_action_5 = 0x7f0a002b
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Light = 0x7f110098
com.iptv.android.dev:string/exo_controls_repeat_one_description = 0x7f1000a3
com.iptv.android.dev:id/accessibility_custom_action_29 = 0x7f0a0026
com.iptv.android.dev:id/accessibility_custom_action_25 = 0x7f0a0022
com.iptv.android.dev:dimen/mtrl_chip_pressed_translation_z = 0x7f0702bd
com.iptv.android.dev:style/Theme.Material3.DynamicColors.DayNight = 0x7f110273
com.iptv.android.dev:id/accessibility_custom_action_2 = 0x7f0a001c
com.iptv.android.dev:style/Theme.Material3.DayNight = 0x7f11026a
com.iptv.android.dev:integer/app_bar_elevation_anim_duration = 0x7f0b0002
com.iptv.android.dev:id/accessibility_custom_action_18 = 0x7f0a001a
com.iptv.android.dev:id/accessibility_custom_action_17 = 0x7f0a0019
com.iptv.android.dev:id/accessibility_custom_action_1 = 0x7f0a0011
com.iptv.android.dev:layout/mtrl_alert_select_dialog_item = 0x7f0d0050
com.iptv.android.dev:dimen/exo_styled_progress_touch_target_height = 0x7f0700b2
com.iptv.android.dev:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f110082
com.iptv.android.dev:id/SHOW_PATH = 0x7f0a0009
com.iptv.android.dev:id/SHIFT = 0x7f0a0007
com.iptv.android.dev:integer/mtrl_switch_track_viewport_height = 0x7f0b003f
com.iptv.android.dev:drawable/mtrl_switch_track_decoration = 0x7f080127
com.iptv.android.dev:style/Widget.AppCompat.RatingBar = 0x7f11035b
com.iptv.android.dev:id/META = 0x7f0a0005
com.iptv.android.dev:drawable/abc_ratingbar_material = 0x7f08005b
com.iptv.android.dev:id/FUNCTION = 0x7f0a0004
com.iptv.android.dev:string/mtrl_picker_range_header_selected = 0x7f100129
com.iptv.android.dev:id/BOTTOM_START = 0x7f0a0002
com.iptv.android.dev:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f110429
com.iptv.android.dev:id/BOTTOM_END = 0x7f0a0001
com.iptv.android.dev:id/mtrl_calendar_text_input_frame = 0x7f0a0141
com.iptv.android.dev:drawable/test_level_drawable = 0x7f080137
com.iptv.android.dev:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f110432
com.iptv.android.dev:drawable/notification_template_icon_low_bg = 0x7f080134
com.iptv.android.dev:drawable/notification_template_icon_bg = 0x7f080133
com.iptv.android.dev:dimen/m3_searchbar_elevation = 0x7f0701f5
com.iptv.android.dev:string/date_picker_title = 0x7f100077
com.iptv.android.dev:string/exo_track_role_commentary = 0x7f1000bc
com.iptv.android.dev:drawable/notification_bg_normal_pressed = 0x7f080130
com.iptv.android.dev:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080036
com.iptv.android.dev:layout/design_bottom_sheet_dialog = 0x7f0d001e
com.iptv.android.dev:drawable/notification_bg_low = 0x7f08012c
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1103c5
com.iptv.android.dev:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f080125
com.iptv.android.dev:drawable/mtrl_switch_thumb_pressed_checked = 0x7f080121
com.iptv.android.dev:attr/fabAlignmentModeEndMargin = 0x7f0401c1
com.iptv.android.dev:drawable/abc_text_select_handle_middle_mtrl = 0x7f08006f
com.iptv.android.dev:id/staticLayout = 0x7f0a01c4
com.iptv.android.dev:drawable/mtrl_popupmenu_background_overlay = 0x7f08011b
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f110040
com.iptv.android.dev:drawable/mtrl_popupmenu_background = 0x7f08011a
com.iptv.android.dev:drawable/mtrl_ic_error = 0x7f080117
com.iptv.android.dev:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f11011c
com.iptv.android.dev:drawable/mtrl_ic_arrow_drop_up = 0x7f080112
com.iptv.android.dev:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f08010b
com.iptv.android.dev:layout/material_clock_display_divider = 0x7f0d0041
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0e0075
com.iptv.android.dev:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.iptv.android.dev:color/material_cursor_color = 0x7f060217
com.iptv.android.dev:id/groups = 0x7f0a00f3
com.iptv.android.dev:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.iptv.android.dev:drawable/mtrl_checkbox_button = 0x7f080105
com.iptv.android.dev:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f080103
com.iptv.android.dev:attr/buffered_color = 0x7f04008c
com.iptv.android.dev:dimen/m3_btn_icon_btn_padding_right = 0x7f0700f5
com.iptv.android.dev:style/ExoStyledControls.Button.Center.PlayPause = 0x7f110142
com.iptv.android.dev:id/exo_position = 0x7f0a00c6
com.iptv.android.dev:id/exo_audio_track = 0x7f0a00a9
com.iptv.android.dev:drawable/material_ic_edit_black_24dp = 0x7f0800fd
com.iptv.android.dev:attr/colorPrimaryInverse = 0x7f04010a
com.iptv.android.dev:drawable/material_ic_clear_black_24dp = 0x7f0800fc
com.iptv.android.dev:drawable/material_ic_calendar_black_24dp = 0x7f0800fb
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0e0164
com.iptv.android.dev:color/mtrl_card_view_ripple = 0x7f0602bb
com.iptv.android.dev:style/Theme.IPTVAndroid.Fullscreen = 0x7f110260
com.iptv.android.dev:drawable/m3_tabs_transparent_background = 0x7f0800f9
com.iptv.android.dev:drawable/m3_popupmenu_background_overlay = 0x7f0800f3
com.iptv.android.dev:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.iptv.android.dev:drawable/m3_password_eye = 0x7f0800f2
com.iptv.android.dev:style/IPTVButton = 0x7f11014c
com.iptv.android.dev:style/Widget.Material3.BottomSheet.Modal = 0x7f110387
com.iptv.android.dev:macro/m3_comp_fab_secondary_icon_color = 0x7f0e003d
com.iptv.android.dev:layout/select_dialog_singlechoice_material = 0x7f0d007e
com.iptv.android.dev:drawable/m3_bottom_sheet_drag_handle = 0x7f0800f1
com.iptv.android.dev:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1100ec
com.iptv.android.dev:id/navigation_header_container = 0x7f0a0159
com.iptv.android.dev:drawable/m3_avd_show_password = 0x7f0800f0
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1103d9
com.iptv.android.dev:color/design_fab_stroke_end_outer_color = 0x7f06005a
com.iptv.android.dev:drawable/m3_appbar_background = 0x7f0800ee
com.iptv.android.dev:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0e00c7
com.iptv.android.dev:attr/badgeRadius = 0x7f040057
com.iptv.android.dev:drawable/ic_tv = 0x7f0800eb
com.iptv.android.dev:dimen/cardview_default_radius = 0x7f070054
com.iptv.android.dev:drawable/ic_search_black_24 = 0x7f0800ea
com.iptv.android.dev:drawable/ic_play_arrow = 0x7f0800e9
com.iptv.android.dev:attr/tooltipText = 0x7f040485
com.iptv.android.dev:style/Base.V22.Theme.AppCompat = 0x7f1100b0
com.iptv.android.dev:attr/collapsingToolbarLayoutLargeSize = 0x7f0400df
com.iptv.android.dev:drawable/ic_mtrl_chip_checked_circle = 0x7f0800e7
com.iptv.android.dev:drawable/ic_m3_chip_close = 0x7f0800e4
com.iptv.android.dev:drawable/ic_call_answer_video_low = 0x7f0800db
com.iptv.android.dev:color/design_dark_default_color_secondary = 0x7f060045
com.iptv.android.dev:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.iptv.android.dev:drawable/exo_styled_controls_speed = 0x7f0800d3
com.iptv.android.dev:color/m3_ref_palette_error90 = 0x7f06010b
com.iptv.android.dev:string/error_not_found = 0x7f10008c
com.iptv.android.dev:id/texture_view = 0x7f0a01ea
com.iptv.android.dev:attr/checkMarkTint = 0x7f0400a8
com.iptv.android.dev:drawable/exo_styled_controls_settings = 0x7f0800d0
com.iptv.android.dev:id/exo_ffwd_with_amount = 0x7f0a00b8
com.iptv.android.dev:drawable/exo_styled_controls_rewind = 0x7f0800cf
com.iptv.android.dev:attr/tabIndicatorHeight = 0x7f040409
com.iptv.android.dev:drawable/exo_styled_controls_repeat_all = 0x7f0800cc
com.iptv.android.dev:drawable/exo_styled_controls_previous = 0x7f0800cb
com.iptv.android.dev:id/accessibility_custom_action_14 = 0x7f0a0016
com.iptv.android.dev:drawable/exo_styled_controls_next = 0x7f0800c6
com.iptv.android.dev:color/material_dynamic_primary95 = 0x7f060240
com.iptv.android.dev:drawable/mtrl_ic_checkbox_unchecked = 0x7f080116
com.iptv.android.dev:drawable/exo_notification_previous = 0x7f0800bc
com.iptv.android.dev:attr/materialCardViewElevatedStyle = 0x7f0402d6
com.iptv.android.dev:drawable/exo_icon_vr = 0x7f0800b7
com.iptv.android.dev:id/title = 0x7f0a01ec
com.iptv.android.dev:id/mini = 0x7f0a0132
com.iptv.android.dev:dimen/m3_bottom_sheet_elevation = 0x7f0700e6
com.iptv.android.dev:drawable/exo_icon_stop = 0x7f0800b6
com.iptv.android.dev:drawable/exo_icon_repeat_off = 0x7f0800b1
com.iptv.android.dev:style/Animation.Design.BottomSheetDialog = 0x7f110005
com.iptv.android.dev:string/path_password_strike_through = 0x7f100157
com.iptv.android.dev:drawable/exo_icon_fullscreen_enter = 0x7f0800aa
com.iptv.android.dev:drawable/exo_icon_fastforward = 0x7f0800a9
com.iptv.android.dev:attr/iconTintMode = 0x7f040224
com.iptv.android.dev:drawable/exo_icon_circular_play = 0x7f0800a8
com.iptv.android.dev:attr/queryPatterns = 0x7f040377
com.iptv.android.dev:drawable/exo_ic_skip_previous = 0x7f0800a4
com.iptv.android.dev:color/m3_ref_palette_neutral_variant40 = 0x7f06012b
com.iptv.android.dev:drawable/exo_ic_settings = 0x7f0800a2
com.iptv.android.dev:style/ShapeAppearance.Material3.Corner.Medium = 0x7f1101a5
com.iptv.android.dev:color/material_on_primary_emphasis_high_type = 0x7f06026b
com.iptv.android.dev:attr/gapBetweenBars = 0x7f040201
com.iptv.android.dev:id/search_src_text = 0x7f0a01a3
com.iptv.android.dev:drawable/exo_ic_play_circle_filled = 0x7f0800a0
com.iptv.android.dev:attr/paddingTopSystemWindowInsets = 0x7f04034c
com.iptv.android.dev:drawable/exo_ic_fullscreen_exit = 0x7f08009e
com.iptv.android.dev:attr/layout_keyline = 0x7f04029b
com.iptv.android.dev:drawable/exo_ic_forward = 0x7f08009c
com.iptv.android.dev:drawable/exo_ic_default_album_image = 0x7f08009b
com.iptv.android.dev:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f070142
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f07021b
com.iptv.android.dev:attr/onPositiveCross = 0x7f04033f
com.iptv.android.dev:dimen/m3_btn_elevation = 0x7f0700f3
com.iptv.android.dev:attr/shapeAppearanceSmallComponent = 0x7f0403a6
com.iptv.android.dev:color/material_dynamic_neutral_variant70 = 0x7f060230
com.iptv.android.dev:drawable/exo_ic_chevron_right = 0x7f08009a
com.iptv.android.dev:dimen/m3_badge_size = 0x7f0700d8
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.Button = 0x7f1101b3
com.iptv.android.dev:attr/forceApplySystemWindowInsetTop = 0x7f0401fd
com.iptv.android.dev:dimen/m3_comp_sheet_side_docked_container_width = 0x7f07019f
com.iptv.android.dev:drawable/exo_ic_chevron_left = 0x7f080099
com.iptv.android.dev:color/material_grey_850 = 0x7f060261
com.iptv.android.dev:drawable/exo_ic_audiotrack = 0x7f080097
com.iptv.android.dev:integer/m3_sys_motion_duration_medium4 = 0x7f0b001d
com.iptv.android.dev:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702d4
com.iptv.android.dev:drawable/exo_controls_previous = 0x7f08008e
com.iptv.android.dev:anim/design_bottom_sheet_slide_out = 0x7f010019
com.iptv.android.dev:drawable/exo_controls_next = 0x7f08008b
com.iptv.android.dev:drawable/exo_controls_fullscreen_exit = 0x7f08008a
com.iptv.android.dev:style/TextAppearance.Material3.DisplayLarge = 0x7f11021f
com.iptv.android.dev:drawable/exo_controls_fullscreen_enter = 0x7f080089
com.iptv.android.dev:drawable/exo_controls_fastforward = 0x7f080088
com.iptv.android.dev:dimen/design_bottom_navigation_active_text_size = 0x7f070061
com.iptv.android.dev:style/Base.TextAppearance.MaterialComponents.Button = 0x7f110045
com.iptv.android.dev:color/m3_selection_control_ripple_color_selector = 0x7f06015b
com.iptv.android.dev:drawable/ic_call_answer_video = 0x7f0800da
com.iptv.android.dev:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702cf
com.iptv.android.dev:drawable/design_snackbar_background = 0x7f080087
com.iptv.android.dev:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f0701b7
com.iptv.android.dev:dimen/mtrl_btn_hovered_z = 0x7f07027b
com.iptv.android.dev:dimen/m3_card_stroke_width = 0x7f07010f
com.iptv.android.dev:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08007a
com.iptv.android.dev:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0e0035
com.iptv.android.dev:macro/m3_comp_search_bar_supporting_text_type = 0x7f0e00f0
com.iptv.android.dev:attr/closeIconVisible = 0x7f0400d7
com.iptv.android.dev:drawable/m3_tabs_rounded_line_indicator = 0x7f0800f8
com.iptv.android.dev:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080073
com.iptv.android.dev:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08006c
com.iptv.android.dev:attr/font = 0x7f0401f1
com.iptv.android.dev:dimen/m3_extended_fab_min_height = 0x7f0701cf
com.iptv.android.dev:color/design_default_color_secondary = 0x7f060052
com.iptv.android.dev:drawable/abc_tab_indicator_material = 0x7f08006b
com.iptv.android.dev:macro/m3_comp_fab_primary_icon_color = 0x7f0e0039
com.iptv.android.dev:style/ExoStyledControls.Button.Center.FfwdWithAmount = 0x7f110140
com.iptv.android.dev:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0e00a4
com.iptv.android.dev:drawable/abc_switch_thumb_material = 0x7f080069
com.iptv.android.dev:style/Platform.MaterialComponents.Dialog = 0x7f11016c
com.iptv.android.dev:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0e00db
com.iptv.android.dev:attr/iconifiedByDefault = 0x7f040225
com.iptv.android.dev:color/m3_button_ripple_color = 0x7f06007e
com.iptv.android.dev:drawable/abc_star_black_48dp = 0x7f080067
com.iptv.android.dev:layout/abc_action_menu_layout = 0x7f0d0003
com.iptv.android.dev:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08005f
com.iptv.android.dev:color/m3_ref_palette_primary100 = 0x7f060135
com.iptv.android.dev:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005e
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight = 0x7f110281
com.iptv.android.dev:color/material_dynamic_neutral_variant100 = 0x7f06022a
com.iptv.android.dev:drawable/exo_styled_controls_fastforward = 0x7f0800c3
com.iptv.android.dev:drawable/abc_list_selector_holo_dark = 0x7f080056
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1103d7
com.iptv.android.dev:drawable/abc_list_selector_disabled_holo_light = 0x7f080055
com.iptv.android.dev:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f070120
com.iptv.android.dev:string/content_add_favorite = 0x7f100056
com.iptv.android.dev:drawable/abc_list_pressed_holo_dark = 0x7f080050
com.iptv.android.dev:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080071
com.iptv.android.dev:drawable/abc_list_divider_mtrl_alpha = 0x7f08004d
com.iptv.android.dev:dimen/mtrl_textinput_counter_margin_start = 0x7f07031c
com.iptv.android.dev:attr/layout_constraintHeight_max = 0x7f040278
com.iptv.android.dev:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f11009f
com.iptv.android.dev:dimen/m3_extended_fab_end_padding = 0x7f0701cd
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Body2 = 0x7f110018
com.iptv.android.dev:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0e00ad
com.iptv.android.dev:drawable/abc_item_background_holo_light = 0x7f08004b
com.iptv.android.dev:drawable/abc_ic_voice_search_api_material = 0x7f080049
com.iptv.android.dev:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1102b4
com.iptv.android.dev:dimen/mtrl_switch_text_padding = 0x7f070311
com.iptv.android.dev:drawable/abc_ic_search_api_material = 0x7f080048
com.iptv.android.dev:styleable/MaterialButtonToggleGroup = 0x7f120050
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar = 0x7f11033b
com.iptv.android.dev:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080047
com.iptv.android.dev:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f110256
com.iptv.android.dev:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080046
com.iptv.android.dev:color/m3_popupmenu_overlay_color = 0x7f0600b0
com.iptv.android.dev:color/material_personalized_color_on_surface_variant = 0x7f060282
com.iptv.android.dev:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080045
com.iptv.android.dev:attr/colorOnSurfaceInverse = 0x7f0400fd
com.iptv.android.dev:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080042
com.iptv.android.dev:id/dragEnd = 0x7f0a0093
com.iptv.android.dev:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08003e
com.iptv.android.dev:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0701be
com.iptv.android.dev:string/material_timepicker_clock_mode_description = 0x7f1000ff
com.iptv.android.dev:drawable/exo_controls_shuffle_off = 0x7f080093
com.iptv.android.dev:attr/layout_constrainedHeight = 0x7f040267
com.iptv.android.dev:drawable/abc_ic_ab_back_material = 0x7f08003d
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f110158
com.iptv.android.dev:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f11005a
com.iptv.android.dev:integer/status_bar_notification_info_maxnum = 0x7f0b0046
com.iptv.android.dev:drawable/abc_btn_radio_material_anim = 0x7f080032
com.iptv.android.dev:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080024
com.iptv.android.dev:color/material_personalized_color_on_tertiary_container = 0x7f060284
com.iptv.android.dev:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0e009e
com.iptv.android.dev:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080023
com.iptv.android.dev:id/ghost_view = 0x7f0a00ed
com.iptv.android.dev:id/cos = 0x7f0a0079
com.iptv.android.dev:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080020
com.iptv.android.dev:color/m3_ref_palette_tertiary50 = 0x7f060153
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001c
com.iptv.android.dev:string/mtrl_picker_out_of_range = 0x7f100126
com.iptv.android.dev:drawable/abc_switch_track_mtrl_alpha = 0x7f08006a
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001a
com.iptv.android.dev:color/m3_card_stroke_color = 0x7f060084
com.iptv.android.dev:color/m3_ref_palette_neutral30 = 0x7f060116
com.iptv.android.dev:attr/motionDurationShort4 = 0x7f040317
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080018
com.iptv.android.dev:string/cd_back = 0x7f100045
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080013
com.iptv.android.dev:color/m3_sys_color_dark_on_surface_variant = 0x7f060171
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f110218
com.iptv.android.dev:attr/useCompatPadding = 0x7f0404a2
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080011
com.iptv.android.dev:id/dragStart = 0x7f0a0096
com.iptv.android.dev:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000e
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0e00d0
com.iptv.android.dev:drawable/$m3_avd_show_password__2 = 0x7f08000b
com.iptv.android.dev:style/Widget.Material3.BottomSheet = 0x7f110385
com.iptv.android.dev:string/mtrl_checkbox_button_path_group_name = 0x7f10010b
com.iptv.android.dev:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401bb
com.iptv.android.dev:attr/actionBarTabStyle = 0x7f040008
com.iptv.android.dev:color/brand_primary = 0x7f060024
com.iptv.android.dev:string/date_picker_no_selection_description = 0x7f10006e
com.iptv.android.dev:drawable/$avd_hide_password__0 = 0x7f080000
com.iptv.android.dev:dimen/notification_action_text_size = 0x7f070328
com.iptv.android.dev:dimen/m3_alert_dialog_action_top_padding = 0x7f0700c1
com.iptv.android.dev:dimen/tooltip_y_offset_touch = 0x7f070344
com.iptv.android.dev:attr/itemTextAppearanceActive = 0x7f04024d
com.iptv.android.dev:layout/mtrl_calendar_months = 0x7f0d005b
com.iptv.android.dev:id/indeterminate = 0x7f0a0101
com.iptv.android.dev:dimen/splashscreen_icon_mask_size_with_background = 0x7f070337
com.iptv.android.dev:dimen/notification_top_pad = 0x7f070334
com.iptv.android.dev:id/splashscreen_icon_view = 0x7f0a01b6
com.iptv.android.dev:attr/errorContentDescription = 0x7f0401a4
com.iptv.android.dev:dimen/notification_right_icon_size = 0x7f07032f
com.iptv.android.dev:dimen/notification_large_icon_height = 0x7f07032b
com.iptv.android.dev:dimen/mtrl_tooltip_padding = 0x7f070325
com.iptv.android.dev:id/spacer = 0x7f0a01b3
com.iptv.android.dev:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f07031e
com.iptv.android.dev:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f07031b
com.iptv.android.dev:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0e00b5
com.iptv.android.dev:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070319
com.iptv.android.dev:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000d
com.iptv.android.dev:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070318
com.iptv.android.dev:id/SHOW_ALL = 0x7f0a0008
com.iptv.android.dev:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f11044b
com.iptv.android.dev:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f07030b
com.iptv.android.dev:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f08010e
com.iptv.android.dev:dimen/mtrl_slider_widget_height = 0x7f07030a
com.iptv.android.dev:style/Widget.MaterialComponents.Tooltip = 0x7f110497
com.iptv.android.dev:color/material_dynamic_secondary95 = 0x7f06024d
com.iptv.android.dev:dimen/mtrl_slider_track_side_padding = 0x7f070309
com.iptv.android.dev:id/material_clock_period_toggle = 0x7f0a0120
com.iptv.android.dev:dimen/mtrl_slider_track_height = 0x7f070308
com.iptv.android.dev:integer/mtrl_switch_thumb_pressed_duration = 0x7f0b003c
com.iptv.android.dev:attr/extendedFloatingActionButtonStyle = 0x7f0401bc
com.iptv.android.dev:attr/editTextStyle = 0x7f04018f
com.iptv.android.dev:id/open_search_view_content_container = 0x7f0a0167
com.iptv.android.dev:dimen/m3_searchview_elevation = 0x7f0701fe
com.iptv.android.dev:dimen/mtrl_slider_thumb_elevation = 0x7f070305
com.iptv.android.dev:dimen/mtrl_slider_label_padding = 0x7f070302
com.iptv.android.dev:string/abc_searchview_description_search = 0x7f100015
com.iptv.android.dev:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0e002e
com.iptv.android.dev:dimen/m3_extended_fab_top_padding = 0x7f0701d1
com.iptv.android.dev:dimen/mtrl_shape_corner_size_small_component = 0x7f070300
com.iptv.android.dev:integer/show_password_duration = 0x7f0b0045
com.iptv.android.dev:dimen/mtrl_shape_corner_size_medium_component = 0x7f0702ff
com.iptv.android.dev:dimen/mtrl_shape_corner_size_large_component = 0x7f0702fe
com.iptv.android.dev:id/visible_removing_fragment_view_tag = 0x7f0a0207
com.iptv.android.dev:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0702fc
com.iptv.android.dev:string/time_picker_pm = 0x7f1001a8
com.iptv.android.dev:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f07017b
com.iptv.android.dev:color/mtrl_btn_transparent_bg_color = 0x7f0602b7
com.iptv.android.dev:color/material_on_surface_emphasis_high_type = 0x7f06026e
com.iptv.android.dev:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0702fb
com.iptv.android.dev:dimen/mtrl_calendar_action_padding = 0x7f07028f
com.iptv.android.dev:string/abc_menu_shift_shortcut_label = 0x7f10000e
com.iptv.android.dev:dimen/material_clock_size = 0x7f07024a
com.iptv.android.dev:dimen/mtrl_progress_circular_inset_small = 0x7f0702f3
com.iptv.android.dev:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702f1
com.iptv.android.dev:attr/indicatorColor = 0x7f040229
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f110195
com.iptv.android.dev:dimen/mtrl_slider_halo_radius = 0x7f070301
com.iptv.android.dev:attr/checkedChip = 0x7f0400ac
com.iptv.android.dev:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702ee
com.iptv.android.dev:dimen/mtrl_navigation_rail_icon_size = 0x7f0702ec
com.iptv.android.dev:attr/boxStrokeErrorColor = 0x7f040088
com.iptv.android.dev:style/Widget.Material3.Toolbar.OnSurface = 0x7f110417
com.iptv.android.dev:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0e00c4
com.iptv.android.dev:dimen/mtrl_navigation_rail_default_width = 0x7f0702e9
com.iptv.android.dev:id/accessibility_custom_action_24 = 0x7f0a0021
com.iptv.android.dev:color/material_dynamic_tertiary95 = 0x7f06025a
com.iptv.android.dev:attr/arrowShaftLength = 0x7f04003c
com.iptv.android.dev:color/m3_calendar_item_stroke_color = 0x7f060081
com.iptv.android.dev:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702e7
com.iptv.android.dev:attr/indicatorDirectionLinear = 0x7f04022b
com.iptv.android.dev:dimen/mtrl_navigation_item_icon_size = 0x7f0702e4
com.iptv.android.dev:styleable/CheckedTextView = 0x7f12001d
com.iptv.android.dev:dimen/mtrl_navigation_item_icon_padding = 0x7f0702e3
com.iptv.android.dev:styleable/MaterialAutoCompleteTextView = 0x7f12004e
com.iptv.android.dev:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702e2
com.iptv.android.dev:attr/itemFillColor = 0x7f040236
com.iptv.android.dev:macro/m3_comp_fab_tertiary_container_color = 0x7f0e0040
com.iptv.android.dev:dimen/mtrl_navigation_elevation = 0x7f0702e1
com.iptv.android.dev:macro/m3_comp_bottom_app_bar_container_color = 0x7f0e0005
com.iptv.android.dev:dimen/mtrl_high_ripple_default_alpha = 0x7f0702d6
com.iptv.android.dev:string/exo_controls_fastforward_description = 0x7f100096
com.iptv.android.dev:string/date_range_picker_scroll_to_next_month = 0x7f10007e
com.iptv.android.dev:color/switch_thumb_normal_material_dark = 0x7f06030e
com.iptv.android.dev:id/action_image = 0x7f0a003b
com.iptv.android.dev:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702d1
com.iptv.android.dev:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702d0
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1100c6
com.iptv.android.dev:dimen/mtrl_extended_fab_top_padding = 0x7f0702ce
com.iptv.android.dev:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702d8
com.iptv.android.dev:attr/subtitleTextColor = 0x7f0403f3
com.iptv.android.dev:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700da
com.iptv.android.dev:color/exo_styled_error_message_background = 0x7f06006b
com.iptv.android.dev:dimen/mtrl_extended_fab_start_padding = 0x7f0702cc
com.iptv.android.dev:dimen/mtrl_extended_fab_min_width = 0x7f0702cb
com.iptv.android.dev:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0e0140
com.iptv.android.dev:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702c7
com.iptv.android.dev:color/design_dark_default_color_background = 0x7f06003b
com.iptv.android.dev:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0e0098
com.iptv.android.dev:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702c3
com.iptv.android.dev:dimen/abc_text_size_display_3_material = 0x7f070045
com.iptv.android.dev:attr/values = 0x7f0404a7
com.iptv.android.dev:dimen/exo_settings_height = 0x7f07009d
com.iptv.android.dev:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0702c1
com.iptv.android.dev:attr/waveVariesBy = 0x7f0404b2
com.iptv.android.dev:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0702c0
com.iptv.android.dev:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0702bf
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f110180
com.iptv.android.dev:drawable/abc_seekbar_tick_mark_material = 0x7f080063
com.iptv.android.dev:dimen/mtrl_card_elevation = 0x7f0702bb
com.iptv.android.dev:styleable/Variant = 0x7f12009a
com.iptv.android.dev:string/character_counter_content_description = 0x7f10004f
com.iptv.android.dev:attr/round = 0x7f040388
com.iptv.android.dev:attr/fontProviderPackage = 0x7f0401f7
com.iptv.android.dev:dimen/mtrl_card_dragged_z = 0x7f0702ba
com.iptv.android.dev:dimen/mtrl_card_corner_radius = 0x7f0702b9
com.iptv.android.dev:dimen/mtrl_card_checked_icon_size = 0x7f0702b8
com.iptv.android.dev:attr/boxStrokeWidth = 0x7f040089
com.iptv.android.dev:color/design_dark_default_color_primary_dark = 0x7f060043
com.iptv.android.dev:attr/expandedTitleTextAppearance = 0x7f0401b6
com.iptv.android.dev:dimen/mtrl_calendar_year_vertical_padding = 0x7f0702b5
com.iptv.android.dev:attr/scrubber_color = 0x7f04038f
com.iptv.android.dev:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0702b4
com.iptv.android.dev:layout/material_time_input = 0x7f0d0049
com.iptv.android.dev:dimen/mtrl_calendar_year_height = 0x7f0702b3
com.iptv.android.dev:attr/behavior_saveFlags = 0x7f040075
com.iptv.android.dev:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0702ab
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600c6
com.iptv.android.dev:drawable/exo_notification_rewind = 0x7f0800bd
com.iptv.android.dev:style/TextAppearance.Material3.BodyLarge = 0x7f11021c
com.iptv.android.dev:drawable/design_password_eye = 0x7f080086
com.iptv.android.dev:style/Widget.Material3.CardView.Outlined = 0x7f11039d
com.iptv.android.dev:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0702a2
com.iptv.android.dev:dimen/exo_error_message_text_padding_vertical = 0x7f070093
com.iptv.android.dev:attr/barLength = 0x7f040066
com.iptv.android.dev:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0702a1
com.iptv.android.dev:dimen/mtrl_calendar_header_selection_line_height = 0x7f07029f
com.iptv.android.dev:style/Widget.Material3.Chip.Filter.Elevated = 0x7f1103a2
com.iptv.android.dev:dimen/mtrl_calendar_dialog_background_inset = 0x7f070299
com.iptv.android.dev:layout/mtrl_calendar_month = 0x7f0d0058
com.iptv.android.dev:attr/numericModifiers = 0x7f04033a
com.iptv.android.dev:dimen/mtrl_calendar_day_vertical_padding = 0x7f070296
com.iptv.android.dev:color/material_personalized_color_primary_inverse = 0x7f060289
com.iptv.android.dev:string/auth_activate_subtitle = 0x7f100029
com.iptv.android.dev:dimen/mtrl_card_spacing = 0x7f0702bc
com.iptv.android.dev:color/material_personalized_color_surface_container_highest = 0x7f060294
com.iptv.android.dev:dimen/mtrl_calendar_day_horizontal_padding = 0x7f070294
com.iptv.android.dev:dimen/mtrl_calendar_content_padding = 0x7f070291
com.iptv.android.dev:id/textSpacerNoButtons = 0x7f0a01dd
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0e0084
com.iptv.android.dev:drawable/abc_text_select_handle_left_mtrl = 0x7f08006e
com.iptv.android.dev:attr/chipBackgroundColor = 0x7f0400b6
com.iptv.android.dev:dimen/mtrl_calendar_bottom_padding = 0x7f070290
com.iptv.android.dev:color/material_dynamic_primary10 = 0x7f060236
com.iptv.android.dev:dimen/mtrl_extended_fab_icon_size = 0x7f0702c8
com.iptv.android.dev:styleable/ClockFaceView = 0x7f120021
com.iptv.android.dev:dimen/tooltip_vertical_padding = 0x7f070342
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0e015d
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_primary = 0x7f06018a
com.iptv.android.dev:dimen/mtrl_btn_text_size = 0x7f07028b
com.iptv.android.dev:layout/material_timepicker_dialog = 0x7f0d004b
com.iptv.android.dev:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070288
com.iptv.android.dev:dimen/mtrl_progress_track_thickness = 0x7f0702fd
com.iptv.android.dev:dimen/mtrl_alert_dialog_background_inset_start = 0x7f070264
com.iptv.android.dev:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f070286
com.iptv.android.dev:dimen/mtrl_btn_padding_left = 0x7f070282
com.iptv.android.dev:dimen/mtrl_tooltip_minHeight = 0x7f070323
com.iptv.android.dev:dimen/mtrl_btn_icon_padding = 0x7f07027d
com.iptv.android.dev:dimen/mtrl_bottomappbar_height = 0x7f070274
com.iptv.android.dev:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f070271
com.iptv.android.dev:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1102d7
com.iptv.android.dev:attr/materialIconButtonFilledTonalStyle = 0x7f0402e0
com.iptv.android.dev:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702d7
com.iptv.android.dev:id/accessibility_action_clickable_span = 0x7f0a000f
com.iptv.android.dev:dimen/mtrl_badge_size = 0x7f070269
com.iptv.android.dev:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f110113
com.iptv.android.dev:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f070266
com.iptv.android.dev:style/Base.Widget.AppCompat.ListView = 0x7f1100ea
com.iptv.android.dev:color/material_personalized_color_secondary_container = 0x7f06028d
com.iptv.android.dev:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f070261
com.iptv.android.dev:dimen/material_time_picker_minimum_screen_width = 0x7f070260
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0600f5
com.iptv.android.dev:color/secondary_text_disabled_material_dark = 0x7f060303
com.iptv.android.dev:id/content = 0x7f0a0075
com.iptv.android.dev:drawable/ic_clear_black_24 = 0x7f0800de
com.iptv.android.dev:style/Theme.Design.Light.BottomSheetDialog = 0x7f11025c
com.iptv.android.dev:attr/backgroundInsetTop = 0x7f04004f
com.iptv.android.dev:dimen/material_textinput_default_width = 0x7f07025c
com.iptv.android.dev:attr/helperTextTextAppearance = 0x7f04020b
com.iptv.android.dev:color/material_dynamic_tertiary10 = 0x7f060250
com.iptv.android.dev:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070257
com.iptv.android.dev:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f070256
com.iptv.android.dev:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f070255
com.iptv.android.dev:interpolator/mtrl_linear_out_slow_in = 0x7f0c0011
com.iptv.android.dev:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f110107
com.iptv.android.dev:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f070252
com.iptv.android.dev:dimen/m3_extended_fab_icon_padding = 0x7f0701ce
com.iptv.android.dev:color/m3_tabs_ripple_color = 0x7f0601fd
com.iptv.android.dev:dimen/material_cursor_inset = 0x7f07024b
com.iptv.android.dev:id/fitEnd = 0x7f0a00e1
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Body1 = 0x7f110230
com.iptv.android.dev:dimen/design_fab_size_normal = 0x7f070072
com.iptv.android.dev:dimen/material_clock_period_toggle_width = 0x7f070249
com.iptv.android.dev:dimen/material_clock_period_toggle_vertical_gap = 0x7f070248
com.iptv.android.dev:id/material_textinput_timepicker = 0x7f0a0126
com.iptv.android.dev:style/Platform.V25.AppCompat.Light = 0x7f110175
com.iptv.android.dev:dimen/material_clock_display_width = 0x7f070240
com.iptv.android.dev:macro/m3_comp_slider_handle_color = 0x7f0e0110
com.iptv.android.dev:dimen/m3_timepicker_window_elevation = 0x7f07023b
com.iptv.android.dev:attr/actionOverflowMenuStyle = 0x7f040021
com.iptv.android.dev:string/settings_title = 0x7f10018d
com.iptv.android.dev:dimen/mtrl_extended_fab_bottom_padding = 0x7f0702c2
com.iptv.android.dev:styleable/SnackbarLayout = 0x7f120086
com.iptv.android.dev:dimen/design_navigation_icon_padding = 0x7f070076
com.iptv.android.dev:dimen/m3_timepicker_display_stroke_width = 0x7f07023a
com.iptv.android.dev:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1102dc
com.iptv.android.dev:drawable/ic_list = 0x7f0800e1
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f070234
com.iptv.android.dev:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0702fa
com.iptv.android.dev:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0e00a0
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f070233
com.iptv.android.dev:color/material_personalized__highlighted_text_inverse = 0x7f060272
com.iptv.android.dev:styleable/CollapsingToolbarLayout_Layout = 0x7f120024
com.iptv.android.dev:layout/abc_screen_content_include = 0x7f0d0014
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary99 = 0x7f0600e6
com.iptv.android.dev:attr/checkedButton = 0x7f0400ab
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f070231
com.iptv.android.dev:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1102c6
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070230
com.iptv.android.dev:id/mtrl_picker_title_text = 0x7f0a014f
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f07022d
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f07022b
com.iptv.android.dev:dimen/m3_navigation_rail_item_min_height = 0x7f0701eb
com.iptv.android.dev:dimen/fastscroll_margin = 0x7f0700b4
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionButton = 0x7f110344
com.iptv.android.dev:dimen/mtrl_calendar_header_divider_thickness = 0x7f07029c
com.iptv.android.dev:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070228
com.iptv.android.dev:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070227
com.iptv.android.dev:string/cd_favorite_button = 0x7f100048
com.iptv.android.dev:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f070226
com.iptv.android.dev:layout/abc_dialog_title_material = 0x7f0d000c
com.iptv.android.dev:dimen/mtrl_snackbar_background_corner_radius = 0x7f07030c
com.iptv.android.dev:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080026
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f070222
com.iptv.android.dev:style/Widget.Material3.ActionMode = 0x7f110378
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f070212
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070220
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f07021e
com.iptv.android.dev:color/m3_slider_inactive_track_color = 0x7f06015f
com.iptv.android.dev:attr/motionTarget = 0x7f040329
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f07021d
com.iptv.android.dev:style/Widget.Material3.MaterialDivider = 0x7f1103e4
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070218
com.iptv.android.dev:id/material_minute_tv = 0x7f0a0125
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f070217
com.iptv.android.dev:attr/dayStyle = 0x7f040160
com.iptv.android.dev:attr/popupWindowStyle = 0x7f04036c
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f070215
com.iptv.android.dev:id/coil_request_manager = 0x7f0a006e
com.iptv.android.dev:dimen/m3_carousel_debug_keyline_width = 0x7f070110
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f110452
com.iptv.android.dev:dimen/fastscroll_default_thickness = 0x7f0700b3
com.iptv.android.dev:string/exo_track_stereo = 0x7f1000c3
com.iptv.android.dev:id/exo_main_text = 0x7f0a00bb
com.iptv.android.dev:color/primary = 0x7f0602f3
com.iptv.android.dev:dimen/m3_sys_elevation_level4 = 0x7f070210
com.iptv.android.dev:dimen/m3_sys_elevation_level3 = 0x7f07020f
com.iptv.android.dev:dimen/m3_snackbar_margin = 0x7f07020b
com.iptv.android.dev:dimen/m3_slider_thumb_elevation = 0x7f070207
com.iptv.android.dev:dimen/m3_side_sheet_standard_elevation = 0x7f070202
com.iptv.android.dev:string/error_a11y_label = 0x7f100086
com.iptv.android.dev:dimen/m3_side_sheet_modal_elevation = 0x7f070201
com.iptv.android.dev:macro/m3_comp_search_view_container_surface_tint_layer_color = 0x7f0e00f3
com.iptv.android.dev:dimen/mtrl_btn_max_width = 0x7f070280
com.iptv.android.dev:dimen/m3_searchview_height = 0x7f0701ff
com.iptv.android.dev:dimen/m3_searchbar_outlined_stroke_width = 0x7f0701f9
com.iptv.android.dev:string/error_invalid_code = 0x7f10008a
com.iptv.android.dev:id/motion_base = 0x7f0a0139
com.iptv.android.dev:dimen/m3_searchbar_margin_vertical = 0x7f0701f8
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0e0100
com.iptv.android.dev:dimen/m3_searchbar_margin_horizontal = 0x7f0701f7
com.iptv.android.dev:style/Widget.Material3.ChipGroup = 0x7f1103a9
com.iptv.android.dev:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701f4
com.iptv.android.dev:attr/layout = 0x7f04025e
com.iptv.android.dev:dimen/m3_ripple_pressed_alpha = 0x7f0701f3
com.iptv.android.dev:dimen/material_time_picker_minimum_screen_height = 0x7f07025f
com.iptv.android.dev:attr/layout_constraintEnd_toStartOf = 0x7f040273
com.iptv.android.dev:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701ef
com.iptv.android.dev:color/mtrl_tabs_colored_ripple_color = 0x7f0602dc
com.iptv.android.dev:color/m3_navigation_item_background_color = 0x7f0600a9
com.iptv.android.dev:dimen/abc_search_view_preferred_height = 0x7f070036
com.iptv.android.dev:attr/indicatorInset = 0x7f04022c
com.iptv.android.dev:dimen/m3_navigation_rail_item_padding_top = 0x7f0701ee
com.iptv.android.dev:style/ThemeOverlay.AppCompat.Light = 0x7f1102b7
com.iptv.android.dev:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.iptv.android.dev:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701e4
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601bb
com.iptv.android.dev:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701e3
com.iptv.android.dev:attr/useDrawerArrowDrawable = 0x7f0404a3
com.iptv.android.dev:color/m3_ref_palette_neutral98 = 0x7f060124
com.iptv.android.dev:string/player_play = 0x7f10015f
com.iptv.android.dev:dimen/m3_navigation_item_vertical_padding = 0x7f0701e2
com.iptv.android.dev:dimen/m3_navigation_item_shape_inset_top = 0x7f0701e1
com.iptv.android.dev:dimen/m3_navigation_item_shape_inset_start = 0x7f0701e0
com.iptv.android.dev:dimen/m3_navigation_item_shape_inset_end = 0x7f0701df
com.iptv.android.dev:style/Widget.Design.AppBarLayout = 0x7f11036c
com.iptv.android.dev:color/m3_timepicker_time_input_stroke_color = 0x7f060212
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1103df
com.iptv.android.dev:dimen/m3_navigation_item_icon_padding = 0x7f0701dd
com.iptv.android.dev:attr/materialSwitchStyle = 0x7f0402e8
com.iptv.android.dev:color/material_dynamic_neutral40 = 0x7f060220
com.iptv.android.dev:drawable/$m3_avd_hide_password__0 = 0x7f080006
com.iptv.android.dev:dimen/m3_fab_border_width = 0x7f0701d2
com.iptv.android.dev:attr/ttcIndex = 0x7f04049f
com.iptv.android.dev:dimen/m3_extended_fab_start_padding = 0x7f0701d0
com.iptv.android.dev:integer/mtrl_calendar_header_orientation = 0x7f0b0033
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001b
com.iptv.android.dev:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701c5
com.iptv.android.dev:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0701c1
com.iptv.android.dev:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1102bc
com.iptv.android.dev:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0701bf
com.iptv.android.dev:attr/telltales_tailColor = 0x7f04041d
com.iptv.android.dev:attr/fontFamily = 0x7f0401f2
com.iptv.android.dev:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0701bc
com.iptv.android.dev:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f0701b3
com.iptv.android.dev:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f0701af
com.iptv.android.dev:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f0701ac
com.iptv.android.dev:macro/m3_comp_fab_primary_large_container_shape = 0x7f0e003a
com.iptv.android.dev:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f0701ab
com.iptv.android.dev:style/Widget.MaterialComponents.BottomSheet = 0x7f11042d
com.iptv.android.dev:color/material_personalized_color_primary_container = 0x7f060288
com.iptv.android.dev:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f0701aa
com.iptv.android.dev:integer/mtrl_view_gone = 0x7f0b0042
com.iptv.android.dev:drawable/exo_icon_play = 0x7f0800ae
com.iptv.android.dev:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070247
com.iptv.android.dev:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f0701a9
com.iptv.android.dev:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f0701a0
com.iptv.android.dev:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f0701a4
com.iptv.android.dev:string/action_delete = 0x7f10001d
com.iptv.android.dev:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f0701a3
com.iptv.android.dev:layout/design_menu_item_action_area = 0x7f0d0023
com.iptv.android.dev:attr/floatingActionButtonLargeStyle = 0x7f0401d1
com.iptv.android.dev:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f0701a1
com.iptv.android.dev:color/m3_sys_color_light_inverse_primary = 0x7f0601d1
com.iptv.android.dev:color/m3_chip_ripple_color = 0x7f060089
com.iptv.android.dev:attr/materialCalendarDay = 0x7f0402c7
com.iptv.android.dev:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f07019e
com.iptv.android.dev:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f07019d
com.iptv.android.dev:string/call_notification_screening_text = 0x7f100044
com.iptv.android.dev:color/mtrl_error = 0x7f0602c3
com.iptv.android.dev:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0602b3
com.iptv.android.dev:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f07019c
com.iptv.android.dev:string/exo_controls_fullscreen_enter_description = 0x7f100097
com.iptv.android.dev:dimen/mtrl_progress_circular_size = 0x7f0702f5
com.iptv.android.dev:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f07019a
com.iptv.android.dev:string/settings_parental = 0x7f100185
com.iptv.android.dev:color/m3_highlighted_text = 0x7f0600a3
com.iptv.android.dev:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f070196
com.iptv.android.dev:string/exo_controls_previous_description = 0x7f1000a0
com.iptv.android.dev:dimen/m3_comp_search_bar_container_elevation = 0x7f070190
com.iptv.android.dev:color/material_personalized_color_background = 0x7f060273
com.iptv.android.dev:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f07018b
com.iptv.android.dev:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f070188
com.iptv.android.dev:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f070187
com.iptv.android.dev:id/mtrl_internal_children_alpha_tag = 0x7f0a0145
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f070184
com.iptv.android.dev:drawable/$m3_avd_show_password__0 = 0x7f080009
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f070183
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f110096
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1101ee
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f070181
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0e0081
com.iptv.android.dev:style/Widget.Material3.PopupMenu.Overflow = 0x7f1103f6
com.iptv.android.dev:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f07017c
com.iptv.android.dev:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f110086
com.iptv.android.dev:attr/cardPreventCornerOverlap = 0x7f0400a2
com.iptv.android.dev:drawable/ic_clock_black_24dp = 0x7f0800df
com.iptv.android.dev:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f07017a
com.iptv.android.dev:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f070176
com.iptv.android.dev:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f070171
com.iptv.android.dev:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f07016f
com.iptv.android.dev:attr/contentDescription = 0x7f040129
com.iptv.android.dev:attr/hideOnContentScroll = 0x7f040210
com.iptv.android.dev:dimen/m3_comp_navigation_rail_container_elevation = 0x7f07016c
com.iptv.android.dev:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0e0009
com.iptv.android.dev:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f070169
com.iptv.android.dev:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f070164
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Title.Text = 0x7f11015d
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f110341
com.iptv.android.dev:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f070162
com.iptv.android.dev:string/exo_track_selection_none = 0x7f1000bf
com.iptv.android.dev:dimen/m3_comp_navigation_bar_icon_size = 0x7f070161
com.iptv.android.dev:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f070160
com.iptv.android.dev:attr/trackDecoration = 0x7f040490
com.iptv.android.dev:style/Widget.Material3.NavigationRailView = 0x7f1103ef
com.iptv.android.dev:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702e6
com.iptv.android.dev:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f07015b
com.iptv.android.dev:color/material_dynamic_neutral_variant80 = 0x7f060231
com.iptv.android.dev:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0e0036
com.iptv.android.dev:dimen/m3_comp_input_chip_container_elevation = 0x7f070154
com.iptv.android.dev:string/abc_action_mode_done = 0x7f100003
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary99 = 0x7f0600f3
com.iptv.android.dev:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f070153
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0e0072
com.iptv.android.dev:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f070150
com.iptv.android.dev:dimen/m3_comp_filled_card_icon_size = 0x7f07014c
com.iptv.android.dev:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600a7
com.iptv.android.dev:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f07014a
com.iptv.android.dev:styleable/NavDeepLink = 0x7f120069
com.iptv.android.dev:dimen/m3_comp_filled_card_container_elevation = 0x7f070148
com.iptv.android.dev:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070147
com.iptv.android.dev:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0e0013
com.iptv.android.dev:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f07013c
com.iptv.android.dev:dimen/m3_btn_padding_left = 0x7f0700fd
com.iptv.android.dev:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f07013b
com.iptv.android.dev:id/scale = 0x7f0a0194
com.iptv.android.dev:dimen/m3_comp_fab_primary_container_height = 0x7f07013a
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f070136
com.iptv.android.dev:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f11026f
com.iptv.android.dev:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0701bb
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1103c7
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f070134
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f070133
com.iptv.android.dev:color/material_dynamic_secondary100 = 0x7f060244
com.iptv.android.dev:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0e0137
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f070132
com.iptv.android.dev:dimen/m3_comp_elevated_card_icon_size = 0x7f07012f
com.iptv.android.dev:styleable/LinearLayoutCompat = 0x7f120048
com.iptv.android.dev:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f110089
com.iptv.android.dev:dimen/m3_comp_elevated_button_container_elevation = 0x7f07012c
com.iptv.android.dev:layout/notification_template_big_media = 0x7f0d0071
com.iptv.android.dev:attr/layout_dodgeInsetEdges = 0x7f040291
com.iptv.android.dev:dimen/material_emphasis_medium = 0x7f070251
com.iptv.android.dev:attr/extendMotionSpec = 0x7f0401b8
com.iptv.android.dev:color/material_dynamic_neutral99 = 0x7f060227
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f070235
com.iptv.android.dev:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f070128
com.iptv.android.dev:attr/materialSearchBarStyle = 0x7f0402e3
com.iptv.android.dev:drawable/exo_controls_shuffle_on = 0x7f080094
com.iptv.android.dev:dimen/m3_comp_circular_progress_indicator_active_indicator_width = 0x7f070127
com.iptv.android.dev:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f070126
com.iptv.android.dev:attr/layout_constraintTop_creator = 0x7f040287
com.iptv.android.dev:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0e0095
com.iptv.android.dev:drawable/mtrl_switch_thumb_pressed = 0x7f080120
com.iptv.android.dev:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700c6
com.iptv.android.dev:dimen/tooltip_margin = 0x7f07033f
com.iptv.android.dev:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f070124
com.iptv.android.dev:drawable/exo_notification_pause = 0x7f0800ba
com.iptv.android.dev:color/m3_button_background_color_selector = 0x7f06007b
com.iptv.android.dev:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f07011f
com.iptv.android.dev:string/mtrl_switch_track_decoration_path = 0x7f10013f
com.iptv.android.dev:layout/mtrl_calendar_days_of_week = 0x7f0d0056
com.iptv.android.dev:dimen/m3_chip_dragged_translation_z = 0x7f070119
com.iptv.android.dev:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f110012
com.iptv.android.dev:style/Widget.MaterialComponents.CheckedTextView = 0x7f11043c
com.iptv.android.dev:dimen/m3_chip_disabled_translation_z = 0x7f070118
com.iptv.android.dev:dimen/m3_chip_checked_hovered_translation_z = 0x7f070116
com.iptv.android.dev:style/Widget.Design.BottomSheet.Modal = 0x7f11036e
com.iptv.android.dev:dimen/m3_carousel_gone_size = 0x7f070112
com.iptv.android.dev:dimen/m3_carousel_extra_small_item_size = 0x7f070111
com.iptv.android.dev:dimen/m3_card_elevation = 0x7f07010d
com.iptv.android.dev:drawable/exo_ic_fullscreen_enter = 0x7f08009d
com.iptv.android.dev:dimen/m3_card_elevated_hovered_z = 0x7f07010c
com.iptv.android.dev:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f07014e
com.iptv.android.dev:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0a0203
com.iptv.android.dev:style/ShapeAppearance.Material3.Tooltip = 0x7f1101ac
com.iptv.android.dev:dimen/m3_btn_text_btn_icon_padding_right = 0x7f070102
com.iptv.android.dev:id/material_hour_tv = 0x7f0a0122
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Caption = 0x7f11001a
com.iptv.android.dev:id/exo_bottom_bar = 0x7f0a00ab
com.iptv.android.dev:drawable/exo_styled_controls_vr = 0x7f0800d6
com.iptv.android.dev:dimen/m3_btn_padding_top = 0x7f0700ff
com.iptv.android.dev:attr/scrimVisibleHeightTrigger = 0x7f04038e
com.iptv.android.dev:style/Base.V22.Theme.AppCompat.Light = 0x7f1100b1
com.iptv.android.dev:dimen/m3_btn_padding_right = 0x7f0700fe
com.iptv.android.dev:color/m3_ref_palette_primary99 = 0x7f06013f
com.iptv.android.dev:dimen/m3_btn_padding_bottom = 0x7f0700fc
com.iptv.android.dev:dimen/m3_btn_max_width = 0x7f0700fb
com.iptv.android.dev:dimen/m3_btn_icon_only_min_width = 0x7f0700f9
com.iptv.android.dev:font/roboto_medium_numbers = 0x7f090000
com.iptv.android.dev:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f070152
com.iptv.android.dev:dimen/m3_btn_icon_only_default_padding = 0x7f0700f6
com.iptv.android.dev:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.iptv.android.dev:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0702f9
com.iptv.android.dev:dimen/m3_btn_icon_btn_padding_left = 0x7f0700f4
com.iptv.android.dev:dimen/m3_btn_elevated_btn_elevation = 0x7f0700f2
com.iptv.android.dev:id/save_overlay_view = 0x7f0a0192
com.iptv.android.dev:attr/actionModeBackground = 0x7f040011
com.iptv.android.dev:dimen/m3_btn_dialog_btn_spacing = 0x7f0700ef
com.iptv.android.dev:dimen/m3_toolbar_text_size_title = 0x7f07023c
com.iptv.android.dev:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f070262
com.iptv.android.dev:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0701c0
com.iptv.android.dev:dimen/mtrl_extended_fab_elevation = 0x7f0702c5
com.iptv.android.dev:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700ed
com.iptv.android.dev:dimen/tooltip_y_offset_non_touch = 0x7f070343
com.iptv.android.dev:dimen/m3_bottomappbar_height = 0x7f0700ec
com.iptv.android.dev:color/m3_sys_color_light_inverse_on_surface = 0x7f0601d0
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f070180
com.iptv.android.dev:string/content_see_all = 0x7f100061
com.iptv.android.dev:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f0700bd
com.iptv.android.dev:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700e8
com.iptv.android.dev:color/m3_sys_color_light_surface_container_highest = 0x7f0601e8
com.iptv.android.dev:dimen/mtrl_fab_min_touch_target = 0x7f0702d3
com.iptv.android.dev:attr/layout_editor_absoluteY = 0x7f040293
com.iptv.android.dev:attr/coplanarSiblingViewId = 0x7f04013c
com.iptv.android.dev:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700e7
com.iptv.android.dev:style/Widget.MaterialComponents.FloatingActionButton = 0x7f11044c
com.iptv.android.dev:color/material_personalized_primary_text_disable_only = 0x7f0602a4
com.iptv.android.dev:color/m3_button_outline_color_selector = 0x7f06007d
com.iptv.android.dev:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700e2
com.iptv.android.dev:style/Base.Theme.AppCompat.Light = 0x7f110052
com.iptv.android.dev:drawable/abc_seekbar_track_material = 0x7f080064
com.iptv.android.dev:dimen/splashscreen_icon_size_no_background = 0x7f07033b
com.iptv.android.dev:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.iptv.android.dev:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0e00ee
com.iptv.android.dev:drawable/design_ic_visibility_off = 0x7f080085
com.iptv.android.dev:style/Theme.Material3.DayNight.NoActionBar = 0x7f110270
com.iptv.android.dev:dimen/m3_badge_with_text_size = 0x7f0700dc
com.iptv.android.dev:dimen/m3_badge_vertical_offset = 0x7f0700d9
com.iptv.android.dev:attr/popupMenuBackground = 0x7f040369
com.iptv.android.dev:color/material_dynamic_neutral_variant20 = 0x7f06022b
com.iptv.android.dev:id/accessibility_custom_action_15 = 0x7f0a0017
com.iptv.android.dev:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f0701b0
com.iptv.android.dev:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700d5
com.iptv.android.dev:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700d4
com.iptv.android.dev:color/material_dynamic_primary80 = 0x7f06023e
com.iptv.android.dev:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700cf
com.iptv.android.dev:string/mtrl_picker_navigate_to_year_description = 0x7f100125
com.iptv.android.dev:dimen/m3_appbar_size_medium = 0x7f0700ce
com.iptv.android.dev:attr/show_timeout = 0x7f0403b9
com.iptv.android.dev:dimen/m3_appbar_size_compact = 0x7f0700cc
com.iptv.android.dev:attr/scrimBackground = 0x7f04038d
com.iptv.android.dev:dimen/m3_appbar_scrim_height_trigger = 0x7f0700c9
com.iptv.android.dev:attr/errorIconTint = 0x7f0401a7
com.iptv.android.dev:attr/framePosition = 0x7f040200
com.iptv.android.dev:dimen/m3_alert_dialog_elevation = 0x7f0700c3
com.iptv.android.dev:attr/errorEnabled = 0x7f0401a5
com.iptv.android.dev:color/mtrl_choice_chip_ripple_color = 0x7f0602c1
com.iptv.android.dev:dimen/m3_alert_dialog_corner_size = 0x7f0700c2
com.iptv.android.dev:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700c0
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0e0162
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary70 = 0x7f0600e2
com.iptv.android.dev:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f070317
com.iptv.android.dev:dimen/exo_styled_controls_padding = 0x7f0700ab
com.iptv.android.dev:dimen/hint_pressed_alpha_material_dark = 0x7f0700bb
com.iptv.android.dev:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701e9
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Menu = 0x7f110027
com.iptv.android.dev:dimen/highlight_alpha_material_light = 0x7f0700b8
com.iptv.android.dev:id/cut = 0x7f0a007f
com.iptv.android.dev:dimen/fastscroll_minimum_range = 0x7f0700b5
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1101c4
com.iptv.android.dev:style/Widget.Compat.NotificationActionText = 0x7f11036b
com.iptv.android.dev:layout/mtrl_layout_snackbar_include = 0x7f0d005f
com.iptv.android.dev:dimen/exo_styled_progress_layout_height = 0x7f0700b0
com.iptv.android.dev:dimen/exo_styled_bottom_bar_height = 0x7f0700a8
com.iptv.android.dev:string/character_counter_overflowed_content_description = 0x7f100050
com.iptv.android.dev:style/ExoStyledControls.Button = 0x7f110133
com.iptv.android.dev:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700df
com.iptv.android.dev:styleable/NavigationRailView = 0x7f12006f
com.iptv.android.dev:color/m3_ref_palette_tertiary99 = 0x7f060159
com.iptv.android.dev:dimen/exo_small_icon_horizontal_margin = 0x7f0700a4
com.iptv.android.dev:dimen/abc_action_bar_default_height_material = 0x7f070002
com.iptv.android.dev:style/Platform.MaterialComponents = 0x7f11016b
com.iptv.android.dev:dimen/exo_settings_offset = 0x7f0700a0
com.iptv.android.dev:dimen/m3_comp_navigation_drawer_container_width = 0x7f070163
com.iptv.android.dev:drawable/ic_mtrl_chip_close_circle = 0x7f0800e8
com.iptv.android.dev:color/m3_button_foreground_color_selector = 0x7f06007c
com.iptv.android.dev:attr/textAppearanceLineHeightEnabled = 0x7f040438
com.iptv.android.dev:dimen/exo_media_button_width = 0x7f07009b
com.iptv.android.dev:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080040
com.iptv.android.dev:dimen/exo_icon_text_size = 0x7f070099
com.iptv.android.dev:dimen/exo_icon_size = 0x7f070098
com.iptv.android.dev:dimen/exo_icon_padding_bottom = 0x7f070097
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f070186
com.iptv.android.dev:dimen/exo_icon_padding = 0x7f070096
com.iptv.android.dev:id/end_padder = 0x7f0a00a3
com.iptv.android.dev:string/mtrl_picker_text_input_date_range_end_hint = 0x7f10012f
com.iptv.android.dev:attr/circularProgressIndicatorStyle = 0x7f0400cb
com.iptv.android.dev:dimen/exo_error_message_text_size = 0x7f070094
com.iptv.android.dev:dimen/exo_error_message_text_padding_horizontal = 0x7f070092
com.iptv.android.dev:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0e005b
com.iptv.android.dev:style/TextAppearance.Material3.BodySmall = 0x7f11021e
com.iptv.android.dev:dimen/exo_error_message_margin_bottom = 0x7f070091
com.iptv.android.dev:attr/listPopupWindowStyle = 0x7f0402ae
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f110164
com.iptv.android.dev:drawable/btn_checkbox_checked_mtrl = 0x7f080079
com.iptv.android.dev:dimen/disabled_alpha_material_dark = 0x7f07008e
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f110457
com.iptv.android.dev:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f110064
com.iptv.android.dev:dimen/design_tab_scrollable_min_width = 0x7f07008a
com.iptv.android.dev:attr/touch_target_height = 0x7f04048a
com.iptv.android.dev:dimen/design_tab_max_width = 0x7f070089
com.iptv.android.dev:attr/errorAccessibilityLiveRegion = 0x7f0401a3
com.iptv.android.dev:dimen/design_snackbar_text_size = 0x7f070088
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.Display = 0x7f11048c
com.iptv.android.dev:dimen/design_snackbar_min_width = 0x7f070084
com.iptv.android.dev:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700ea
com.iptv.android.dev:dimen/design_snackbar_max_width = 0x7f070083
com.iptv.android.dev:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070082
com.iptv.android.dev:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f1101a0
com.iptv.android.dev:attr/preserveIconSpacing = 0x7f040371
com.iptv.android.dev:color/material_personalized_color_on_surface = 0x7f060280
com.iptv.android.dev:attr/textAppearanceHeadline1 = 0x7f04042b
com.iptv.android.dev:anim/m3_motion_fade_exit = 0x7f010024
com.iptv.android.dev:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f070193
com.iptv.android.dev:dimen/design_snackbar_action_text_color_alpha = 0x7f07007f
com.iptv.android.dev:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080060
com.iptv.android.dev:attr/shapeAppearance = 0x7f04039d
com.iptv.android.dev:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0701ba
com.iptv.android.dev:dimen/design_snackbar_action_inline_max_width = 0x7f07007e
com.iptv.android.dev:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700c7
com.iptv.android.dev:id/snackbar_text = 0x7f0a01b0
com.iptv.android.dev:dimen/design_navigation_item_vertical_padding = 0x7f07007a
com.iptv.android.dev:dimen/design_fab_size_mini = 0x7f070071
com.iptv.android.dev:dimen/design_fab_border_width = 0x7f07006e
com.iptv.android.dev:attr/itemMinHeight = 0x7f04023d
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f11028a
com.iptv.android.dev:dimen/design_bottom_navigation_text_size = 0x7f07006a
com.iptv.android.dev:attr/verticalOffsetWithText = 0x7f0404a9
com.iptv.android.dev:dimen/design_bottom_navigation_label_padding = 0x7f070067
com.iptv.android.dev:drawable/notification_oversize_large_icon_bg = 0x7f080132
com.iptv.android.dev:dimen/design_bottom_navigation_item_min_width = 0x7f070066
com.iptv.android.dev:color/material_blue_grey_950 = 0x7f060216
com.iptv.android.dev:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f07012d
com.iptv.android.dev:dimen/design_bottom_navigation_elevation = 0x7f070062
com.iptv.android.dev:color/bright_foreground_inverse_material_dark = 0x7f06002a
com.iptv.android.dev:id/dropdown_menu = 0x7f0a0098
com.iptv.android.dev:dimen/m3_badge_with_text_vertical_padding = 0x7f0700de
com.iptv.android.dev:dimen/design_bottom_navigation_active_item_min_width = 0x7f070060
com.iptv.android.dev:style/TextAppearance.AppCompat.Display3 = 0x7f1101cd
com.iptv.android.dev:dimen/def_drawer_elevation = 0x7f07005d
com.iptv.android.dev:color/m3_text_button_ripple_color_selector = 0x7f060203
com.iptv.android.dev:styleable/CollapsingToolbarLayout = 0x7f120023
com.iptv.android.dev:dimen/compat_notification_large_icon_max_width = 0x7f07005c
com.iptv.android.dev:dimen/m3_comp_suggestion_chip_container_height = 0x7f0701a7
com.iptv.android.dev:color/m3_sys_color_light_on_surface_variant = 0x7f0601db
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f11045d
com.iptv.android.dev:dimen/compat_control_corner_material = 0x7f07005a
com.iptv.android.dev:style/Theme.Material3.Dark = 0x7f110262
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f110300
com.iptv.android.dev:id/view_tree_saved_state_registry_owner = 0x7f0a0204
com.iptv.android.dev:attr/drawableEndCompat = 0x7f04017d
com.iptv.android.dev:dimen/compat_button_inset_vertical_material = 0x7f070057
com.iptv.android.dev:attr/useMaterialThemeColors = 0x7f0404a4
com.iptv.android.dev:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080058
com.iptv.android.dev:color/material_personalized_color_secondary = 0x7f06028c
com.iptv.android.dev:style/Widget.Material3.TabLayout.Secondary = 0x7f110409
com.iptv.android.dev:dimen/clock_face_margin_start = 0x7f070055
com.iptv.android.dev:attr/contentInsetEnd = 0x7f04012a
com.iptv.android.dev:dimen/cardview_default_elevation = 0x7f070053
com.iptv.android.dev:id/accessibility_custom_action_4 = 0x7f0a002a
com.iptv.android.dev:dimen/cardview_compat_inset_shadow = 0x7f070052
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0e0083
com.iptv.android.dev:color/material_dynamic_tertiary80 = 0x7f060258
com.iptv.android.dev:dimen/abc_text_size_large_material = 0x7f070048
com.iptv.android.dev:dimen/abc_text_size_headline_material = 0x7f070047
com.iptv.android.dev:dimen/abc_text_size_button_material = 0x7f070041
com.iptv.android.dev:dimen/abc_star_small = 0x7f07003d
com.iptv.android.dev:attr/linearProgressIndicatorStyle = 0x7f0402a6
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600c5
com.iptv.android.dev:dimen/abc_star_big = 0x7f07003b
com.iptv.android.dev:id/wrap = 0x7f0a020c
com.iptv.android.dev:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.iptv.android.dev:macro/m3_comp_date_picker_modal_container_shape = 0x7f0e000f
com.iptv.android.dev:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.iptv.android.dev:macro/m3_comp_search_view_header_input_text_type = 0x7f0e00f7
com.iptv.android.dev:id/exo_error_message = 0x7f0a00b4
com.iptv.android.dev:dimen/abc_search_view_preferred_width = 0x7f070037
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_container_height = 0x7f070131
com.iptv.android.dev:dimen/abc_progress_bar_height_material = 0x7f070035
com.iptv.android.dev:attr/offsetAlignmentMode = 0x7f04033b
com.iptv.android.dev:drawable/mtrl_ic_cancel = 0x7f080113
com.iptv.android.dev:attr/horizontalOffsetWithText = 0x7f04021b
com.iptv.android.dev:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.iptv.android.dev:drawable/exo_styled_controls_shuffle_off = 0x7f0800d1
com.iptv.android.dev:color/material_dynamic_neutral80 = 0x7f060224
com.iptv.android.dev:style/ExoMediaButton.Play = 0x7f11012e
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110167
com.iptv.android.dev:string/exo_download_failed = 0x7f1000b0
com.iptv.android.dev:string/default_popup_window_title = 0x7f100083
com.iptv.android.dev:id/marquee = 0x7f0a0116
com.iptv.android.dev:dimen/abc_list_item_height_material = 0x7f070031
com.iptv.android.dev:attr/dividerVertical = 0x7f040177
com.iptv.android.dev:dimen/abc_floating_window_z = 0x7f07002f
com.iptv.android.dev:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.iptv.android.dev:attr/startIconDrawable = 0x7f0403d7
com.iptv.android.dev:attr/customFloatValue = 0x7f040157
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0e0074
com.iptv.android.dev:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.iptv.android.dev:dimen/material_clock_display_padding = 0x7f07023f
com.iptv.android.dev:dimen/abc_dialog_padding_material = 0x7f070024
com.iptv.android.dev:string/date_picker_switch_to_input_mode = 0x7f100073
com.iptv.android.dev:string/settings_app_title = 0x7f10017b
com.iptv.android.dev:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.iptv.android.dev:string/exo_controls_pause_description = 0x7f10009d
com.iptv.android.dev:dimen/abc_config_prefDialogWidth = 0x7f070017
com.iptv.android.dev:dimen/m3_comp_linear_progress_indicator_active_indicator_height = 0x7f070159
com.iptv.android.dev:string/content_share = 0x7f100062
com.iptv.android.dev:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.iptv.android.dev:style/IPTVText.Title = 0x7f110154
com.iptv.android.dev:string/auth_activation_code_hint = 0x7f10002c
com.iptv.android.dev:attr/transitionDisable = 0x7f040497
com.iptv.android.dev:color/m3_slider_thumb_color = 0x7f060160
com.iptv.android.dev:drawable/abc_cab_background_top_material = 0x7f080038
com.iptv.android.dev:color/design_fab_shadow_start_color = 0x7f060058
com.iptv.android.dev:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.iptv.android.dev:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.iptv.android.dev:string/search_recent = 0x7f10016f
com.iptv.android.dev:string/exo_controls_next_description = 0x7f10009a
com.iptv.android.dev:dimen/abc_action_button_min_height_material = 0x7f07000d
com.iptv.android.dev:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.iptv.android.dev:id/right = 0x7f0a018b
com.iptv.android.dev:attr/navGraph = 0x7f04032e
com.iptv.android.dev:color/warning = 0x7f060319
com.iptv.android.dev:attr/windowSplashScreenAnimatedIcon = 0x7f0404bd
com.iptv.android.dev:color/vector_tint_theme_color = 0x7f060318
com.iptv.android.dev:color/tooltip_background_light = 0x7f060315
com.iptv.android.dev:color/text_secondary = 0x7f060312
com.iptv.android.dev:dimen/m3_comp_badge_size = 0x7f070123
com.iptv.android.dev:color/text_primary = 0x7f060311
com.iptv.android.dev:color/switch_thumb_material_light = 0x7f06030d
com.iptv.android.dev:string/abc_capital_on = 0x7f100007
com.iptv.android.dev:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0701b9
com.iptv.android.dev:color/surface_bright = 0x7f060308
com.iptv.android.dev:drawable/exo_icon_rewind = 0x7f0800b3
com.iptv.android.dev:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f070129
com.iptv.android.dev:dimen/abc_control_padding_material = 0x7f07001a
com.iptv.android.dev:color/secondary_text_default_material_light = 0x7f060302
com.iptv.android.dev:dimen/material_clock_number_text_size = 0x7f070245
com.iptv.android.dev:color/secondary_container = 0x7f060300
com.iptv.android.dev:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0701fb
com.iptv.android.dev:styleable/BaseProgressIndicator = 0x7f120016
com.iptv.android.dev:color/secondary = 0x7f0602ff
com.iptv.android.dev:id/exo_next = 0x7f0a00be
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f11020c
com.iptv.android.dev:attr/colorOnPrimary = 0x7f0400f3
com.iptv.android.dev:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.iptv.android.dev:color/primary_text_disabled_material_light = 0x7f0602fc
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f110023
com.iptv.android.dev:attr/startIconScaleType = 0x7f0403d9
com.iptv.android.dev:color/primary_text_default_material_light = 0x7f0602fa
com.iptv.android.dev:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f07012a
com.iptv.android.dev:color/primary_material_light = 0x7f0602f8
com.iptv.android.dev:attr/itemShapeInsetTop = 0x7f040248
com.iptv.android.dev:macro/m3_comp_badge_color = 0x7f0e0002
com.iptv.android.dev:drawable/exo_icon_repeat_one = 0x7f0800b2
com.iptv.android.dev:color/primary_dark_material_light = 0x7f0602f6
com.iptv.android.dev:color/overlay_light = 0x7f0602f2
com.iptv.android.dev:macro/m3_comp_time_picker_clock_dial_color = 0x7f0e014d
com.iptv.android.dev:attr/region_heightMoreThan = 0x7f04037f
com.iptv.android.dev:drawable/notification_icon_background = 0x7f080131
com.iptv.android.dev:color/on_secondary = 0x7f0602ed
com.iptv.android.dev:color/on_background = 0x7f0602ea
com.iptv.android.dev:color/m3_dark_highlighted_text = 0x7f06008e
com.iptv.android.dev:style/Animation.Material3.SideSheetDialog.Left = 0x7f110008
com.iptv.android.dev:drawable/exo_controls_repeat_all = 0x7f08008f
com.iptv.android.dev:color/notification_action_color_filter = 0x7f0602e7
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f07021c
com.iptv.android.dev:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0602e6
com.iptv.android.dev:attr/contentPaddingLeft = 0x7f040133
com.iptv.android.dev:color/mtrl_textinput_disabled_color = 0x7f0602e3
com.iptv.android.dev:color/mtrl_textinput_default_box_stroke_color = 0x7f0602e2
com.iptv.android.dev:id/video_decoder_gl_surface_view = 0x7f0a0200
com.iptv.android.dev:string/indeterminate = 0x7f1000cf
com.iptv.android.dev:drawable/abc_control_background_material = 0x7f08003a
com.iptv.android.dev:attr/snackbarTextViewStyle = 0x7f0403ca
com.iptv.android.dev:color/mtrl_tabs_ripple_color = 0x7f0602e0
com.iptv.android.dev:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f07016e
com.iptv.android.dev:styleable/Motion = 0x7f120062
com.iptv.android.dev:color/mtrl_tabs_legacy_text_color_selector = 0x7f0602df
com.iptv.android.dev:string/loading_content = 0x7f1000db
com.iptv.android.dev:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f07026f
com.iptv.android.dev:dimen/abc_text_size_medium_material = 0x7f070049
com.iptv.android.dev:dimen/m3_comp_navigation_drawer_icon_size = 0x7f070166
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1101e3
com.iptv.android.dev:style/TextAppearance.AppCompat.Tooltip = 0x7f1101e2
com.iptv.android.dev:color/mtrl_tabs_icon_color_selector_colored = 0x7f0602de
com.iptv.android.dev:dimen/disabled_alpha_material_light = 0x7f07008f
com.iptv.android.dev:color/mtrl_switch_track_tint = 0x7f0602db
com.iptv.android.dev:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f080106
com.iptv.android.dev:color/mtrl_popupmenu_overlay_color = 0x7f0602d6
com.iptv.android.dev:color/mtrl_outlined_stroke_color = 0x7f0602d5
com.iptv.android.dev:color/mtrl_outlined_icon_tint = 0x7f0602d4
com.iptv.android.dev:dimen/mtrl_switch_thumb_icon_size = 0x7f070313
com.iptv.android.dev:string/player_subtitles = 0x7f100162
com.iptv.android.dev:color/mtrl_navigation_item_background_color = 0x7f0602cf
com.iptv.android.dev:style/Widget.AppCompat.ActionButton = 0x7f110326
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f11028c
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f11017d
com.iptv.android.dev:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f1102ab
com.iptv.android.dev:attr/actionMenuTextAppearance = 0x7f04000f
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f110191
com.iptv.android.dev:dimen/mtrl_slider_label_radius = 0x7f070303
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600d9
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light = 0x7f11006e
com.iptv.android.dev:dimen/mtrl_btn_disabled_z = 0x7f070278
com.iptv.android.dev:attr/motionProgress = 0x7f040327
com.iptv.android.dev:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f110279
com.iptv.android.dev:color/mtrl_navigation_bar_colored_item_tint = 0x7f0602cb
com.iptv.android.dev:layout/abc_alert_dialog_title_material = 0x7f0d000a
com.iptv.android.dev:color/mtrl_filled_background_color = 0x7f0602c7
com.iptv.android.dev:color/mtrl_fab_ripple_color = 0x7f0602c6
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f11003d
com.iptv.android.dev:color/mtrl_choice_chip_text_color = 0x7f0602c2
com.iptv.android.dev:color/mtrl_choice_chip_background_color = 0x7f0602c0
com.iptv.android.dev:color/mtrl_chip_surface_color = 0x7f0602be
com.iptv.android.dev:color/mtrl_chip_close_icon_tint = 0x7f0602bd
com.iptv.android.dev:color/abc_tint_btn_checkable = 0x7f060013
com.iptv.android.dev:color/mtrl_card_view_foreground = 0x7f0602ba
com.iptv.android.dev:color/mtrl_calendar_selected_range = 0x7f0602b9
com.iptv.android.dev:attr/tickRadiusActive = 0x7f040467
com.iptv.android.dev:style/Widget.Material3.SearchView.Toolbar = 0x7f1103fd
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f11045c
com.iptv.android.dev:color/mtrl_calendar_item_stroke_color = 0x7f0602b8
com.iptv.android.dev:color/mtrl_btn_bg_color_selector = 0x7f0602b0
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f110282
com.iptv.android.dev:string/snackbar_dismiss = 0x7f100194
com.iptv.android.dev:layout/mtrl_layout_snackbar = 0x7f0d005e
com.iptv.android.dev:attr/windowSplashScreenBackground = 0x7f0404bf
com.iptv.android.dev:color/material_timepicker_modebutton_tint = 0x7f0602af
com.iptv.android.dev:color/m3_timepicker_button_ripple_color = 0x7f06020a
com.iptv.android.dev:color/material_timepicker_button_stroke = 0x7f0602ac
com.iptv.android.dev:drawable/ic_mtrl_chip_checked_black = 0x7f0800e6
com.iptv.android.dev:color/material_slider_inactive_tick_marks_color = 0x7f0602a8
com.iptv.android.dev:id/titleDividerNoCustom = 0x7f0a01ed
com.iptv.android.dev:color/material_personalized_hint_foreground_inverse = 0x7f0602a2
com.iptv.android.dev:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0602a0
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Title = 0x7f11002f
com.iptv.android.dev:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003
com.iptv.android.dev:dimen/m3_comp_search_view_container_elevation = 0x7f070194
com.iptv.android.dev:style/Base.Theme.SplashScreen.DayNight = 0x7f110079
com.iptv.android.dev:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0e00af
com.iptv.android.dev:color/material_personalized_color_tertiary = 0x7f06029a
com.iptv.android.dev:color/material_dynamic_neutral_variant30 = 0x7f06022c
com.iptv.android.dev:color/material_personalized_color_surface_variant = 0x7f060299
com.iptv.android.dev:color/material_personalized_color_surface_container_lowest = 0x7f060296
com.iptv.android.dev:color/abc_primary_text_material_dark = 0x7f06000b
com.iptv.android.dev:string/language_english = 0x7f1000d4
com.iptv.android.dev:color/material_personalized_color_primary_text = 0x7f06028a
com.iptv.android.dev:string/exo_download_paused_for_network = 0x7f1000b3
com.iptv.android.dev:color/material_personalized_color_primary = 0x7f060287
com.iptv.android.dev:color/material_personalized_color_on_surface_inverse = 0x7f060281
com.iptv.android.dev:dimen/mtrl_textinput_start_icon_margin_end = 0x7f07031f
com.iptv.android.dev:color/material_personalized_color_on_secondary = 0x7f06027e
com.iptv.android.dev:color/material_personalized_color_on_primary_container = 0x7f06027d
com.iptv.android.dev:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701c3
com.iptv.android.dev:color/material_personalized_color_on_error_container = 0x7f06027b
com.iptv.android.dev:color/button_material_light = 0x7f060032
com.iptv.android.dev:color/material_personalized_color_on_error = 0x7f06027a
com.iptv.android.dev:attr/cornerFamilyBottomRight = 0x7f04013f
com.iptv.android.dev:color/material_personalized_color_control_highlight = 0x7f060275
com.iptv.android.dev:attr/textAppearanceTitleSmall = 0x7f040445
com.iptv.android.dev:color/material_on_surface_stroke = 0x7f060270
com.iptv.android.dev:color/material_on_surface_emphasis_medium = 0x7f06026f
com.iptv.android.dev:style/Animation.Material3.SideSheetDialog = 0x7f110007
com.iptv.android.dev:dimen/design_bottom_navigation_item_max_width = 0x7f070065
com.iptv.android.dev:color/material_on_primary_disabled = 0x7f06026a
com.iptv.android.dev:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0e011e
com.iptv.android.dev:style/TextAppearance.Material3.LabelSmall = 0x7f110227
com.iptv.android.dev:color/material_harmonized_color_on_error_container = 0x7f060266
com.iptv.android.dev:styleable/AppCompatTheme = 0x7f120013
com.iptv.android.dev:dimen/material_clock_period_toggle_height = 0x7f070246
com.iptv.android.dev:color/material_harmonized_color_on_error = 0x7f060265
com.iptv.android.dev:attr/number = 0x7f040339
com.iptv.android.dev:color/m3_bottom_sheet_drag_handle_color = 0x7f06007a
com.iptv.android.dev:color/material_harmonized_color_error_container = 0x7f060264
com.iptv.android.dev:dimen/abc_text_size_title_material = 0x7f07004f
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0e00d1
com.iptv.android.dev:string/search_no_results_message = 0x7f10016b
com.iptv.android.dev:color/material_harmonized_color_error = 0x7f060263
com.iptv.android.dev:attr/alpha = 0x7f04002f
com.iptv.android.dev:color/material_grey_800 = 0x7f060260
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f110162
com.iptv.android.dev:color/material_grey_600 = 0x7f06025f
com.iptv.android.dev:color/material_grey_100 = 0x7f06025c
com.iptv.android.dev:attr/backgroundColor = 0x7f04004b
com.iptv.android.dev:color/material_dynamic_tertiary99 = 0x7f06025b
com.iptv.android.dev:attr/flow_firstVerticalStyle = 0x7f0401e1
com.iptv.android.dev:dimen/m3_sys_elevation_level2 = 0x7f07020e
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600b5
com.iptv.android.dev:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070157
com.iptv.android.dev:color/material_dynamic_tertiary90 = 0x7f060259
com.iptv.android.dev:attr/windowSplashScreenIconBackgroundColor = 0x7f0404c0
com.iptv.android.dev:attr/errorShown = 0x7f0401a9
com.iptv.android.dev:attr/fontProviderAuthority = 0x7f0401f3
com.iptv.android.dev:color/material_dynamic_tertiary50 = 0x7f060255
com.iptv.android.dev:dimen/m3_comp_input_chip_container_height = 0x7f070155
com.iptv.android.dev:color/material_dynamic_tertiary40 = 0x7f060254
com.iptv.android.dev:color/material_dynamic_tertiary100 = 0x7f060251
com.iptv.android.dev:attr/listLayout = 0x7f0402ac
com.iptv.android.dev:drawable/m3_avd_hide_password = 0x7f0800ef
com.iptv.android.dev:style/Base.Theme.Material3.Dark = 0x7f110059
com.iptv.android.dev:integer/mtrl_card_anim_duration_ms = 0x7f0b0037
com.iptv.android.dev:color/material_dynamic_tertiary0 = 0x7f06024f
com.iptv.android.dev:color/material_dynamic_secondary99 = 0x7f06024e
com.iptv.android.dev:color/material_dynamic_secondary80 = 0x7f06024b
com.iptv.android.dev:color/material_dynamic_secondary60 = 0x7f060249
com.iptv.android.dev:color/material_dynamic_secondary40 = 0x7f060247
com.iptv.android.dev:color/switch_thumb_normal_material_light = 0x7f06030f
com.iptv.android.dev:color/material_dynamic_secondary20 = 0x7f060245
com.iptv.android.dev:string/date_picker_scroll_to_later_years = 0x7f100070
com.iptv.android.dev:color/material_dynamic_secondary10 = 0x7f060243
com.iptv.android.dev:drawable/abc_popup_background_mtrl_mult = 0x7f080059
com.iptv.android.dev:attr/rippleColor = 0x7f040387
com.iptv.android.dev:color/material_dynamic_primary90 = 0x7f06023f
com.iptv.android.dev:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1102e0
com.iptv.android.dev:drawable/exo_icon_pause = 0x7f0800ad
com.iptv.android.dev:styleable/MaterialCheckBox = 0x7f120054
com.iptv.android.dev:layout/abc_action_bar_title_item = 0x7f0d0000
com.iptv.android.dev:color/material_dynamic_primary20 = 0x7f060238
com.iptv.android.dev:dimen/notification_content_margin_start = 0x7f07032a
com.iptv.android.dev:id/expanded_menu = 0x7f0a00d9
com.iptv.android.dev:color/material_dynamic_neutral_variant99 = 0x7f060234
com.iptv.android.dev:attr/enforceTextAppearance = 0x7f04019f
com.iptv.android.dev:color/material_dynamic_neutral_variant60 = 0x7f06022f
com.iptv.android.dev:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0e009c
com.iptv.android.dev:attr/backgroundInsetBottom = 0x7f04004c
com.iptv.android.dev:color/material_dynamic_neutral_variant40 = 0x7f06022d
com.iptv.android.dev:color/m3_sys_color_light_primary_container = 0x7f0601e1
com.iptv.android.dev:color/material_dynamic_neutral60 = 0x7f060222
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f110038
com.iptv.android.dev:color/material_personalized__highlighted_text = 0x7f060271
com.iptv.android.dev:attr/customStringValue = 0x7f04015b
com.iptv.android.dev:color/material_dynamic_neutral0 = 0x7f06021b
com.iptv.android.dev:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f080108
com.iptv.android.dev:id/exo_text = 0x7f0a00d4
com.iptv.android.dev:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f110477
com.iptv.android.dev:macro/m3_comp_switch_selected_focus_track_color = 0x7f0e0124
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f110451
com.iptv.android.dev:color/material_divider_color = 0x7f06021a
com.iptv.android.dev:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1102cf
com.iptv.android.dev:color/material_deep_teal_200 = 0x7f060218
com.iptv.android.dev:attr/listPreferredItemPaddingStart = 0x7f0402b5
com.iptv.android.dev:dimen/mtrl_min_touch_target_size = 0x7f0702de
com.iptv.android.dev:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f11040a
com.iptv.android.dev:styleable/ImageFilterView = 0x7f12003d
com.iptv.android.dev:color/m3_tonal_button_ripple_color_selector = 0x7f060213
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0600fb
com.iptv.android.dev:color/m3_timepicker_secondary_text_button_text_color = 0x7f060211
com.iptv.android.dev:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f100108
com.iptv.android.dev:id/enterAlwaysCollapsed = 0x7f0a00a5
com.iptv.android.dev:color/m3_timepicker_display_text_color = 0x7f06020f
com.iptv.android.dev:color/m3_timepicker_display_ripple_color = 0x7f06020e
com.iptv.android.dev:drawable/exo_notification_next = 0x7f0800b9
com.iptv.android.dev:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1102f8
com.iptv.android.dev:macro/m3_comp_checkbox_selected_icon_color = 0x7f0e000b
com.iptv.android.dev:id/material_timepicker_mode_button = 0x7f0a0129
com.iptv.android.dev:color/m3_timepicker_display_background_color = 0x7f06020d
com.iptv.android.dev:dimen/m3_btn_stroke_size = 0x7f070100
com.iptv.android.dev:color/m3_timepicker_clock_text_color = 0x7f06020c
com.iptv.android.dev:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f110242
com.iptv.android.dev:id/accessibility_custom_action_23 = 0x7f0a0020
com.iptv.android.dev:attr/cursorColor = 0x7f040150
com.iptv.android.dev:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f070236
com.iptv.android.dev:color/m3_textfield_stroke_color = 0x7f060208
com.iptv.android.dev:color/m3_textfield_label_color = 0x7f060207
com.iptv.android.dev:styleable/View = 0x7f12009b
com.iptv.android.dev:color/material_dynamic_primary0 = 0x7f060235
com.iptv.android.dev:color/material_dynamic_neutral90 = 0x7f060225
com.iptv.android.dev:id/transition_transform = 0x7f0a01f9
com.iptv.android.dev:color/m3_textfield_input_text_color = 0x7f060206
com.iptv.android.dev:string/error_icon_content_description = 0x7f100089
com.iptv.android.dev:drawable/abc_btn_check_material = 0x7f08002b
com.iptv.android.dev:color/m3_tabs_ripple_color_secondary = 0x7f0601fe
com.iptv.android.dev:attr/actionModePasteDrawable = 0x7f040018
com.iptv.android.dev:color/m3_sys_color_tertiary_fixed_dim = 0x7f0601fa
com.iptv.android.dev:color/m3_sys_color_tertiary_fixed = 0x7f0601f9
com.iptv.android.dev:color/m3_sys_color_primary_fixed_dim = 0x7f0601f6
com.iptv.android.dev:macro/m3_comp_outlined_autocomplete_menu_list_item_selected_container_color = 0x7f0e00a2
com.iptv.android.dev:color/m3_sys_color_primary_fixed = 0x7f0601f5
com.iptv.android.dev:color/m3_ref_palette_primary70 = 0x7f06013b
com.iptv.android.dev:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0601f2
com.iptv.android.dev:color/m3_sys_color_on_primary_fixed_variant = 0x7f0601f0
com.iptv.android.dev:id/material_clock_level = 0x7f0a011d
com.iptv.android.dev:dimen/m3_ripple_focused_alpha = 0x7f0701f1
com.iptv.android.dev:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f110437
com.iptv.android.dev:color/m3_sys_color_light_tertiary_container = 0x7f0601ee
com.iptv.android.dev:color/m3_sys_color_light_tertiary = 0x7f0601ed
com.iptv.android.dev:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.iptv.android.dev:color/material_dynamic_neutral_variant90 = 0x7f060232
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f11030c
com.iptv.android.dev:attr/itemBackground = 0x7f040235
com.iptv.android.dev:color/m3_sys_color_light_surface_container_lowest = 0x7f0601ea
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1103c6
com.iptv.android.dev:attr/chipStartPadding = 0x7f0400c5
com.iptv.android.dev:color/m3_sys_color_light_surface_container_high = 0x7f0601e7
com.iptv.android.dev:color/m3_textfield_filled_background_color = 0x7f060204
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0e0163
com.iptv.android.dev:attr/layout_constraintHeight_min = 0x7f040279
com.iptv.android.dev:drawable/abc_ratingbar_small_material = 0x7f08005c
com.iptv.android.dev:color/m3_sys_color_light_surface = 0x7f0601e4
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog = 0x7f110291
com.iptv.android.dev:dimen/design_bottom_sheet_peek_height_min = 0x7f07006d
com.iptv.android.dev:id/legacy = 0x7f0a010f
com.iptv.android.dev:color/m3_sys_color_light_secondary_container = 0x7f0601e3
com.iptv.android.dev:dimen/mtrl_calendar_navigation_height = 0x7f0702a8
com.iptv.android.dev:attr/colorSurfaceVariant = 0x7f04011b
com.iptv.android.dev:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.iptv.android.dev:style/Platform.AppCompat = 0x7f110169
com.iptv.android.dev:color/m3_sys_color_light_secondary = 0x7f0601e2
com.iptv.android.dev:id/italic = 0x7f0a0107
com.iptv.android.dev:id/fill_vertical = 0x7f0a00dd
com.iptv.android.dev:color/m3_sys_color_light_outline = 0x7f0601de
com.iptv.android.dev:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1102bd
com.iptv.android.dev:id/accessibility_custom_action_21 = 0x7f0a001e
com.iptv.android.dev:color/m3_sys_color_light_on_tertiary_container = 0x7f0601dd
com.iptv.android.dev:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f0701b2
com.iptv.android.dev:color/m3_sys_color_light_on_surface = 0x7f0601da
com.iptv.android.dev:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1102d9
com.iptv.android.dev:attr/mock_label = 0x7f040302
com.iptv.android.dev:color/m3_sys_color_light_on_secondary_container = 0x7f0601d9
com.iptv.android.dev:string/action_save = 0x7f100021
com.iptv.android.dev:color/m3_sys_color_light_on_primary_container = 0x7f0601d7
com.iptv.android.dev:color/m3_sys_color_light_on_primary = 0x7f0601d6
com.iptv.android.dev:style/Widget.Design.CollapsingToolbar = 0x7f11036f
com.iptv.android.dev:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0e0030
com.iptv.android.dev:color/m3_sys_color_light_on_error_container = 0x7f0601d5
com.iptv.android.dev:color/m3_sys_color_dark_on_surface = 0x7f060170
com.iptv.android.dev:drawable/design_ic_visibility = 0x7f080084
com.iptv.android.dev:drawable/exo_styled_controls_audiotrack = 0x7f0800c1
com.iptv.android.dev:color/m3_sys_color_light_on_background = 0x7f0601d3
com.iptv.android.dev:color/m3_sys_color_light_error_container = 0x7f0601cf
com.iptv.android.dev:color/m3_sys_color_light_background = 0x7f0601cd
com.iptv.android.dev:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1103cc
com.iptv.android.dev:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0601cc
com.iptv.android.dev:attr/buttonBarStyle = 0x7f040091
com.iptv.android.dev:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0601cb
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f110196
com.iptv.android.dev:attr/layout_scrollFlags = 0x7f04029e
com.iptv.android.dev:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0601ca
com.iptv.android.dev:drawable/$avd_show_password__2 = 0x7f080005
com.iptv.android.dev:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0601c9
com.iptv.android.dev:id/exo_play = 0x7f0a00c3
com.iptv.android.dev:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0601c8
com.iptv.android.dev:style/Base.V28.Theme.AppCompat = 0x7f1100bb
com.iptv.android.dev:color/m3_sys_color_dynamic_primary_fixed = 0x7f0601c7
com.iptv.android.dev:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1102f4
com.iptv.android.dev:dimen/m3_sys_elevation_level1 = 0x7f07020d
com.iptv.android.dev:dimen/design_appbar_elevation = 0x7f07005e
com.iptv.android.dev:attr/listPreferredItemPaddingRight = 0x7f0402b4
com.iptv.android.dev:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0601c4
com.iptv.android.dev:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0601c3
com.iptv.android.dev:dimen/design_navigation_item_horizontal_padding = 0x7f070078
com.iptv.android.dev:attr/motionPath = 0x7f040325
com.iptv.android.dev:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0601c2
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f110453
com.iptv.android.dev:string/mtrl_picker_date_header_title = 0x7f10011c
com.iptv.android.dev:id/container = 0x7f0a0074
com.iptv.android.dev:dimen/mtrl_progress_circular_radius = 0x7f0702f4
com.iptv.android.dev:attr/elevation = 0x7f040190
com.iptv.android.dev:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0601c1
com.iptv.android.dev:string/exo_controls_rewind_description = 0x7f1000a4
com.iptv.android.dev:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f0701b8
com.iptv.android.dev:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601bf
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601be
com.iptv.android.dev:drawable/notification_tile_bg = 0x7f080135
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601bc
com.iptv.android.dev:color/on_primary = 0x7f0602eb
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601ba
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f110214
com.iptv.android.dev:drawable/icon_background = 0x7f0800ed
com.iptv.android.dev:dimen/notification_action_icon_size = 0x7f070327
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0601b7
com.iptv.android.dev:string/searchview_clear_text_content_description = 0x7f100173
com.iptv.android.dev:id/percent = 0x7f0a017e
com.iptv.android.dev:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0601b5
com.iptv.android.dev:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070168
com.iptv.android.dev:color/m3_sys_color_dynamic_light_secondary = 0x7f0601b4
com.iptv.android.dev:style/ShapeAppearance.Material3.SmallComponent = 0x7f1101ab
com.iptv.android.dev:color/m3_sys_color_dynamic_light_primary = 0x7f0601b2
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f110181
com.iptv.android.dev:attr/drawableTopCompat = 0x7f040184
com.iptv.android.dev:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0601b1
com.iptv.android.dev:dimen/design_bottom_navigation_shadow_height = 0x7f070069
com.iptv.android.dev:drawable/abc_list_divider_material = 0x7f08004c
com.iptv.android.dev:color/m3_tabs_text_color_secondary = 0x7f060200
com.iptv.android.dev:color/m3_sys_color_dynamic_light_outline = 0x7f0601b0
com.iptv.android.dev:attr/fontWeight = 0x7f0401fc
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0601ae
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0601aa
com.iptv.android.dev:attr/fabSize = 0x7f0401c8
com.iptv.android.dev:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f080100
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_primary = 0x7f0601a8
com.iptv.android.dev:attr/buttonTintMode = 0x7f04009c
com.iptv.android.dev:dimen/mtrl_calendar_year_corner = 0x7f0702b2
com.iptv.android.dev:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0e00fa
com.iptv.android.dev:attr/windowFixedWidthMajor = 0x7f0404b8
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_background = 0x7f0601a7
com.iptv.android.dev:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0601a6
com.iptv.android.dev:attr/dividerInsetStart = 0x7f040174
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f08000f
com.iptv.android.dev:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0601a5
com.iptv.android.dev:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0601a4
com.iptv.android.dev:attr/windowActionBarOverlay = 0x7f0404b4
com.iptv.android.dev:color/m3_sys_color_dynamic_light_background = 0x7f0601a3
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080015
com.iptv.android.dev:attr/collapsedTitleGravity = 0x7f0400dc
com.iptv.android.dev:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0602d2
com.iptv.android.dev:dimen/m3_comp_search_bar_avatar_size = 0x7f07018f
com.iptv.android.dev:style/Widget.MaterialComponents.TabLayout = 0x7f110479
com.iptv.android.dev:string/m3_ref_typeface_plain_medium = 0x7f1000e1
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0601a2
com.iptv.android.dev:style/ThemeOverlay.AppCompat.Dialog = 0x7f1102b5
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f06019f
com.iptv.android.dev:dimen/material_emphasis_high_type = 0x7f070250
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f06019d
com.iptv.android.dev:color/material_personalized_color_tertiary_container = 0x7f06029b
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f06019c
com.iptv.android.dev:style/Widget.Material3.SearchBar = 0x7f1103f9
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f06019b
com.iptv.android.dev:color/m3_ref_palette_tertiary100 = 0x7f06014f
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_container = 0x7f06019a
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_secondary = 0x7f060196
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_primary_container = 0x7f060195
com.iptv.android.dev:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0b003b
com.iptv.android.dev:dimen/m3_ripple_hovered_alpha = 0x7f0701f2
com.iptv.android.dev:color/tooltip_background_dark = 0x7f060314
com.iptv.android.dev:id/dragLeft = 0x7f0a0094
com.iptv.android.dev:attr/trackColor = 0x7f04048c
com.iptv.android.dev:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f07026d
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f060190
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f06018f
com.iptv.android.dev:attr/contentInsetStart = 0x7f04012e
com.iptv.android.dev:color/mtrl_on_surface_ripple_color = 0x7f0602d3
com.iptv.android.dev:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1102f5
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_surface = 0x7f06018e
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f06018b
com.iptv.android.dev:dimen/material_clock_face_margin_top = 0x7f070241
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f060186
com.iptv.android.dev:id/search_voice_btn = 0x7f0a01a4
com.iptv.android.dev:id/accessibility_custom_action_0 = 0x7f0a0010
com.iptv.android.dev:color/m3_ref_palette_neutral_variant100 = 0x7f060128
com.iptv.android.dev:color/m3_sys_color_dark_tertiary_container = 0x7f060184
com.iptv.android.dev:attr/layout_anchorGravity = 0x7f040263
com.iptv.android.dev:attr/itemActiveIndicatorStyle = 0x7f040234
com.iptv.android.dev:drawable/abc_ratingbar_indicator_material = 0x7f08005a
com.iptv.android.dev:id/disjoint = 0x7f0a0091
com.iptv.android.dev:attr/actionModeSelectAllDrawable = 0x7f04001a
com.iptv.android.dev:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f070221
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601b9
com.iptv.android.dev:attr/rangeFillColor = 0x7f040379
com.iptv.android.dev:color/m3_sys_color_dark_surface_container_lowest = 0x7f060180
com.iptv.android.dev:color/m3_sys_color_dark_surface_container_high = 0x7f06017d
com.iptv.android.dev:dimen/m3_comp_outlined_card_icon_size = 0x7f070177
com.iptv.android.dev:string/nav_series = 0x7f10014d
com.iptv.android.dev:color/m3_sys_color_dark_surface_container = 0x7f06017c
com.iptv.android.dev:color/m3_sys_color_dark_surface_bright = 0x7f06017b
com.iptv.android.dev:color/m3_slider_active_track_color = 0x7f06015d
com.iptv.android.dev:drawable/exo_icon_fullscreen_exit = 0x7f0800ab
com.iptv.android.dev:color/m3_sys_color_dark_surface = 0x7f06017a
com.iptv.android.dev:id/accessibility_custom_action_19 = 0x7f0a001b
com.iptv.android.dev:color/m3_sys_color_dark_secondary_container = 0x7f060179
com.iptv.android.dev:color/m3_sys_color_dark_primary = 0x7f060176
com.iptv.android.dev:attr/layout_constraintHorizontal_bias = 0x7f04027b
com.iptv.android.dev:color/m3_sys_color_dark_outline_variant = 0x7f060175
com.iptv.android.dev:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.iptv.android.dev:attr/titleTextColor = 0x7f04047a
com.iptv.android.dev:color/m3_sys_color_dark_outline = 0x7f060174
com.iptv.android.dev:color/m3_sys_color_dark_on_tertiary_container = 0x7f060173
com.iptv.android.dev:id/exo_artwork = 0x7f0a00a8
com.iptv.android.dev:attr/textAppearanceLabelSmall = 0x7f040436
com.iptv.android.dev:color/m3_sys_color_dark_on_tertiary = 0x7f060172
com.iptv.android.dev:style/Base.Theme.AppCompat.Dialog = 0x7f11004d
com.iptv.android.dev:dimen/m3_comp_fab_primary_small_icon_size = 0x7f070144
com.iptv.android.dev:color/m3_sys_color_dark_on_secondary_container = 0x7f06016f
com.iptv.android.dev:color/m3_sys_color_dark_on_secondary = 0x7f06016e
com.iptv.android.dev:color/m3_sys_color_dark_on_primary_container = 0x7f06016d
com.iptv.android.dev:macro/m3_comp_switch_unselected_icon_color = 0x7f0e013b
com.iptv.android.dev:id/customPanel = 0x7f0a007e
com.iptv.android.dev:attr/chipStyle = 0x7f0400c8
com.iptv.android.dev:color/design_default_color_on_background = 0x7f06004a
com.iptv.android.dev:color/m3_sys_color_dark_on_primary = 0x7f06016c
com.iptv.android.dev:style/Widget.AppCompat.ActionBar.TabView = 0x7f110325
com.iptv.android.dev:attr/fontStyle = 0x7f0401fa
com.iptv.android.dev:color/m3_sys_color_dark_on_error_container = 0x7f06016b
com.iptv.android.dev:color/material_personalized_color_on_background = 0x7f060279
com.iptv.android.dev:attr/colorPrimaryDark = 0x7f040107
com.iptv.android.dev:color/m3_sys_color_dark_on_error = 0x7f06016a
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1101f4
com.iptv.android.dev:color/m3_sys_color_dark_on_background = 0x7f060169
com.iptv.android.dev:color/m3_sys_color_dark_inverse_surface = 0x7f060168
com.iptv.android.dev:string/mtrl_switch_thumb_path_morphing = 0x7f10013b
com.iptv.android.dev:attr/checkedIconEnabled = 0x7f0400ae
com.iptv.android.dev:color/m3_sys_color_dark_inverse_primary = 0x7f060167
com.iptv.android.dev:color/m3_sys_color_dark_error = 0x7f060164
com.iptv.android.dev:color/material_dynamic_neutral_variant50 = 0x7f06022e
com.iptv.android.dev:color/m3_sys_color_dark_background = 0x7f060163
com.iptv.android.dev:color/m3_switch_track_tint = 0x7f060162
com.iptv.android.dev:id/coordinator = 0x7f0a0078
com.iptv.android.dev:drawable/abc_text_select_handle_right_mtrl = 0x7f080070
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1101f1
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0601a0
com.iptv.android.dev:color/m3_simple_item_ripple_color = 0x7f06015c
com.iptv.android.dev:attr/insetForeground = 0x7f04022f
com.iptv.android.dev:attr/textAppearanceSmallPopupMenu = 0x7f040440
com.iptv.android.dev:color/m3_ref_palette_tertiary95 = 0x7f060158
com.iptv.android.dev:color/m3_ref_palette_tertiary90 = 0x7f060157
com.iptv.android.dev:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f110063
com.iptv.android.dev:attr/triggerId = 0x7f04049c
com.iptv.android.dev:color/m3_ref_palette_tertiary70 = 0x7f060155
com.iptv.android.dev:color/m3_ref_palette_tertiary40 = 0x7f060152
com.iptv.android.dev:dimen/notification_media_narrow_margin = 0x7f07032e
com.iptv.android.dev:color/m3_ref_palette_tertiary30 = 0x7f060151
com.iptv.android.dev:style/Base.Theme.AppCompat.CompactMenu = 0x7f11004c
com.iptv.android.dev:attr/alertDialogTheme = 0x7f04002d
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f07022c
com.iptv.android.dev:color/material_on_primary_emphasis_medium = 0x7f06026c
com.iptv.android.dev:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0e001f
com.iptv.android.dev:color/m3_ref_palette_tertiary20 = 0x7f060150
com.iptv.android.dev:style/Platform.MaterialComponents.Light.Dialog = 0x7f11016e
com.iptv.android.dev:color/m3_ref_palette_tertiary0 = 0x7f06014d
com.iptv.android.dev:dimen/mtrl_tooltip_arrowSize = 0x7f070321
com.iptv.android.dev:attr/navigationIcon = 0x7f040330
com.iptv.android.dev:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f07029b
com.iptv.android.dev:color/m3_ref_palette_secondary90 = 0x7f06014a
com.iptv.android.dev:color/m3_ref_palette_secondary80 = 0x7f060149
com.iptv.android.dev:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0e00bb
com.iptv.android.dev:color/m3_ref_palette_secondary40 = 0x7f060145
com.iptv.android.dev:color/m3_ref_palette_secondary30 = 0x7f060144
com.iptv.android.dev:attr/actionModeCloseButtonStyle = 0x7f040012
com.iptv.android.dev:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.iptv.android.dev:color/design_dark_default_color_primary_variant = 0x7f060044
com.iptv.android.dev:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080039
com.iptv.android.dev:string/m3_sys_motion_easing_standard_accelerate = 0x7f1000ec
com.iptv.android.dev:attr/passwordToggleTintMode = 0x7f040354
com.iptv.android.dev:color/m3_ref_palette_primary95 = 0x7f06013e
com.iptv.android.dev:string/m3_sys_motion_easing_emphasized = 0x7f1000e3
com.iptv.android.dev:integer/m3_sys_motion_duration_medium3 = 0x7f0b001c
com.iptv.android.dev:color/m3_ref_palette_primary60 = 0x7f06013a
com.iptv.android.dev:color/m3_ref_palette_primary50 = 0x7f060139
com.iptv.android.dev:dimen/design_fab_elevation = 0x7f07006f
com.iptv.android.dev:color/cardview_shadow_start_color = 0x7f060038
com.iptv.android.dev:color/m3_ref_palette_primary30 = 0x7f060137
com.iptv.android.dev:drawable/abc_btn_borderless_material = 0x7f08002a
com.iptv.android.dev:dimen/mtrl_calendar_day_width = 0x7f070297
com.iptv.android.dev:color/m3_sys_color_on_tertiary_fixed = 0x7f0601f3
com.iptv.android.dev:attr/materialCalendarStyle = 0x7f0402d3
com.iptv.android.dev:color/m3_ref_palette_neutral_variant99 = 0x7f060132
com.iptv.android.dev:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000c
com.iptv.android.dev:color/m3_ref_palette_neutral87 = 0x7f06011e
com.iptv.android.dev:color/m3_ref_palette_neutral_variant95 = 0x7f060131
com.iptv.android.dev:id/open_search_view_toolbar = 0x7f0a0170
com.iptv.android.dev:color/m3_ref_palette_neutral_variant90 = 0x7f060130
com.iptv.android.dev:color/m3_switch_thumb_tint = 0x7f060161
com.iptv.android.dev:color/m3_ref_palette_neutral_variant80 = 0x7f06012f
com.iptv.android.dev:dimen/material_emphasis_disabled = 0x7f07024e
com.iptv.android.dev:color/m3_ref_palette_neutral_variant70 = 0x7f06012e
com.iptv.android.dev:string/exo_download_description = 0x7f1000ae
com.iptv.android.dev:color/m3_ref_palette_neutral_variant60 = 0x7f06012d
com.iptv.android.dev:dimen/abc_text_size_body_1_material = 0x7f07003f
com.iptv.android.dev:color/m3_ref_palette_neutral_variant30 = 0x7f06012a
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f11002c
com.iptv.android.dev:attr/boxBackgroundMode = 0x7f040081
com.iptv.android.dev:color/m3_ref_palette_neutral_variant10 = 0x7f060127
com.iptv.android.dev:color/design_default_color_on_surface = 0x7f06004e
com.iptv.android.dev:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080074
com.iptv.android.dev:color/m3_ref_palette_neutral99 = 0x7f060125
com.iptv.android.dev:attr/deriveConstraintsFrom = 0x7f04016a
com.iptv.android.dev:attr/toolbarSurfaceStyle = 0x7f040481
com.iptv.android.dev:attr/dragScale = 0x7f040179
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0e0157
com.iptv.android.dev:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface = 0x7f0601b6
com.iptv.android.dev:attr/selectionRequired = 0x7f04039b
com.iptv.android.dev:color/m3_ref_palette_neutral92 = 0x7f060120
com.iptv.android.dev:integer/m3_btn_anim_delay_ms = 0x7f0b000d
com.iptv.android.dev:attr/tintNavigationIcon = 0x7f04046d
com.iptv.android.dev:color/m3_ref_palette_neutral90 = 0x7f06011f
com.iptv.android.dev:attr/actionBarDivider = 0x7f040001
com.iptv.android.dev:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1100f6
com.iptv.android.dev:string/cd_content_image = 0x7f100047
com.iptv.android.dev:dimen/mtrl_calendar_year_width = 0x7f0702b6
com.iptv.android.dev:attr/tabIndicatorColor = 0x7f040406
com.iptv.android.dev:color/m3_ref_palette_neutral70 = 0x7f06011c
com.iptv.android.dev:color/m3_ref_palette_neutral60 = 0x7f06011b
com.iptv.android.dev:style/Theme.Design.BottomSheetDialog = 0x7f11025a
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0e0166
com.iptv.android.dev:attr/hintAnimationEnabled = 0x7f040214
com.iptv.android.dev:style/Base.Theme.SplashScreen.Light = 0x7f11007a
com.iptv.android.dev:color/m3_ref_palette_neutral12 = 0x7f060111
com.iptv.android.dev:color/m3_ref_palette_error99 = 0x7f06010d
com.iptv.android.dev:color/m3_ref_palette_error95 = 0x7f06010c
com.iptv.android.dev:attr/layout_constraintHeight_default = 0x7f040277
com.iptv.android.dev:color/m3_ref_palette_error20 = 0x7f060104
com.iptv.android.dev:attr/subheaderInsetEnd = 0x7f0403ec
com.iptv.android.dev:drawable/btn_radio_off_mtrl = 0x7f08007d
com.iptv.android.dev:attr/subheaderColor = 0x7f0403eb
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0600f4
com.iptv.android.dev:attr/actionModeWebSearchDrawable = 0x7f04001f
com.iptv.android.dev:color/m3_ref_palette_neutral_variant50 = 0x7f06012c
com.iptv.android.dev:attr/behavior_expandedOffset = 0x7f04006f
com.iptv.android.dev:color/m3_ref_palette_error10 = 0x7f060102
com.iptv.android.dev:styleable/MaterialCalendarItem = 0x7f120052
com.iptv.android.dev:color/m3_ref_palette_neutral17 = 0x7f060112
com.iptv.android.dev:animator/design_appbar_state_list_animator = 0x7f020000
com.iptv.android.dev:color/m3_ref_palette_error0 = 0x7f060101
com.iptv.android.dev:styleable/AnimatedStateListDrawableTransition = 0x7f12000a
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600ca
com.iptv.android.dev:attr/viewInflaterClass = 0x7f0404aa
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0600ff
com.iptv.android.dev:attr/boxCornerRadiusBottomStart = 0x7f040084
com.iptv.android.dev:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701da
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f110308
com.iptv.android.dev:attr/colorTertiary = 0x7f04011d
com.iptv.android.dev:attr/drawableTintMode = 0x7f040183
com.iptv.android.dev:attr/layout_constraintHorizontal_chainStyle = 0x7f04027c
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0600fe
com.iptv.android.dev:anim/abc_slide_out_top = 0x7f010009
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0600f9
com.iptv.android.dev:drawable/exo_styled_controls_subtitle_on = 0x7f0800d5
com.iptv.android.dev:styleable/MaterialShape = 0x7f120058
com.iptv.android.dev:dimen/m3_small_fab_max_image_size = 0x7f070208
com.iptv.android.dev:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f11026d
com.iptv.android.dev:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.iptv.android.dev:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0e0174
com.iptv.android.dev:attr/motionDurationExtraLong3 = 0x7f04030a
com.iptv.android.dev:style/Theme.Design.Light = 0x7f11025b
com.iptv.android.dev:attr/materialCalendarTheme = 0x7f0402d4
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0600f7
com.iptv.android.dev:style/ShapeAppearance.Material3.Corner.Full = 0x7f1101a3
com.iptv.android.dev:layout/select_dialog_item_material = 0x7f0d007c
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0600f6
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0e0063
com.iptv.android.dev:drawable/exo_ic_subtitle_on = 0x7f0800a7
com.iptv.android.dev:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0401d6
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600e7
com.iptv.android.dev:color/mtrl_switch_thumb_tint = 0x7f0602d9
com.iptv.android.dev:style/Widget.Material3.SideSheet = 0x7f1103fe
com.iptv.android.dev:style/IPTVButton.Primary = 0x7f11014d
com.iptv.android.dev:string/content_recently_added = 0x7f10005e
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary95 = 0x7f0600e5
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary60 = 0x7f0600e1
com.iptv.android.dev:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0601f4
com.iptv.android.dev:attr/constraintSet = 0x7f040123
com.iptv.android.dev:attr/boxStrokeWidthFocused = 0x7f04008a
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary30 = 0x7f0600de
com.iptv.android.dev:color/mtrl_navigation_bar_ripple_color = 0x7f0602ce
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1101eb
com.iptv.android.dev:attr/contentPaddingEnd = 0x7f040132
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary20 = 0x7f0600dd
com.iptv.android.dev:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701ec
com.iptv.android.dev:id/text = 0x7f0a01da
com.iptv.android.dev:id/off = 0x7f0a0161
com.iptv.android.dev:attr/lineHeight = 0x7f0402a4
com.iptv.android.dev:drawable/btn_radio_on_mtrl = 0x7f08007f
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600d8
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600d6
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600d5
com.iptv.android.dev:drawable/ic_m3_chip_check = 0x7f0800e2
com.iptv.android.dev:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0e0109
com.iptv.android.dev:color/m3_navigation_rail_ripple_color_selector = 0x7f0600af
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600d3
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600d2
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600ce
com.iptv.android.dev:dimen/mtrl_badge_with_text_size = 0x7f07026e
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600ef
com.iptv.android.dev:dimen/m3_btn_translation_z_hovered = 0x7f070106
com.iptv.android.dev:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f11008a
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600c4
com.iptv.android.dev:macro/m3_comp_badge_large_label_text_type = 0x7f0e0004
com.iptv.android.dev:dimen/mtrl_low_ripple_default_alpha = 0x7f0702da
com.iptv.android.dev:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1100ad
com.iptv.android.dev:string/auth_device_name = 0x7f10002d
com.iptv.android.dev:attr/layout_behavior = 0x7f040264
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600c1
com.iptv.android.dev:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1102c1
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600bd
com.iptv.android.dev:id/match_parent = 0x7f0a0118
com.iptv.android.dev:attr/colorContainer = 0x7f0400e8
com.iptv.android.dev:dimen/mtrl_calendar_day_today_stroke = 0x7f070295
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600bc
com.iptv.android.dev:style/Theme.AppCompat.Light.Dialog = 0x7f110253
com.iptv.android.dev:color/material_personalized_color_secondary_text = 0x7f06028e
com.iptv.android.dev:attr/cardCornerRadius = 0x7f04009e
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600b7
com.iptv.android.dev:string/material_timepicker_pm = 0x7f100102
com.iptv.android.dev:dimen/design_fab_image_size = 0x7f070070
com.iptv.android.dev:dimen/mtrl_navigation_rail_elevation = 0x7f0702ea
com.iptv.android.dev:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700d3
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600e9
com.iptv.android.dev:color/m3_ref_palette_black = 0x7f0600b4
com.iptv.android.dev:string/abc_menu_sym_shortcut_label = 0x7f100010
com.iptv.android.dev:color/material_dynamic_primary30 = 0x7f060239
com.iptv.android.dev:attr/materialAlertDialogTitleTextStyle = 0x7f0402c3
com.iptv.android.dev:color/m3_radiobutton_ripple_tint = 0x7f0600b3
com.iptv.android.dev:attr/collapsedTitleTextColor = 0x7f0400de
com.iptv.android.dev:id/parent_matrix = 0x7f0a0179
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary99 = 0x7f060100
com.iptv.android.dev:color/m3_ref_palette_primary0 = 0x7f060133
com.iptv.android.dev:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0600ae
com.iptv.android.dev:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1100c0
com.iptv.android.dev:attr/autoTransition = 0x7f040047
com.iptv.android.dev:attr/surface_type = 0x7f0403f9
com.iptv.android.dev:style/Widget.AppCompat.ListView.DropDown = 0x7f110354
com.iptv.android.dev:macro/m3_comp_time_picker_container_color = 0x7f0e014f
com.iptv.android.dev:id/wrapped_composition_tag = 0x7f0a020e
com.iptv.android.dev:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0600ad
com.iptv.android.dev:id/search_mag_icon = 0x7f0a01a1
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral12 = 0x7f0600b8
com.iptv.android.dev:color/material_timepicker_clockface = 0x7f0602ae
com.iptv.android.dev:color/bright_foreground_disabled_material_dark = 0x7f060028
com.iptv.android.dev:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.iptv.android.dev:animator/fragment_open_exit = 0x7f020008
com.iptv.android.dev:attr/telltales_tailScale = 0x7f04041e
com.iptv.android.dev:anim/design_bottom_sheet_slide_in = 0x7f010018
com.iptv.android.dev:dimen/m3_datepicker_elevation = 0x7f0701ca
com.iptv.android.dev:color/material_dynamic_tertiary20 = 0x7f060252
com.iptv.android.dev:attr/materialButtonStyle = 0x7f0402c5
com.iptv.android.dev:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1102cd
com.iptv.android.dev:style/IPTVCard = 0x7f11014f
com.iptv.android.dev:color/m3_navigation_bar_ripple_color_selector = 0x7f0600a8
com.iptv.android.dev:color/m3_hint_foreground = 0x7f0600a4
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Body2 = 0x7f110231
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f070223
com.iptv.android.dev:attr/itemVerticalPadding = 0x7f040251
com.iptv.android.dev:color/m3_navigation_item_text_color = 0x7f0600ac
com.iptv.android.dev:color/m3_filled_icon_button_container_color_selector = 0x7f0600a2
com.iptv.android.dev:id/endToStart = 0x7f0a00a2
com.iptv.android.dev:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702dc
com.iptv.android.dev:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f08010a
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0601a1
com.iptv.android.dev:drawable/compat_splash_screen = 0x7f080081
com.iptv.android.dev:color/m3_fab_efab_foreground_color_selector = 0x7f0600a0
com.iptv.android.dev:color/m3_elevated_chip_background_color = 0x7f06009e
com.iptv.android.dev:dimen/mtrl_calendar_landscape_header_width = 0x7f0702a3
com.iptv.android.dev:dimen/abc_action_button_min_width_material = 0x7f07000e
com.iptv.android.dev:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070267
com.iptv.android.dev:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401d2
com.iptv.android.dev:styleable/KeyAttribute = 0x7f12003f
com.iptv.android.dev:color/m3_efab_ripple_color_selector = 0x7f06009d
com.iptv.android.dev:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701db
com.iptv.android.dev:attr/time_bar_min_update_interval = 0x7f04046a
com.iptv.android.dev:color/m3_dynamic_primary_text_disable_only = 0x7f06009c
com.iptv.android.dev:string/hide_bottom_view_on_scroll_behavior = 0x7f1000cc
com.iptv.android.dev:attr/actionBarSplitStyle = 0x7f040005
com.iptv.android.dev:attr/colorOnSurfaceVariant = 0x7f0400fe
com.iptv.android.dev:color/m3_dynamic_hint_foreground = 0x7f06009b
com.iptv.android.dev:color/m3_dynamic_default_color_secondary_text = 0x7f060099
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary95 = 0x7f0600f2
com.iptv.android.dev:color/m3_ref_palette_error70 = 0x7f060109
com.iptv.android.dev:string/search_hint = 0x7f100168
com.iptv.android.dev:id/submenuarrow = 0x7f0a01c9
com.iptv.android.dev:color/m3_dynamic_dark_default_color_primary_text = 0x7f060093
com.iptv.android.dev:string/date_input_invalid_not_allowed = 0x7f100066
com.iptv.android.dev:attr/chainUseRtl = 0x7f0400a6
com.iptv.android.dev:color/m3_dark_hint_foreground = 0x7f06008f
com.iptv.android.dev:color/m3_navigation_item_ripple_color = 0x7f0600ab
com.iptv.android.dev:attr/placeholderText = 0x7f04035d
com.iptv.android.dev:color/m3_button_ripple_color_selector = 0x7f06007f
com.iptv.android.dev:color/m3_dark_default_color_primary_text = 0x7f06008c
com.iptv.android.dev:color/m3_chip_text_color = 0x7f06008b
com.iptv.android.dev:attr/region_heightLessThan = 0x7f04037e
com.iptv.android.dev:color/material_dynamic_primary50 = 0x7f06023b
com.iptv.android.dev:anim/abc_tooltip_exit = 0x7f01000b
com.iptv.android.dev:color/m3_chip_stroke_color = 0x7f06008a
com.iptv.android.dev:style/Widget.Material3.SearchView.Prefix = 0x7f1103fc
com.iptv.android.dev:color/switch_thumb_disabled_material_dark = 0x7f06030a
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f06018c
com.iptv.android.dev:color/gradient_end = 0x7f060072
com.iptv.android.dev:color/m3_dynamic_highlighted_text = 0x7f06009a
com.iptv.android.dev:color/m3_tabs_icon_color = 0x7f0601fb
com.iptv.android.dev:attr/transitionPathRotate = 0x7f04049a
com.iptv.android.dev:macro/m3_comp_search_bar_input_text_color = 0x7f0e00ea
com.iptv.android.dev:color/m3_chip_background_color = 0x7f060088
com.iptv.android.dev:color/m3_chip_assist_text_color = 0x7f060087
com.iptv.android.dev:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f07018c
com.iptv.android.dev:color/m3_checkbox_button_icon_tint = 0x7f060085
com.iptv.android.dev:color/m3_card_foreground_color = 0x7f060082
com.iptv.android.dev:dimen/mtrl_toolbar_default_height = 0x7f070320
com.iptv.android.dev:dimen/m3_btn_disabled_elevation = 0x7f0700f0
com.iptv.android.dev:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f110394
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary10 = 0x7f0600db
com.iptv.android.dev:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0e00a3
com.iptv.android.dev:dimen/exo_settings_icon_size = 0x7f07009e
com.iptv.android.dev:attr/layout_constraintRight_toRightOf = 0x7f040283
com.iptv.android.dev:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702dd
com.iptv.android.dev:attr/drawableRightCompat = 0x7f04017f
com.iptv.android.dev:attr/motionDurationShort3 = 0x7f040316
com.iptv.android.dev:color/m3_appbar_overlay_color = 0x7f060077
com.iptv.android.dev:dimen/m3_simple_item_color_hovered_alpha = 0x7f070204
com.iptv.android.dev:string/settings_logout_subtitle = 0x7f100184
com.iptv.android.dev:color/m3_tabs_text_color = 0x7f0601ff
com.iptv.android.dev:attr/checkboxStyle = 0x7f0400aa
com.iptv.android.dev:color/highlighted_text_material_dark = 0x7f060074
com.iptv.android.dev:macro/m3_comp_filter_chip_container_shape = 0x7f0e0058
com.iptv.android.dev:color/m3_sys_color_dark_surface_container_low = 0x7f06017f
com.iptv.android.dev:attr/hide_during_ads = 0x7f040212
com.iptv.android.dev:color/abc_hint_foreground_material_dark = 0x7f060007
com.iptv.android.dev:color/exo_white_opacity_70 = 0x7f06006d
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110071
com.iptv.android.dev:color/exo_white = 0x7f06006c
com.iptv.android.dev:style/Theme.AppCompat.DayNight.Dialog = 0x7f110247
com.iptv.android.dev:color/error_color_material_light = 0x7f060065
com.iptv.android.dev:color/error_color_material_dark = 0x7f060064
com.iptv.android.dev:integer/mtrl_switch_track_viewport_width = 0x7f0b0040
com.iptv.android.dev:color/dim_foreground_material_dark = 0x7f060061
com.iptv.android.dev:attr/actionDropDownStyle = 0x7f04000d
com.iptv.android.dev:integer/hide_password_duration = 0x7f0b000b
com.iptv.android.dev:color/dim_foreground_disabled_material_dark = 0x7f06005f
com.iptv.android.dev:id/unchecked = 0x7f0a01fb
com.iptv.android.dev:drawable/abc_edit_text_material = 0x7f08003c
com.iptv.android.dev:integer/exo_media_button_opacity_percentage_disabled = 0x7f0b0009
com.iptv.android.dev:dimen/m3_btn_text_btn_padding_right = 0x7f070104
com.iptv.android.dev:string/cd_logo = 0x7f100049
com.iptv.android.dev:color/design_fab_stroke_top_inner_color = 0x7f06005b
com.iptv.android.dev:attr/layout_constraintLeft_toRightOf = 0x7f040280
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f110070
com.iptv.android.dev:string/mtrl_badge_numberless_content_description = 0x7f100105
com.iptv.android.dev:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08002e
com.iptv.android.dev:dimen/m3_searchview_divider_size = 0x7f0701fd
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f110069
com.iptv.android.dev:color/design_fab_stroke_end_inner_color = 0x7f060059
com.iptv.android.dev:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f110187
com.iptv.android.dev:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f110060
com.iptv.android.dev:drawable/mtrl_dropdown_arrow = 0x7f080110
com.iptv.android.dev:color/design_fab_shadow_mid_color = 0x7f060057
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1100e2
com.iptv.android.dev:color/mtrl_btn_text_color_disabled = 0x7f0602b5
com.iptv.android.dev:style/Widget.AppCompat.ListView.Menu = 0x7f110355
com.iptv.android.dev:color/design_error = 0x7f060055
com.iptv.android.dev:dimen/abc_star_medium = 0x7f07003c
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f11044f
com.iptv.android.dev:string/in_progress = 0x7f1000ce
com.iptv.android.dev:color/design_default_color_primary = 0x7f06004f
com.iptv.android.dev:attr/flow_verticalGap = 0x7f0401ee
com.iptv.android.dev:dimen/m3_fab_translation_z_pressed = 0x7f0701d5
com.iptv.android.dev:color/m3_sys_color_light_surface_container_low = 0x7f0601e9
com.iptv.android.dev:layout/design_layout_snackbar = 0x7f0d001f
com.iptv.android.dev:attr/title = 0x7f04046e
com.iptv.android.dev:color/design_dark_default_color_on_secondary = 0x7f060040
com.iptv.android.dev:id/up = 0x7f0a01fe
com.iptv.android.dev:drawable/mtrl_switch_thumb = 0x7f08011c
com.iptv.android.dev:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0601c5
com.iptv.android.dev:color/design_box_stroke_color = 0x7f06003a
com.iptv.android.dev:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.iptv.android.dev:styleable/BottomSheetBehavior_Layout = 0x7f120019
com.iptv.android.dev:string/content_remove_favorite = 0x7f100060
com.iptv.android.dev:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0e0010
com.iptv.android.dev:id/square = 0x7f0a01bb
com.iptv.android.dev:attr/maxAcceleration = 0x7f0402ed
com.iptv.android.dev:color/info = 0x7f060076
com.iptv.android.dev:drawable/avd_hide_password = 0x7f080077
com.iptv.android.dev:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700c8
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0e0155
com.iptv.android.dev:dimen/notification_big_circle_margin = 0x7f070329
com.iptv.android.dev:color/mtrl_btn_ripple_color = 0x7f0602b1
com.iptv.android.dev:string/exo_track_resolution = 0x7f1000b9
com.iptv.android.dev:attr/materialCircleRadius = 0x7f0402da
com.iptv.android.dev:color/cardview_shadow_end_color = 0x7f060037
com.iptv.android.dev:dimen/abc_dialog_padding_top_material = 0x7f070025
com.iptv.android.dev:color/call_notification_decline_color = 0x7f060034
com.iptv.android.dev:layout/material_clockface_textview = 0x7f0d0044
com.iptv.android.dev:id/accessibility_custom_action_7 = 0x7f0a002d
com.iptv.android.dev:color/call_notification_answer_color = 0x7f060033
com.iptv.android.dev:id/text_input_start_icon = 0x7f0a01e3
com.iptv.android.dev:color/button_material_dark = 0x7f060031
com.iptv.android.dev:attr/collapsingToolbarLayoutMediumSize = 0x7f0400e1
com.iptv.android.dev:attr/flow_firstHorizontalStyle = 0x7f0401df
com.iptv.android.dev:attr/telltales_velocityMode = 0x7f04041f
com.iptv.android.dev:color/button_focused_outline = 0x7f06002e
com.iptv.android.dev:color/bright_foreground_material_light = 0x7f06002d
com.iptv.android.dev:color/material_dynamic_primary70 = 0x7f06023d
com.iptv.android.dev:color/brand_secondary = 0x7f060027
com.iptv.android.dev:attr/itemPaddingBottom = 0x7f04023f
com.iptv.android.dev:attr/layout_constraintVertical_bias = 0x7f04028a
com.iptv.android.dev:style/Widget.Material3.Button.IconButton = 0x7f11038c
com.iptv.android.dev:color/brand_primary_dark = 0x7f060025
com.iptv.android.dev:layout/abc_tooltip = 0x7f0d001b
com.iptv.android.dev:drawable/exo_ic_pause_circle_filled = 0x7f08009f
com.iptv.android.dev:attr/windowFixedHeightMajor = 0x7f0404b6
com.iptv.android.dev:dimen/design_fab_translation_z_pressed = 0x7f070074
com.iptv.android.dev:attr/materialCalendarHeaderLayout = 0x7f0402cd
com.iptv.android.dev:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110015
com.iptv.android.dev:color/black = 0x7f060022
com.iptv.android.dev:color/background_floating_material_light = 0x7f06001f
com.iptv.android.dev:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0e0018
com.iptv.android.dev:attr/attributeName = 0x7f04003e
com.iptv.android.dev:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600a6
com.iptv.android.dev:dimen/mtrl_btn_text_btn_padding_left = 0x7f070289
com.iptv.android.dev:attr/indicatorDirectionCircular = 0x7f04022a
com.iptv.android.dev:attr/alertDialogCenterButtons = 0x7f04002b
com.iptv.android.dev:color/material_dynamic_primary99 = 0x7f060241
com.iptv.android.dev:attr/materialButtonOutlinedStyle = 0x7f0402c4
com.iptv.android.dev:color/background = 0x7f06001d
com.iptv.android.dev:string/auth_activation_code = 0x7f10002b
com.iptv.android.dev:color/androidx_core_ripple_material_light = 0x7f06001b
com.iptv.android.dev:styleable/ShapeableImageView = 0x7f120082
com.iptv.android.dev:attr/showPaths = 0x7f0403af
com.iptv.android.dev:color/m3_sys_color_light_surface_container = 0x7f0601e6
com.iptv.android.dev:color/material_slider_active_tick_marks_color = 0x7f0602a5
com.iptv.android.dev:color/abc_tint_spinner = 0x7f060017
com.iptv.android.dev:color/abc_tint_seek_thumb = 0x7f060016
com.iptv.android.dev:id/accessibility_custom_action_16 = 0x7f0a0018
com.iptv.android.dev:dimen/abc_switch_padding = 0x7f07003e
com.iptv.android.dev:color/abc_secondary_text_material_dark = 0x7f060011
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0600fd
com.iptv.android.dev:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1101db
com.iptv.android.dev:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0e016f
com.iptv.android.dev:color/abc_search_url_text_pressed = 0x7f06000f
com.iptv.android.dev:attr/animateNavigationIcon = 0x7f040033
com.iptv.android.dev:layout/exo_list_divider = 0x7f0d002c
com.iptv.android.dev:color/abc_primary_text_material_light = 0x7f06000c
com.iptv.android.dev:color/material_personalized_color_secondary_text_inverse = 0x7f06028f
com.iptv.android.dev:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.iptv.android.dev:color/dim_foreground_material_light = 0x7f060062
com.iptv.android.dev:attr/destination = 0x7f04016b
com.iptv.android.dev:drawable/abc_dialog_material_background = 0x7f08003b
com.iptv.android.dev:color/abc_decor_view_status_guard = 0x7f060005
com.iptv.android.dev:string/auth_success = 0x7f100031
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600ee
com.iptv.android.dev:color/abc_btn_colored_text_material = 0x7f060003
com.iptv.android.dev:attr/compatShadowEnabled = 0x7f040122
com.iptv.android.dev:color/transparent = 0x7f060316
com.iptv.android.dev:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.iptv.android.dev:style/Widget.Material3.Chip.Assist = 0x7f11039f
com.iptv.android.dev:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0e00b6
com.iptv.android.dev:bool/abc_action_bar_embed_tabs = 0x7f050000
com.iptv.android.dev:color/bright_foreground_disabled_material_light = 0x7f060029
com.iptv.android.dev:attr/yearStyle = 0x7f0404c2
com.iptv.android.dev:attr/yearSelectedStyle = 0x7f0404c1
com.iptv.android.dev:dimen/mtrl_btn_stroke_size = 0x7f070287
com.iptv.android.dev:attr/windowMinWidthMinor = 0x7f0404bb
com.iptv.android.dev:attr/windowMinWidthMajor = 0x7f0404ba
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0e0153
com.iptv.android.dev:attr/materialCalendarHeaderTitle = 0x7f0402cf
com.iptv.android.dev:attr/yearTodayStyle = 0x7f0404c3
com.iptv.android.dev:style/Theme.MaterialComponents.CompactMenu = 0x7f110280
com.iptv.android.dev:string/exo_download_removing = 0x7f1000b5
com.iptv.android.dev:attr/shapeAppearanceCornerMedium = 0x7f0403a1
com.iptv.android.dev:attr/badgeGravity = 0x7f040055
com.iptv.android.dev:id/parentPanel = 0x7f0a0177
com.iptv.android.dev:attr/windowFixedHeightMinor = 0x7f0404b7
com.iptv.android.dev:styleable/MaterialRadioButton = 0x7f120057
com.iptv.android.dev:styleable/ForegroundLinearLayout = 0x7f120038
com.iptv.android.dev:attr/buttonBarButtonStyle = 0x7f04008d
com.iptv.android.dev:attr/simpleItemSelectedRippleColor = 0x7f0403c1
com.iptv.android.dev:attr/windowActionModeOverlay = 0x7f0404b5
com.iptv.android.dev:id/tag_window_insets_animation_callback = 0x7f0a01d9
com.iptv.android.dev:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0702b1
com.iptv.android.dev:id/showCustom = 0x7f0a01a9
com.iptv.android.dev:drawable/mtrl_bottomsheet_drag_handle = 0x7f080104
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0e00cc
com.iptv.android.dev:attr/drawableLeftCompat = 0x7f04017e
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600cc
com.iptv.android.dev:attr/counterOverflowTextColor = 0x7f04014b
com.iptv.android.dev:attr/argType = 0x7f04003a
com.iptv.android.dev:attr/waveShape = 0x7f0404b1
com.iptv.android.dev:style/Widget.Material3.NavigationView = 0x7f1103f2
com.iptv.android.dev:attr/keep_content_on_player_reset = 0x7f040252
com.iptv.android.dev:attr/subtitleCentered = 0x7f0403f1
com.iptv.android.dev:color/m3_sys_color_dynamic_light_primary_container = 0x7f0601b3
com.iptv.android.dev:drawable/exo_controls_rewind = 0x7f080092
com.iptv.android.dev:color/background_material_light = 0x7f060021
com.iptv.android.dev:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f07026a
com.iptv.android.dev:attr/waveDecay = 0x7f0404ae
com.iptv.android.dev:string/exo_track_mono = 0x7f1000b8
com.iptv.android.dev:attr/warmth = 0x7f0404ad
com.iptv.android.dev:attr/show_subtitle_button = 0x7f0403b8
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0e0103
com.iptv.android.dev:color/exo_bottom_bar_background = 0x7f060068
com.iptv.android.dev:style/Base.Widget.AppCompat.Button = 0x7f1100d0
com.iptv.android.dev:macro/m3_comp_outlined_card_outline_color = 0x7f0e00b0
com.iptv.android.dev:attr/uri = 0x7f0404a1
com.iptv.android.dev:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f11041b
com.iptv.android.dev:color/material_dynamic_tertiary30 = 0x7f060253
com.iptv.android.dev:string/language_changed = 0x7f1000d1
com.iptv.android.dev:attr/colorSurfaceContainerLow = 0x7f040117
com.iptv.android.dev:attr/unplayed_color = 0x7f0404a0
com.iptv.android.dev:attr/gestureInsetBottomIgnored = 0x7f040202
com.iptv.android.dev:color/material_personalized_color_outline_variant = 0x7f060286
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1102ff
com.iptv.android.dev:attr/behavior_significantVelocityThreshold = 0x7f040076
com.iptv.android.dev:attr/scrimAnimationDuration = 0x7f04038c
com.iptv.android.dev:attr/triggerReceiver = 0x7f04049d
com.iptv.android.dev:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.iptv.android.dev:color/mtrl_scrim_color = 0x7f0602d7
com.iptv.android.dev:dimen/hint_alpha_material_light = 0x7f0700ba
com.iptv.android.dev:attr/transitionFlags = 0x7f040499
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0e00ce
com.iptv.android.dev:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0702ad
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Media = 0x7f1101fb
com.iptv.android.dev:style/Base.Widget.AppCompat.ListMenuView = 0x7f1100e8
com.iptv.android.dev:color/m3_sys_color_secondary_fixed = 0x7f0601f7
com.iptv.android.dev:attr/transitionEasing = 0x7f040498
com.iptv.android.dev:dimen/mtrl_progress_circular_inset_medium = 0x7f0702f2
com.iptv.android.dev:attr/trackThickness = 0x7f040494
com.iptv.android.dev:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1103e5
com.iptv.android.dev:dimen/design_navigation_separator_vertical_padding = 0x7f07007d
com.iptv.android.dev:attr/liftOnScrollTargetViewId = 0x7f0402a2
com.iptv.android.dev:attr/dialogPreferredPadding = 0x7f04016d
com.iptv.android.dev:drawable/$avd_show_password__0 = 0x7f080003
com.iptv.android.dev:attr/trackDecorationTintMode = 0x7f040492
com.iptv.android.dev:dimen/material_clock_display_height = 0x7f07023e
com.iptv.android.dev:attr/trackCornerRadius = 0x7f04048f
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f110284
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f110190
com.iptv.android.dev:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f070326
com.iptv.android.dev:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0e0171
com.iptv.android.dev:attr/track = 0x7f04048b
com.iptv.android.dev:style/Widget.Material3.SearchBar.Outlined = 0x7f1103fa
com.iptv.android.dev:attr/colorSurface = 0x7f040112
com.iptv.android.dev:attr/passwordToggleTint = 0x7f040353
com.iptv.android.dev:attr/tooltipFrameBackground = 0x7f040483
com.iptv.android.dev:attr/trackDecorationTint = 0x7f040491
com.iptv.android.dev:attr/textAppearanceBodySmall = 0x7f040425
com.iptv.android.dev:attr/tooltipForegroundColor = 0x7f040482
com.iptv.android.dev:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f110472
com.iptv.android.dev:id/textinput_placeholder = 0x7f0a01e7
com.iptv.android.dev:attr/goIcon = 0x7f040203
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f11047d
com.iptv.android.dev:animator/fragment_close_enter = 0x7f020003
com.iptv.android.dev:attr/toolbarId = 0x7f04047e
com.iptv.android.dev:attr/titlePositionInterpolator = 0x7f040478
com.iptv.android.dev:id/tag_unhandled_key_event_manager = 0x7f0a01d7
com.iptv.android.dev:attr/pivotAnchor = 0x7f04035c
com.iptv.android.dev:style/TextAppearance.Material3.BodyMedium = 0x7f11021d
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600be
com.iptv.android.dev:drawable/ic_call_decline_low = 0x7f0800dd
com.iptv.android.dev:dimen/abc_control_corner_material = 0x7f070018
com.iptv.android.dev:style/ShapeAppearance.Material3.MediumComponent = 0x7f1101a9
com.iptv.android.dev:attr/titleMarginTop = 0x7f040476
com.iptv.android.dev:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f11015c
com.iptv.android.dev:attr/ratingBarStyle = 0x7f04037a
com.iptv.android.dev:attr/titleMarginStart = 0x7f040475
com.iptv.android.dev:string/exo_controls_cc_enabled_description = 0x7f100094
com.iptv.android.dev:attr/colorSurfaceContainer = 0x7f040114
com.iptv.android.dev:attr/contentInsetRight = 0x7f04012d
com.iptv.android.dev:color/m3_text_button_foreground_color_selector = 0x7f060202
com.iptv.android.dev:attr/behavior_autoHide = 0x7f04006c
com.iptv.android.dev:color/m3_calendar_item_disabled_text = 0x7f060080
com.iptv.android.dev:attr/textInputFilledStyle = 0x7f04044b
com.iptv.android.dev:attr/titleMarginEnd = 0x7f040474
com.iptv.android.dev:color/m3_ref_palette_secondary95 = 0x7f06014b
com.iptv.android.dev:attr/endIconMode = 0x7f04019a
com.iptv.android.dev:attr/titleMargin = 0x7f040472
com.iptv.android.dev:attr/defaultQueryHint = 0x7f040164
com.iptv.android.dev:color/design_default_color_on_secondary = 0x7f06004d
com.iptv.android.dev:attr/titleCollapseMode = 0x7f040470
com.iptv.android.dev:attr/actionOverflowButtonStyle = 0x7f040020
com.iptv.android.dev:attr/tintMode = 0x7f04046c
com.iptv.android.dev:color/m3_sys_color_light_error = 0x7f0601ce
com.iptv.android.dev:attr/fontProviderSystemFontFamily = 0x7f0401f9
com.iptv.android.dev:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f110053
com.iptv.android.dev:attr/snackbarStyle = 0x7f0403c9
com.iptv.android.dev:attr/tickVisible = 0x7f040469
com.iptv.android.dev:dimen/abc_text_size_display_4_material = 0x7f070046
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f110197
com.iptv.android.dev:integer/m3_sys_motion_duration_long1 = 0x7f0b0016
com.iptv.android.dev:attr/textAppearanceHeadline4 = 0x7f04042e
com.iptv.android.dev:string/date_range_picker_day_in_range = 0x7f10007c
com.iptv.android.dev:attr/tickMarkTint = 0x7f040465
com.iptv.android.dev:style/TextAppearance.AppCompat.Display2 = 0x7f1101cc
com.iptv.android.dev:color/m3_ref_palette_error50 = 0x7f060107
com.iptv.android.dev:attr/windowNoTitle = 0x7f0404bc
com.iptv.android.dev:string/cd_menu_button = 0x7f10004a
com.iptv.android.dev:attr/motion_postLayoutCollision = 0x7f04032a
com.iptv.android.dev:attr/tickColor = 0x7f040461
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0e015b
com.iptv.android.dev:dimen/material_bottom_sheet_max_width = 0x7f07023d
com.iptv.android.dev:style/Theme.Material3.Light.DialogWhenLarge = 0x7f11027a
com.iptv.android.dev:attr/deltaPolarRadius = 0x7f040169
com.iptv.android.dev:color/button_focused_secondary = 0x7f060030
com.iptv.android.dev:attr/arcMode = 0x7f040039
com.iptv.android.dev:macro/m3_comp_fab_surface_icon_color = 0x7f0e003f
com.iptv.android.dev:animator/m3_chip_state_list_anim = 0x7f02000e
com.iptv.android.dev:attr/thumbStrokeColor = 0x7f04045c
com.iptv.android.dev:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0b003a
com.iptv.android.dev:drawable/abc_spinner_mtrl_am_alpha = 0x7f080065
com.iptv.android.dev:attr/thumbRadius = 0x7f04045b
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600c3
com.iptv.android.dev:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1100af
com.iptv.android.dev:dimen/material_clock_hand_center_dot_radius = 0x7f070242
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f110199
com.iptv.android.dev:integer/m3_badge_max_number = 0x7f0b000c
com.iptv.android.dev:attr/thumbIconSize = 0x7f040458
com.iptv.android.dev:string/on = 0x7f100152
com.iptv.android.dev:attr/thumbElevation = 0x7f040456
com.iptv.android.dev:color/m3_ref_palette_neutral95 = 0x7f060122
com.iptv.android.dev:id/navigation_bar_item_icon_view = 0x7f0a0155
com.iptv.android.dev:dimen/m3_btn_dialog_btn_min_width = 0x7f0700ee
com.iptv.android.dev:attr/thumbColor = 0x7f040455
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600c2
com.iptv.android.dev:id/accelerate = 0x7f0a000e
com.iptv.android.dev:dimen/abc_list_item_height_small_material = 0x7f070032
com.iptv.android.dev:macro/m3_comp_date_picker_modal_container_color = 0x7f0e000e
com.iptv.android.dev:attr/textInputStyle = 0x7f040450
com.iptv.android.dev:attr/textInputOutlinedStyle = 0x7f04044f
com.iptv.android.dev:attr/textInputLayoutFocusedRectEnabled = 0x7f04044c
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600d4
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e00d2
com.iptv.android.dev:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f070141
com.iptv.android.dev:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f11041a
com.iptv.android.dev:animator/m3_card_state_list_anim = 0x7f02000d
com.iptv.android.dev:attr/textEndPadding = 0x7f040448
com.iptv.android.dev:color/surface = 0x7f060307
com.iptv.android.dev:drawable/$m3_avd_show_password__1 = 0x7f08000a
com.iptv.android.dev:attr/textAppearanceTitleMedium = 0x7f040444
com.iptv.android.dev:attr/itemTextColor = 0x7f040250
com.iptv.android.dev:attr/textAppearanceTitleLarge = 0x7f040443
com.iptv.android.dev:layout/abc_action_bar_up_container = 0x7f0d0001
com.iptv.android.dev:attr/textAppearanceSubtitle2 = 0x7f040442
com.iptv.android.dev:dimen/m3_sys_elevation_level5 = 0x7f070211
com.iptv.android.dev:dimen/exo_settings_text_height = 0x7f0700a2
com.iptv.android.dev:attr/activityChooserViewStyle = 0x7f040026
com.iptv.android.dev:attr/textAppearanceSearchResultTitle = 0x7f04043f
com.iptv.android.dev:attr/liftOnScroll = 0x7f0402a0
com.iptv.android.dev:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f110266
com.iptv.android.dev:attr/textAppearanceSearchResultSubtitle = 0x7f04043e
com.iptv.android.dev:layout/mtrl_calendar_day_of_week = 0x7f0d0055
com.iptv.android.dev:attr/textAppearanceListItemSecondary = 0x7f04043a
com.iptv.android.dev:styleable/FontFamilyFont = 0x7f120037
com.iptv.android.dev:attr/snackbarButtonStyle = 0x7f0403c8
com.iptv.android.dev:attr/badgeStyle = 0x7f04005a
com.iptv.android.dev:color/material_personalized_color_text_hint_foreground_inverse = 0x7f06029c
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f110467
com.iptv.android.dev:attr/selectorSize = 0x7f04039c
com.iptv.android.dev:style/Base.Widget.Material3.CollapsingToolbar = 0x7f110105
com.iptv.android.dev:id/view_tree_lifecycle_owner = 0x7f0a0202
com.iptv.android.dev:id/src_in = 0x7f0a01bd
com.iptv.android.dev:attr/textAppearanceBody2 = 0x7f040422
com.iptv.android.dev:style/Theme.Material3.Light.SideSheetDialog = 0x7f11027c
com.iptv.android.dev:color/design_icon_tint = 0x7f06005d
com.iptv.android.dev:attr/nestedScrollable = 0x7f040337
com.iptv.android.dev:dimen/mtrl_fab_elevation = 0x7f0702d2
com.iptv.android.dev:attr/textAppearanceLabelLarge = 0x7f040434
com.iptv.android.dev:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702df
com.iptv.android.dev:attr/dialogCornerRadius = 0x7f04016c
com.iptv.android.dev:attr/textAppearanceHeadlineSmall = 0x7f040433
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f1102a5
com.iptv.android.dev:string/fab_transformation_scrim_behavior = 0x7f1000ca
com.iptv.android.dev:attr/drawableBottomCompat = 0x7f04017c
com.iptv.android.dev:attr/chipIcon = 0x7f0400ba
com.iptv.android.dev:attr/textAppearanceHeadlineMedium = 0x7f040432
com.iptv.android.dev:color/mtrl_chip_background_color = 0x7f0602bc
com.iptv.android.dev:attr/textAppearanceHeadline6 = 0x7f040430
com.iptv.android.dev:styleable/Snackbar = 0x7f120085
com.iptv.android.dev:styleable/OnClick = 0x7f120072
com.iptv.android.dev:attr/textAppearanceHeadline3 = 0x7f04042d
com.iptv.android.dev:attr/textAppearanceDisplayMedium = 0x7f040429
com.iptv.android.dev:attr/layout_anchor = 0x7f040262
com.iptv.android.dev:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.iptv.android.dev:id/consume_window_insets_tag = 0x7f0a0073
com.iptv.android.dev:dimen/design_tab_text_size_2line = 0x7f07008c
com.iptv.android.dev:attr/textAppearanceButton = 0x7f040426
com.iptv.android.dev:attr/mock_showLabel = 0x7f040306
com.iptv.android.dev:attr/textAppearanceBodyMedium = 0x7f040424
com.iptv.android.dev:drawable/exo_rounded_rectangle = 0x7f0800c0
com.iptv.android.dev:dimen/exo_small_icon_width = 0x7f0700a7
com.iptv.android.dev:attr/textAppearanceBodyLarge = 0x7f040423
com.iptv.android.dev:attr/layout_insetEdge = 0x7f04029a
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f110210
com.iptv.android.dev:attr/textAppearanceBody1 = 0x7f040421
com.iptv.android.dev:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0e0143
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary100 = 0x7f0600dc
com.iptv.android.dev:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f11007c
com.iptv.android.dev:dimen/design_bottom_navigation_margin = 0x7f070068
com.iptv.android.dev:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f11041c
com.iptv.android.dev:id/channel_name = 0x7f0a0065
com.iptv.android.dev:attr/appBarLayoutStyle = 0x7f040037
com.iptv.android.dev:attr/targetPackage = 0x7f04041c
com.iptv.android.dev:attr/tabTextAppearance = 0x7f040418
com.iptv.android.dev:dimen/mtrl_calendar_day_height = 0x7f070293
com.iptv.android.dev:string/search_suggestions = 0x7f100171
com.iptv.android.dev:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0e011a
com.iptv.android.dev:id/TOP_END = 0x7f0a000c
com.iptv.android.dev:dimen/m3_slider_inactive_track_height = 0x7f070206
com.iptv.android.dev:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110014
com.iptv.android.dev:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0e013d
com.iptv.android.dev:attr/tabSecondaryStyle = 0x7f040414
com.iptv.android.dev:attr/tabRippleColor = 0x7f040413
com.iptv.android.dev:attr/tabPaddingStart = 0x7f040411
com.iptv.android.dev:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f11040d
com.iptv.android.dev:color/exo_black_opacity_60 = 0x7f060066
com.iptv.android.dev:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f110271
com.iptv.android.dev:attr/layout_constrainedWidth = 0x7f040268
com.iptv.android.dev:attr/tabPadding = 0x7f04040e
com.iptv.android.dev:attr/tabMinWidth = 0x7f04040c
com.iptv.android.dev:attr/minSeparation = 0x7f0402fe
com.iptv.android.dev:dimen/exo_styled_bottom_bar_time_padding = 0x7f0700aa
com.iptv.android.dev:attr/tabMaxWidth = 0x7f04040b
com.iptv.android.dev:attr/actionBarTabTextStyle = 0x7f040009
com.iptv.android.dev:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f11044e
com.iptv.android.dev:style/Base.v21.Theme.SplashScreen = 0x7f110122
com.iptv.android.dev:attr/onTouchUp = 0x7f040341
com.iptv.android.dev:attr/materialCalendarYearNavigationButton = 0x7f0402d5
com.iptv.android.dev:dimen/abc_text_size_body_2_material = 0x7f070040
com.iptv.android.dev:color/cardview_dark_background = 0x7f060035
com.iptv.android.dev:attr/tabIndicatorAnimationMode = 0x7f040405
com.iptv.android.dev:attr/tabIndicator = 0x7f040403
com.iptv.android.dev:attr/drawerLayoutStyle = 0x7f040187
com.iptv.android.dev:color/m3_default_color_primary_text = 0x7f060091
com.iptv.android.dev:style/ThemeOverlay.Material3.ActionBar = 0x7f1102ba
com.iptv.android.dev:attr/dialogTheme = 0x7f04016e
com.iptv.android.dev:attr/tabIconTintMode = 0x7f040402
com.iptv.android.dev:attr/tabGravity = 0x7f040400
com.iptv.android.dev:attr/extendStrategy = 0x7f0401b9
com.iptv.android.dev:color/m3_checkbox_button_tint = 0x7f060086
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600e8
com.iptv.android.dev:layout/exo_styled_player_control_rewind_button = 0x7f0d0030
com.iptv.android.dev:attr/autoSizeTextType = 0x7f040046
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600b9
com.iptv.android.dev:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f110296
com.iptv.android.dev:attr/actionBarTheme = 0x7f04000a
com.iptv.android.dev:attr/cardForegroundColor = 0x7f0400a0
com.iptv.android.dev:attr/suffixTextAppearance = 0x7f0403f6
com.iptv.android.dev:attr/subtitleTextAppearance = 0x7f0403f2
com.iptv.android.dev:string/exo_item_list = 0x7f1000b6
com.iptv.android.dev:id/item_touch_helper_previous_elevation = 0x7f0a0108
com.iptv.android.dev:attr/motionDurationLong3 = 0x7f04030e
com.iptv.android.dev:attr/subtitle = 0x7f0403f0
com.iptv.android.dev:color/design_fab_shadow_end_color = 0x7f060056
com.iptv.android.dev:color/m3_ref_palette_neutral24 = 0x7f060115
com.iptv.android.dev:attr/errorIconTintMode = 0x7f0401a8
com.iptv.android.dev:attr/visibilityMode = 0x7f0404ab
com.iptv.android.dev:attr/layout_goneMarginTop = 0x7f040299
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0e015e
com.iptv.android.dev:id/exo_overflow_hide = 0x7f0a00bf
com.iptv.android.dev:attr/listItemLayout = 0x7f0402ab
com.iptv.android.dev:attr/tabMode = 0x7f04040d
com.iptv.android.dev:attr/submitBackground = 0x7f0403ef
com.iptv.android.dev:attr/subheaderTextAppearance = 0x7f0403ee
com.iptv.android.dev:dimen/m3_chip_hovered_translation_z = 0x7f07011b
com.iptv.android.dev:style/Theme.Material3.Light = 0x7f110275
com.iptv.android.dev:id/pathRelative = 0x7f0a017c
com.iptv.android.dev:attr/flow_verticalBias = 0x7f0401ed
com.iptv.android.dev:attr/strokeWidth = 0x7f0403e9
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f110463
com.iptv.android.dev:attr/iconEndPadding = 0x7f04021e
com.iptv.android.dev:dimen/mtrl_alert_dialog_background_inset_top = 0x7f070265
com.iptv.android.dev:drawable/btn_checkbox_unchecked_mtrl = 0x7f08007b
com.iptv.android.dev:color/m3_ref_palette_neutral_variant20 = 0x7f060129
com.iptv.android.dev:styleable/MotionHelper = 0x7f120063
com.iptv.android.dev:layout/notification_template_custom_big = 0x7f0d0075
com.iptv.android.dev:attr/strokeColor = 0x7f0403e8
com.iptv.android.dev:string/m3_sys_motion_easing_legacy_accelerate = 0x7f1000e8
com.iptv.android.dev:integer/mtrl_view_visible = 0x7f0b0044
com.iptv.android.dev:attr/flow_lastVerticalStyle = 0x7f0401e9
com.iptv.android.dev:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f1101a2
com.iptv.android.dev:attr/statusBarScrim = 0x7f0403e7
com.iptv.android.dev:attr/statusBarForeground = 0x7f0403e6
com.iptv.android.dev:color/design_dark_default_color_on_error = 0x7f06003e
com.iptv.android.dev:color/material_blue_grey_900 = 0x7f060215
com.iptv.android.dev:attr/statusBarBackground = 0x7f0403e5
com.iptv.android.dev:dimen/exo_styled_progress_margin_bottom = 0x7f0700b1
com.iptv.android.dev:attr/marginRightSystemWindowInsets = 0x7f0402bc
com.iptv.android.dev:string/content_popular_movies = 0x7f10005d
com.iptv.android.dev:color/material_dynamic_tertiary70 = 0x7f060257
com.iptv.android.dev:attr/state_with_icon = 0x7f0403e4
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f070138
com.iptv.android.dev:attr/shapeAppearanceCornerExtraSmall = 0x7f04039f
com.iptv.android.dev:attr/windowFixedWidthMinor = 0x7f0404b9
com.iptv.android.dev:style/ExoMediaButton.FastForward = 0x7f11012b
com.iptv.android.dev:attr/expandedTitleMargin = 0x7f0401b1
com.iptv.android.dev:drawable/abc_cab_background_internal_bg = 0x7f080037
com.iptv.android.dev:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1103f8
com.iptv.android.dev:style/ExoStyledControls.TimeBar = 0x7f110145
com.iptv.android.dev:animator/m3_btn_state_list_anim = 0x7f02000b
com.iptv.android.dev:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f110381
com.iptv.android.dev:attr/state_lifted = 0x7f0403e3
com.iptv.android.dev:attr/state_liftable = 0x7f0403e2
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.RepeatToggle = 0x7f11013b
com.iptv.android.dev:attr/motionEasingStandardDecelerateInterpolator = 0x7f040322
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f11040f
com.iptv.android.dev:attr/state_error = 0x7f0403e0
com.iptv.android.dev:attr/state_dragged = 0x7f0403df
com.iptv.android.dev:color/mtrl_text_btn_text_color_selector = 0x7f0602e1
com.iptv.android.dev:color/design_default_color_surface = 0x7f060054
com.iptv.android.dev:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0702a7
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1103d5
com.iptv.android.dev:attr/textAppearanceCaption = 0x7f040427
com.iptv.android.dev:attr/layout_goneMarginBottom = 0x7f040294
com.iptv.android.dev:string/exo_controls_repeat_off_description = 0x7f1000a2
com.iptv.android.dev:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f070273
com.iptv.android.dev:id/exo_center_controls = 0x7f0a00ad
com.iptv.android.dev:color/m3_fab_ripple_color_selector = 0x7f0600a1
com.iptv.android.dev:attr/staggered = 0x7f0403d3
com.iptv.android.dev:attr/srcCompat = 0x7f0403d1
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600cd
com.iptv.android.dev:attr/spinnerStyle = 0x7f0403ce
com.iptv.android.dev:attr/singleSelection = 0x7f0403c5
com.iptv.android.dev:color/gradient_start = 0x7f060073
com.iptv.android.dev:attr/singleLine = 0x7f0403c4
com.iptv.android.dev:attr/simpleItemSelectedColor = 0x7f0403c0
com.iptv.android.dev:dimen/material_input_text_to_prefix_suffix_padding = 0x7f07025b
com.iptv.android.dev:attr/sideSheetModalStyle = 0x7f0403be
com.iptv.android.dev:drawable/ic_call_decline = 0x7f0800dc
com.iptv.android.dev:styleable/MaterialCalendar = 0x7f120051
com.iptv.android.dev:string/date_picker_headline = 0x7f10006b
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f060193
com.iptv.android.dev:attr/sideSheetDialogTheme = 0x7f0403bd
com.iptv.android.dev:styleable/NavAction = 0x7f120067
com.iptv.android.dev:dimen/exo_media_button_height = 0x7f07009a
com.iptv.android.dev:attr/editTextBackground = 0x7f04018d
com.iptv.android.dev:attr/shrinkMotionSpec = 0x7f0403bb
com.iptv.android.dev:string/call_notification_incoming_text = 0x7f100042
com.iptv.android.dev:dimen/m3_snackbar_action_text_color_alpha = 0x7f07020a
com.iptv.android.dev:style/Widget.Material3.BottomNavigationView = 0x7f110383
com.iptv.android.dev:style/Base.V7.Widget.AppCompat.EditText = 0x7f1100c3
com.iptv.android.dev:dimen/m3_comp_fab_primary_large_icon_size = 0x7f070140
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1101ed
com.iptv.android.dev:string/date_picker_headline_description = 0x7f10006c
com.iptv.android.dev:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401d0
com.iptv.android.dev:attr/show_previous_button = 0x7f0403b5
com.iptv.android.dev:attr/showTitle = 0x7f0403b1
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_primary = 0x7f060194
com.iptv.android.dev:attr/showText = 0x7f0403b0
com.iptv.android.dev:style/Base.Widget.Design.TabLayout = 0x7f1100ff
com.iptv.android.dev:attr/toolbarStyle = 0x7f040480
com.iptv.android.dev:attr/materialIconButtonStyle = 0x7f0402e2
com.iptv.android.dev:dimen/m3_comp_filter_chip_container_height = 0x7f07014f
com.iptv.android.dev:string/cd_search_button = 0x7f10004d
com.iptv.android.dev:attr/showMotionSpec = 0x7f0403ae
com.iptv.android.dev:drawable/abc_list_selector_background_transition_holo_light = 0x7f080053
com.iptv.android.dev:attr/showDividers = 0x7f0403ad
com.iptv.android.dev:dimen/material_cursor_width = 0x7f07024c
com.iptv.android.dev:color/m3_dynamic_dark_hint_foreground = 0x7f060096
com.iptv.android.dev:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f110249
com.iptv.android.dev:attr/state_collapsed = 0x7f0403dd
com.iptv.android.dev:color/exo_black_opacity_70 = 0x7f060067
com.iptv.android.dev:dimen/tooltip_precise_anchor_threshold = 0x7f070341
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f11018d
com.iptv.android.dev:bool/mtrl_btn_textappearance_all_caps = 0x7f050002
com.iptv.android.dev:attr/titleTextStyle = 0x7f04047c
com.iptv.android.dev:attr/trackColorInactive = 0x7f04048e
com.iptv.android.dev:attr/shapeAppearanceCornerLarge = 0x7f0403a0
com.iptv.android.dev:id/counterclockwise = 0x7f0a007a
com.iptv.android.dev:color/exo_edit_mode_background_color = 0x7f060069
com.iptv.android.dev:attr/startIconContentDescription = 0x7f0403d6
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_outline = 0x7f060192
com.iptv.android.dev:id/topPanel = 0x7f0a01f1
com.iptv.android.dev:attr/listPreferredItemHeightLarge = 0x7f0402b0
com.iptv.android.dev:string/mtrl_checkbox_button_icon_path_group_name = 0x7f100107
com.iptv.android.dev:attr/selectableItemBackgroundBorderless = 0x7f04039a
com.iptv.android.dev:drawable/m3_tabs_background = 0x7f0800f6
com.iptv.android.dev:styleable/CircularProgressIndicator = 0x7f120020
com.iptv.android.dev:string/time_picker_hour_text_field = 0x7f1001a2
com.iptv.android.dev:attr/materialIconButtonOutlinedStyle = 0x7f0402e1
com.iptv.android.dev:style/Theme.AppCompat.DayNight = 0x7f110245
com.iptv.android.dev:style/TextAppearance.Design.Placeholder = 0x7f110206
com.iptv.android.dev:attr/seekBarStyle = 0x7f040398
com.iptv.android.dev:attr/counterTextColor = 0x7f04014d
com.iptv.android.dev:attr/windowSplashScreenAnimationDuration = 0x7f0404be
com.iptv.android.dev:dimen/m3_menu_elevation = 0x7f0701d9
com.iptv.android.dev:dimen/material_helper_text_default_padding_top = 0x7f070258
com.iptv.android.dev:attr/colorSecondaryVariant = 0x7f040111
com.iptv.android.dev:dimen/mtrl_calendar_days_of_week_height = 0x7f070298
com.iptv.android.dev:color/m3_ref_palette_neutral20 = 0x7f060113
com.iptv.android.dev:dimen/mtrl_calendar_header_height = 0x7f07029d
com.iptv.android.dev:attr/searchViewStyle = 0x7f040397
com.iptv.android.dev:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401bd
com.iptv.android.dev:attr/fontProviderFetchStrategy = 0x7f0401f5
com.iptv.android.dev:attr/searchPrefixText = 0x7f040396
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary0 = 0x7f0600da
com.iptv.android.dev:attr/scrubber_disabled_size = 0x7f040390
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f11031a
com.iptv.android.dev:color/design_dark_default_color_on_background = 0x7f06003d
com.iptv.android.dev:anim/abc_popup_enter = 0x7f010003
com.iptv.android.dev:dimen/mtrl_snackbar_padding_horizontal = 0x7f070310
com.iptv.android.dev:attr/checkedIconMargin = 0x7f0400b0
com.iptv.android.dev:attr/alertDialogButtonGroupStyle = 0x7f04002a
com.iptv.android.dev:attr/tabIndicatorGravity = 0x7f040408
com.iptv.android.dev:integer/m3_sys_shape_corner_full_corner_family = 0x7f0b0025
com.iptv.android.dev:attr/toggleCheckedStateOnClick = 0x7f04047d
com.iptv.android.dev:id/open_search_view_clear_button = 0x7f0a0166
com.iptv.android.dev:anim/m3_motion_fade_enter = 0x7f010023
com.iptv.android.dev:attr/route = 0x7f04038a
com.iptv.android.dev:color/m3_slider_halo_color = 0x7f06015e
com.iptv.android.dev:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0e00dd
com.iptv.android.dev:attr/homeAsUpIndicator = 0x7f040218
com.iptv.android.dev:attr/resize_mode = 0x7f040384
com.iptv.android.dev:attr/removeEmbeddedFabElevation = 0x7f040382
com.iptv.android.dev:attr/postSplashScreenTheme = 0x7f04036d
com.iptv.android.dev:attr/badgeShapeAppearanceOverlay = 0x7f040059
com.iptv.android.dev:attr/textAppearanceLargePopupMenu = 0x7f040437
com.iptv.android.dev:attr/region_widthLessThan = 0x7f040380
com.iptv.android.dev:id/tag_transition_group = 0x7f0a01d6
com.iptv.android.dev:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f070199
com.iptv.android.dev:layout/mtrl_search_bar = 0x7f0d006b
com.iptv.android.dev:color/material_dynamic_secondary30 = 0x7f060246
com.iptv.android.dev:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0e00a9
com.iptv.android.dev:drawable/compat_splash_screen_no_icon_background = 0x7f080082
com.iptv.android.dev:color/m3_sys_color_dark_secondary = 0x7f060178
com.iptv.android.dev:id/tag_accessibility_heading = 0x7f0a01cf
com.iptv.android.dev:attr/cardMaxElevation = 0x7f0400a1
com.iptv.android.dev:attr/recyclerViewStyle = 0x7f04037d
com.iptv.android.dev:attr/textInputFilledDenseStyle = 0x7f040449
com.iptv.android.dev:attr/helperTextEnabled = 0x7f04020a
com.iptv.android.dev:layout/material_clock_period_toggle = 0x7f0d0042
com.iptv.android.dev:color/abc_search_url_text_selected = 0x7f060010
com.iptv.android.dev:attr/ratingBarStyleSmall = 0x7f04037c
com.iptv.android.dev:id/view_tree_view_model_store_owner = 0x7f0a0205
com.iptv.android.dev:attr/ratingBarStyleIndicator = 0x7f04037b
com.iptv.android.dev:string/auth_device_name_hint = 0x7f10002e
com.iptv.android.dev:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f04044e
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f110317
com.iptv.android.dev:attr/fastScrollHorizontalTrackDrawable = 0x7f0401cb
com.iptv.android.dev:attr/pressedTranslationZ = 0x7f040372
com.iptv.android.dev:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080033
com.iptv.android.dev:style/Widget.AppCompat.RatingBar.Indicator = 0x7f11035c
com.iptv.android.dev:dimen/m3_chip_elevated_elevation = 0x7f07011a
com.iptv.android.dev:attr/materialButtonToggleGroupStyle = 0x7f0402c6
com.iptv.android.dev:color/m3_navigation_item_icon_tint = 0x7f0600aa
com.iptv.android.dev:drawable/abc_btn_default_mtrl_shape = 0x7f080030
com.iptv.android.dev:attr/materialSearchViewToolbarHeight = 0x7f0402e6
com.iptv.android.dev:styleable/MotionLayout = 0x7f120064
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f110318
com.iptv.android.dev:integer/mtrl_card_anim_delay_ms = 0x7f0b0036
com.iptv.android.dev:attr/titleCentered = 0x7f04046f
com.iptv.android.dev:dimen/notification_large_icon_width = 0x7f07032c
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary50 = 0x7f0600e0
com.iptv.android.dev:color/m3_dynamic_default_color_primary_text = 0x7f060098
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600ed
com.iptv.android.dev:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1101f8
com.iptv.android.dev:attr/prefixText = 0x7f04036e
com.iptv.android.dev:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.iptv.android.dev:color/error = 0x7f060063
com.iptv.android.dev:style/TextAppearance.Design.Tab = 0x7f11020a
com.iptv.android.dev:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0b0024
com.iptv.android.dev:attr/logoDescription = 0x7f0402b8
com.iptv.android.dev:color/m3_sys_color_light_inverse_surface = 0x7f0601d2
com.iptv.android.dev:id/exo_check = 0x7f0a00ae
com.iptv.android.dev:dimen/m3_bottom_nav_item_padding_top = 0x7f0700e3
com.iptv.android.dev:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f110302
com.iptv.android.dev:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0e0068
com.iptv.android.dev:layout/exo_styled_sub_settings_list_item = 0x7f0d0035
com.iptv.android.dev:attr/popupTheme = 0x7f04036b
com.iptv.android.dev:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700e9
com.iptv.android.dev:color/m3_primary_text_disable_only = 0x7f0600b1
com.iptv.android.dev:id/accessibility_custom_action_20 = 0x7f0a001d
com.iptv.android.dev:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f11006c
com.iptv.android.dev:attr/popupMenuStyle = 0x7f04036a
com.iptv.android.dev:color/material_slider_thumb_color = 0x7f0602aa
com.iptv.android.dev:attr/popUpTo = 0x7f040366
com.iptv.android.dev:dimen/m3_badge_with_text_offset = 0x7f0700db
com.iptv.android.dev:attr/backgroundSplit = 0x7f040051
com.iptv.android.dev:color/material_personalized_color_primary_text_inverse = 0x7f06028b
com.iptv.android.dev:attr/nestedScrollViewStyle = 0x7f040336
com.iptv.android.dev:attr/popEnterAnim = 0x7f040364
com.iptv.android.dev:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070167
com.iptv.android.dev:attr/tabPaddingEnd = 0x7f040410
com.iptv.android.dev:attr/layout_constraintLeft_creator = 0x7f04027e
com.iptv.android.dev:attr/played_ad_marker_color = 0x7f040361
com.iptv.android.dev:attr/roundPercent = 0x7f040389
com.iptv.android.dev:attr/placeholder_emptyVisibility = 0x7f040360
com.iptv.android.dev:attr/placeholderTextColor = 0x7f04035f
com.iptv.android.dev:attr/placeholderTextAppearance = 0x7f04035e
com.iptv.android.dev:id/exo_track_selection_view = 0x7f0a00d6
com.iptv.android.dev:attr/drawableTint = 0x7f040182
com.iptv.android.dev:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0e00d9
com.iptv.android.dev:attr/iconGravity = 0x7f04021f
com.iptv.android.dev:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0701c2
com.iptv.android.dev:attr/perpendicularPath_percent = 0x7f04035b
com.iptv.android.dev:dimen/m3_btn_inset = 0x7f0700fa
com.iptv.android.dev:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0702a5
com.iptv.android.dev:id/snap = 0x7f0a01b1
com.iptv.android.dev:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.iptv.android.dev:color/m3_ref_palette_neutral50 = 0x7f060119
com.iptv.android.dev:integer/material_motion_duration_medium_1 = 0x7f0b002b
com.iptv.android.dev:attr/percentY = 0x7f04035a
com.iptv.android.dev:attr/maxImageSize = 0x7f0402f2
com.iptv.android.dev:color/m3_ref_palette_neutral100 = 0x7f060110
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f06018d
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f060197
com.iptv.android.dev:attr/minHideDelay = 0x7f0402fd
com.iptv.android.dev:attr/textAppearanceListItemSmall = 0x7f04043b
com.iptv.android.dev:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.iptv.android.dev:string/date_range_picker_title = 0x7f100081
com.iptv.android.dev:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701c8
com.iptv.android.dev:attr/percentX = 0x7f040359
com.iptv.android.dev:attr/tabIndicatorAnimationDuration = 0x7f040404
com.iptv.android.dev:integer/design_tab_indicator_anim_duration_ms = 0x7f0b0008
com.iptv.android.dev:attr/percentHeight = 0x7f040357
com.iptv.android.dev:color/m3_ref_palette_primary40 = 0x7f060138
com.iptv.android.dev:attr/checkMarkCompat = 0x7f0400a7
com.iptv.android.dev:attr/pathMotionArc = 0x7f040355
com.iptv.android.dev:dimen/abc_button_padding_vertical_material = 0x7f070015
com.iptv.android.dev:color/material_grey_900 = 0x7f060262
com.iptv.android.dev:color/focus_border = 0x7f06006f
com.iptv.android.dev:attr/homeLayout = 0x7f040219
com.iptv.android.dev:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.iptv.android.dev:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002
com.iptv.android.dev:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600bf
com.iptv.android.dev:attr/suggestionRowLayout = 0x7f0403f8
com.iptv.android.dev:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f110425
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f110289
com.iptv.android.dev:attr/passwordToggleEnabled = 0x7f040352
com.iptv.android.dev:attr/passwordToggleDrawable = 0x7f040351
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1101ea
com.iptv.android.dev:attr/passwordToggleContentDescription = 0x7f040350
com.iptv.android.dev:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1100ce
com.iptv.android.dev:attr/textAppearanceListItem = 0x7f040439
com.iptv.android.dev:attr/textAppearanceHeadline2 = 0x7f04042c
com.iptv.android.dev:color/m3_sys_color_dark_error_container = 0x7f060165
com.iptv.android.dev:id/exo_progress_placeholder = 0x7f0a00c9
com.iptv.android.dev:attr/panelMenuListTheme = 0x7f04034e
com.iptv.android.dev:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0e00c6
com.iptv.android.dev:attr/panelBackground = 0x7f04034d
com.iptv.android.dev:drawable/exo_icon_next = 0x7f0800ac
com.iptv.android.dev:attr/liftOnScrollColor = 0x7f0402a1
com.iptv.android.dev:attr/layout_constraintGuide_begin = 0x7f040274
com.iptv.android.dev:attr/paddingRightSystemWindowInsets = 0x7f040348
com.iptv.android.dev:anim/design_snackbar_in = 0x7f01001a
com.iptv.android.dev:attr/spinnerDropDownItemStyle = 0x7f0403cd
com.iptv.android.dev:attr/paddingLeftSystemWindowInsets = 0x7f040347
com.iptv.android.dev:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0e0127
com.iptv.android.dev:attr/paddingEnd = 0x7f040346
com.iptv.android.dev:attr/layout_constraintBottom_toTopOf = 0x7f04026d
com.iptv.android.dev:attr/paddingBottomSystemWindowInsets = 0x7f040345
com.iptv.android.dev:color/design_bottom_navigation_shadow_color = 0x7f060039
com.iptv.android.dev:attr/waveOffset = 0x7f0404af
com.iptv.android.dev:attr/paddingBottomNoButtons = 0x7f040344
com.iptv.android.dev:anim/abc_slide_out_bottom = 0x7f010008
com.iptv.android.dev:dimen/compat_button_padding_vertical_material = 0x7f070059
com.iptv.android.dev:id/zoom = 0x7f0a020f
com.iptv.android.dev:attr/repeat_toggle_modes = 0x7f040383
com.iptv.android.dev:color/material_dynamic_neutral20 = 0x7f06021e
com.iptv.android.dev:layout/mtrl_calendar_month_navigation = 0x7f0d005a
com.iptv.android.dev:attr/overlay = 0x7f040343
com.iptv.android.dev:attr/buttonStyle = 0x7f040099
com.iptv.android.dev:attr/onNegativeCross = 0x7f04033e
com.iptv.android.dev:style/TextAppearance.AppCompat.Subhead = 0x7f1101de
com.iptv.android.dev:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701c9
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.CC = 0x7f110136
com.iptv.android.dev:attr/reverseLayout = 0x7f040386
com.iptv.android.dev:attr/customIntegerValue = 0x7f040158
com.iptv.android.dev:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.iptv.android.dev:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.iptv.android.dev:color/material_dynamic_neutral_variant0 = 0x7f060228
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f11017b
com.iptv.android.dev:dimen/abc_text_size_subhead_material = 0x7f07004d
com.iptv.android.dev:attr/motionEasingStandard = 0x7f040320
com.iptv.android.dev:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0e0099
com.iptv.android.dev:macro/m3_comp_navigation_drawer_container_color = 0x7f0e0086
com.iptv.android.dev:attr/colorSurfaceDim = 0x7f040119
com.iptv.android.dev:attr/onHide = 0x7f04033d
com.iptv.android.dev:drawable/m3_tabs_line_indicator = 0x7f0800f7
com.iptv.android.dev:string/abc_menu_function_shortcut_label = 0x7f10000c
com.iptv.android.dev:attr/applyMotionScene = 0x7f040038
com.iptv.android.dev:dimen/mtrl_btn_z = 0x7f07028c
com.iptv.android.dev:layout/mtrl_calendar_horizontal = 0x7f0d0057
com.iptv.android.dev:dimen/m3_comp_navigation_bar_container_elevation = 0x7f07015d
com.iptv.android.dev:styleable/AppCompatTextHelper = 0x7f120011
com.iptv.android.dev:style/Widget.Material3.Button.IconButton.Filled = 0x7f11038d
com.iptv.android.dev:attr/buttonIcon = 0x7f040094
com.iptv.android.dev:attr/percentWidth = 0x7f040358
com.iptv.android.dev:attr/nullable = 0x7f040338
com.iptv.android.dev:macro/m3_comp_search_bar_container_surface_tint_layer_color = 0x7f0e00e7
com.iptv.android.dev:attr/nestedScrollFlags = 0x7f040335
com.iptv.android.dev:style/Widget.MaterialComponents.Chip.Entry = 0x7f11043f
com.iptv.android.dev:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0e012b
com.iptv.android.dev:id/navigation_bar_item_small_label_view = 0x7f0a0158
com.iptv.android.dev:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700ca
com.iptv.android.dev:attr/navigationViewStyle = 0x7f040334
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f11003c
com.iptv.android.dev:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.iptv.android.dev:layout/exo_player_view = 0x7f0d002e
com.iptv.android.dev:attr/itemStrokeWidth = 0x7f04024b
com.iptv.android.dev:color/m3_ref_palette_error40 = 0x7f060106
com.iptv.android.dev:attr/crossfade = 0x7f04014e
com.iptv.android.dev:color/m3_default_color_secondary_text = 0x7f060092
com.iptv.android.dev:attr/navigationRailStyle = 0x7f040333
com.iptv.android.dev:attr/navigationMode = 0x7f040332
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600ba
com.iptv.android.dev:dimen/exo_setting_width = 0x7f07009c
com.iptv.android.dev:attr/multiChoiceItemLayout = 0x7f04032d
com.iptv.android.dev:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f11019d
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0e0156
com.iptv.android.dev:drawable/abc_list_selector_holo_light = 0x7f080057
com.iptv.android.dev:styleable/GradientColor = 0x7f12003b
com.iptv.android.dev:color/material_dynamic_neutral100 = 0x7f06021d
com.iptv.android.dev:id/action_bar_title = 0x7f0a0037
com.iptv.android.dev:attr/moveWhenScrollAtTop = 0x7f04032c
com.iptv.android.dev:string/exo_track_role_alternate = 0x7f1000ba
com.iptv.android.dev:attr/contentPadding = 0x7f040130
com.iptv.android.dev:attr/motion_triggerOnCollision = 0x7f04032b
com.iptv.android.dev:attr/layout_constraintCircleRadius = 0x7f040270
com.iptv.android.dev:style/Base.Widget.MaterialComponents.TextView = 0x7f110121
com.iptv.android.dev:macro/m3_comp_assist_chip_container_shape = 0x7f0e0000
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600eb
com.iptv.android.dev:color/material_slider_inactive_track_color = 0x7f0602a9
com.iptv.android.dev:id/exo_fullscreen = 0x7f0a00b9
com.iptv.android.dev:attr/tabIconTint = 0x7f040401
com.iptv.android.dev:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0e004a
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary90 = 0x7f0600e4
com.iptv.android.dev:attr/flow_horizontalBias = 0x7f0401e3
com.iptv.android.dev:color/bright_foreground_material_dark = 0x7f06002c
com.iptv.android.dev:macro/m3_comp_fab_primary_container_shape = 0x7f0e0038
com.iptv.android.dev:attr/motionPathRotate = 0x7f040326
com.iptv.android.dev:string/settings_autoplay_subtitle = 0x7f10017f
com.iptv.android.dev:attr/tabSelectedTextColor = 0x7f040416
com.iptv.android.dev:id/flip = 0x7f0a00e8
com.iptv.android.dev:id/dragRight = 0x7f0a0095
com.iptv.android.dev:attr/default_artwork = 0x7f040167
com.iptv.android.dev:attr/motionInterpolator = 0x7f040324
com.iptv.android.dev:color/m3_ref_palette_neutral10 = 0x7f06010f
com.iptv.android.dev:attr/motionEasingLinearInterpolator = 0x7f04031f
com.iptv.android.dev:attr/player_layout_id = 0x7f040363
com.iptv.android.dev:dimen/exo_settings_sub_text_size = 0x7f0700a1
com.iptv.android.dev:attr/menuAlignmentMode = 0x7f0402f9
com.iptv.android.dev:id/edit_query = 0x7f0a009d
com.iptv.android.dev:attr/motionEasingDecelerated = 0x7f040319
com.iptv.android.dev:attr/motionDurationShort1 = 0x7f040314
com.iptv.android.dev:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f07016a
com.iptv.android.dev:color/material_dynamic_neutral70 = 0x7f060223
com.iptv.android.dev:attr/maxLines = 0x7f0402f3
com.iptv.android.dev:attr/motionDurationMedium4 = 0x7f040313
com.iptv.android.dev:dimen/m3_comp_outlined_text_field_outline_width = 0x7f07017e
com.iptv.android.dev:dimen/exo_styled_minimal_controls_margin_bottom = 0x7f0700ac
com.iptv.android.dev:attr/motionDurationMedium2 = 0x7f040311
com.iptv.android.dev:string/bottomsheet_drag_handle_content_description = 0x7f10003d
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0e006e
com.iptv.android.dev:attr/motionDurationMedium1 = 0x7f040310
com.iptv.android.dev:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f1102a7
com.iptv.android.dev:attr/backgroundTint = 0x7f040053
com.iptv.android.dev:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001
com.iptv.android.dev:integer/material_motion_duration_long_2 = 0x7f0b002a
com.iptv.android.dev:attr/switchPadding = 0x7f0403fb
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0e006f
com.iptv.android.dev:attr/motionDurationLong2 = 0x7f04030d
com.iptv.android.dev:attr/materialAlertDialogTitlePanelStyle = 0x7f0402c2
com.iptv.android.dev:attr/motionDurationLong1 = 0x7f04030c
com.iptv.android.dev:attr/motionDurationExtraLong4 = 0x7f04030b
com.iptv.android.dev:styleable/ViewPager2 = 0x7f12009d
com.iptv.android.dev:drawable/$avd_hide_password__2 = 0x7f080002
com.iptv.android.dev:attr/layout_collapseMode = 0x7f040265
com.iptv.android.dev:attr/startIconCheckable = 0x7f0403d5
com.iptv.android.dev:drawable/ic_call_answer = 0x7f0800d8
com.iptv.android.dev:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0e0097
com.iptv.android.dev:attr/itemShapeInsetStart = 0x7f040247
com.iptv.android.dev:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.iptv.android.dev:dimen/m3_comp_search_view_docked_header_container_height = 0x7f070195
com.iptv.android.dev:drawable/mtrl_tabs_default_indicator = 0x7f080128
com.iptv.android.dev:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f04031b
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f110486
com.iptv.android.dev:dimen/m3_comp_fab_primary_container_elevation = 0x7f070139
com.iptv.android.dev:attr/motionDebug = 0x7f040307
com.iptv.android.dev:string/mtrl_picker_save = 0x7f10012c
com.iptv.android.dev:attr/customNavigationLayout = 0x7f040159
com.iptv.android.dev:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0702aa
com.iptv.android.dev:attr/colorSurfaceInverse = 0x7f04011a
com.iptv.android.dev:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0601c6
com.iptv.android.dev:attr/mock_labelColor = 0x7f040304
com.iptv.android.dev:attr/badgeWidePadding = 0x7f04005f
com.iptv.android.dev:color/design_dark_default_color_on_primary = 0x7f06003f
com.iptv.android.dev:string/settings_privacy_policy_subtitle = 0x7f100187
com.iptv.android.dev:attr/layout_constraintStart_toStartOf = 0x7f040285
com.iptv.android.dev:color/m3_ref_palette_neutral22 = 0x7f060114
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f110415
com.iptv.android.dev:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f110067
com.iptv.android.dev:attr/tabUnboundedRipple = 0x7f04041a
com.iptv.android.dev:drawable/exo_styled_controls_fullscreen_exit = 0x7f0800c5
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f1101b6
com.iptv.android.dev:macro/m3_comp_radio_button_selected_icon_color = 0x7f0e00dc
com.iptv.android.dev:layout/mtrl_picker_header_dialog = 0x7f0d0064
com.iptv.android.dev:attr/mock_labelBackgroundColor = 0x7f040303
com.iptv.android.dev:string/exo_controls_overflow_show_description = 0x7f10009c
com.iptv.android.dev:dimen/mtrl_fab_translation_z_pressed = 0x7f0702d5
com.iptv.android.dev:attr/minHeight = 0x7f0402fc
com.iptv.android.dev:id/fullscreen_header = 0x7f0a00ec
com.iptv.android.dev:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f11009b
com.iptv.android.dev:attr/colorOnError = 0x7f0400f1
com.iptv.android.dev:style/Theme.SplashScreen.IconBackground = 0x7f1102ae
com.iptv.android.dev:attr/mimeType = 0x7f0402fb
com.iptv.android.dev:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f08007c
com.iptv.android.dev:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f1102a8
com.iptv.android.dev:attr/layout_constraintBottom_creator = 0x7f04026b
com.iptv.android.dev:dimen/m3_alert_dialog_icon_size = 0x7f0700c5
com.iptv.android.dev:drawable/exo_controls_play = 0x7f08008d
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1103db
com.iptv.android.dev:color/material_slider_halo_color = 0x7f0602a7
com.iptv.android.dev:attr/measureWithLargestChild = 0x7f0402f7
com.iptv.android.dev:dimen/abc_text_size_menu_material = 0x7f07004b
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary80 = 0x7f0600f0
com.iptv.android.dev:id/tag_accessibility_clickable_spans = 0x7f0a01ce
com.iptv.android.dev:attr/suffixTextColor = 0x7f0403f7
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.Button = 0x7f11048a
com.iptv.android.dev:attr/maxWidth = 0x7f0402f6
com.iptv.android.dev:attr/checkedState = 0x7f0400b4
com.iptv.android.dev:color/semi_transparent = 0x7f060305
com.iptv.android.dev:attr/buttonIconTint = 0x7f040096
com.iptv.android.dev:color/m3_ref_palette_tertiary80 = 0x7f060156
com.iptv.android.dev:attr/colorOutlineVariant = 0x7f040104
com.iptv.android.dev:attr/lastItemDecorated = 0x7f04025c
com.iptv.android.dev:attr/motionEasingAccelerated = 0x7f040318
com.iptv.android.dev:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401ba
com.iptv.android.dev:attr/motionDurationShort2 = 0x7f040315
com.iptv.android.dev:style/Theme.Material3.Dark.Dialog.Alert = 0x7f110265
com.iptv.android.dev:attr/maxVelocity = 0x7f0402f5
com.iptv.android.dev:attr/fastScrollEnabled = 0x7f0401c9
com.iptv.android.dev:id/fixed_height = 0x7f0a00e6
com.iptv.android.dev:attr/maxNumber = 0x7f0402f4
com.iptv.android.dev:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001e
com.iptv.android.dev:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0e000a
com.iptv.android.dev:attr/state_indeterminate = 0x7f0403e1
com.iptv.android.dev:color/design_default_color_background = 0x7f060048
com.iptv.android.dev:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f11020b
com.iptv.android.dev:attr/displayOptions = 0x7f04016f
com.iptv.android.dev:attr/badgeWithTextHeight = 0x7f040061
com.iptv.android.dev:attr/maxHeight = 0x7f0402f1
com.iptv.android.dev:id/fitCenter = 0x7f0a00e0
com.iptv.android.dev:attr/maxCharacterCount = 0x7f0402f0
com.iptv.android.dev:style/TextAppearance.Material3.TitleLarge = 0x7f11022c
com.iptv.android.dev:attr/buttonBarNegativeButtonStyle = 0x7f04008e
com.iptv.android.dev:attr/materialTimePickerTitleStyle = 0x7f0402ec
com.iptv.android.dev:drawable/exo_controls_pause = 0x7f08008c
com.iptv.android.dev:dimen/mtrl_switch_track_width = 0x7f070316
com.iptv.android.dev:style/AlertDialog.AppCompat = 0x7f110000
com.iptv.android.dev:dimen/mtrl_btn_padding_bottom = 0x7f070281
com.iptv.android.dev:attr/materialTimePickerStyle = 0x7f0402ea
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f11033d
com.iptv.android.dev:id/material_value_index = 0x7f0a012c
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600ea
com.iptv.android.dev:color/m3_ref_palette_error100 = 0x7f060103
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f110413
com.iptv.android.dev:attr/materialThemeOverlay = 0x7f0402e9
com.iptv.android.dev:attr/fastScrollVerticalTrackDrawable = 0x7f0401cd
com.iptv.android.dev:attr/materialSearchViewToolbarStyle = 0x7f0402e7
com.iptv.android.dev:attr/paddingStartSystemWindowInsets = 0x7f04034a
com.iptv.android.dev:attr/layout_constraintCircle = 0x7f04026e
com.iptv.android.dev:string/suggestions_available = 0x7f100196
com.iptv.android.dev:attr/materialSearchViewPrefixStyle = 0x7f0402e4
com.iptv.android.dev:dimen/mtrl_switch_thumb_elevation = 0x7f070312
com.iptv.android.dev:attr/elevationOverlayColor = 0x7f040192
com.iptv.android.dev:dimen/m3_card_dragged_z = 0x7f070108
com.iptv.android.dev:attr/labelBehavior = 0x7f040257
com.iptv.android.dev:dimen/mtrl_snackbar_margin = 0x7f07030e
com.iptv.android.dev:color/ripple_material_light = 0x7f0602fe
com.iptv.android.dev:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.iptv.android.dev:attr/keyPositionType = 0x7f040253
com.iptv.android.dev:attr/materialIconButtonFilledStyle = 0x7f0402df
com.iptv.android.dev:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f11023e
com.iptv.android.dev:dimen/mtrl_btn_dialog_btn_min_width = 0x7f070276
com.iptv.android.dev:macro/m3_comp_slider_active_track_color = 0x7f0e010c
com.iptv.android.dev:attr/materialDividerStyle = 0x7f0402de
com.iptv.android.dev:attr/startDestination = 0x7f0403d4
com.iptv.android.dev:attr/paddingTopNoTitle = 0x7f04034b
com.iptv.android.dev:color/m3_ref_palette_neutral_variant0 = 0x7f060126
com.iptv.android.dev:attr/materialDividerHeavyStyle = 0x7f0402dd
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600c0
com.iptv.android.dev:color/highlighted_text_material_light = 0x7f060075
com.iptv.android.dev:attr/textAppearancePopupMenuHeader = 0x7f04043d
com.iptv.android.dev:color/m3_ref_palette_tertiary60 = 0x7f060154
com.iptv.android.dev:attr/targetId = 0x7f04041b
com.iptv.android.dev:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702d9
com.iptv.android.dev:attr/dayTodayStyle = 0x7f040161
com.iptv.android.dev:attr/materialDisplayDividerStyle = 0x7f0402dc
com.iptv.android.dev:id/decelerate = 0x7f0a0081
com.iptv.android.dev:attr/colorError = 0x7f0400ec
com.iptv.android.dev:attr/materialClockStyle = 0x7f0402db
com.iptv.android.dev:style/ShapeAppearance.MaterialComponents.Badge = 0x7f1101ae
com.iptv.android.dev:id/month_navigation_next = 0x7f0a0136
com.iptv.android.dev:attr/materialCardViewFilledStyle = 0x7f0402d7
com.iptv.android.dev:color/design_dark_default_color_error = 0x7f06003c
com.iptv.android.dev:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1101d4
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f1101bc
com.iptv.android.dev:string/mtrl_picker_text_input_day_abbr = 0x7f100131
com.iptv.android.dev:attr/materialCalendarHeaderToggleButton = 0x7f0402d0
com.iptv.android.dev:attr/tabSelectedTextAppearance = 0x7f040415
com.iptv.android.dev:attr/saturation = 0x7f04038b
com.iptv.android.dev:attr/colorSecondaryFixed = 0x7f04010f
com.iptv.android.dev:drawable/abc_list_selector_disabled_holo_dark = 0x7f080054
com.iptv.android.dev:styleable/Badge = 0x7f120015
com.iptv.android.dev:id/open_search_view_root = 0x7f0a016c
com.iptv.android.dev:attr/state_collapsible = 0x7f0403de
com.iptv.android.dev:attr/materialCalendarHeaderConfirmButton = 0x7f0402cb
com.iptv.android.dev:attr/contentInsetStartWithNavigation = 0x7f04012f
com.iptv.android.dev:attr/onCross = 0x7f04033c
com.iptv.android.dev:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1101f0
com.iptv.android.dev:color/text_disabled = 0x7f060310
com.iptv.android.dev:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f110044
com.iptv.android.dev:attr/autoSizeStepGranularity = 0x7f040045
com.iptv.android.dev:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.iptv.android.dev:attr/labelStyle = 0x7f040258
com.iptv.android.dev:styleable/AppCompatEmojiHelper = 0x7f12000e
com.iptv.android.dev:string/mtrl_exceed_max_badge_number_content_description = 0x7f100112
com.iptv.android.dev:attr/customColorValue = 0x7f040155
com.iptv.android.dev:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f04031c
com.iptv.android.dev:attr/contrast = 0x7f040138
com.iptv.android.dev:id/search_go_btn = 0x7f0a01a0
com.iptv.android.dev:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701de
com.iptv.android.dev:dimen/m3_carousel_small_item_size_max = 0x7f070114
com.iptv.android.dev:color/m3_ref_palette_error80 = 0x7f06010a
com.iptv.android.dev:attr/panelMenuListWidth = 0x7f04034f
com.iptv.android.dev:dimen/mtrl_calendar_header_content_padding = 0x7f07029a
com.iptv.android.dev:attr/materialAlertDialogTheme = 0x7f0402c0
com.iptv.android.dev:string/bottom_sheet_dismiss_description = 0x7f100036
com.iptv.android.dev:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0e013a
com.iptv.android.dev:color/abc_tint_default = 0x7f060014
com.iptv.android.dev:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700d2
com.iptv.android.dev:attr/layout_constraintWidth_min = 0x7f04028f
com.iptv.android.dev:attr/spanCount = 0x7f0403cb
com.iptv.android.dev:integer/m3_sys_motion_duration_short3 = 0x7f0b0020
com.iptv.android.dev:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402bf
com.iptv.android.dev:attr/marginLeftSystemWindowInsets = 0x7f0402bb
com.iptv.android.dev:attr/trackTintMode = 0x7f040496
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0600f8
com.iptv.android.dev:attr/materialAlertDialogBodyTextStyle = 0x7f0402be
com.iptv.android.dev:attr/color = 0x7f0400e4
com.iptv.android.dev:dimen/design_navigation_item_icon_padding = 0x7f070079
com.iptv.android.dev:dimen/abc_dialog_min_width_minor = 0x7f070023
com.iptv.android.dev:attr/errorAccessibilityLabel = 0x7f0401a2
com.iptv.android.dev:attr/hideOnScroll = 0x7f040211
com.iptv.android.dev:string/nav_novelas = 0x7f10014a
com.iptv.android.dev:animator/mtrl_chip_state_list_anim = 0x7f020018
com.iptv.android.dev:attr/menu = 0x7f0402f8
com.iptv.android.dev:attr/floatingActionButtonStyle = 0x7f0401db
com.iptv.android.dev:attr/logoScaleType = 0x7f0402b9
com.iptv.android.dev:attr/logoAdjustViewBounds = 0x7f0402b7
com.iptv.android.dev:attr/hideAnimationBehavior = 0x7f04020d
com.iptv.android.dev:attr/listPreferredItemPaddingEnd = 0x7f0402b2
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary90 = 0x7f0600f1
com.iptv.android.dev:style/ExoStyledControls.TimeText = 0x7f110146
com.iptv.android.dev:string/mtrl_picker_invalid_format = 0x7f100120
com.iptv.android.dev:attr/actionModeSplitBackground = 0x7f04001c
com.iptv.android.dev:attr/shapeAppearanceMediumComponent = 0x7f0403a4
com.iptv.android.dev:color/material_personalized_color_surface_bright = 0x7f060291
com.iptv.android.dev:attr/barrierDirection = 0x7f04006a
com.iptv.android.dev:color/mtrl_btn_text_btn_ripple_color = 0x7f0602b4
com.iptv.android.dev:attr/itemTextAppearance = 0x7f04024c
com.iptv.android.dev:attr/listMenuViewStyle = 0x7f0402ad
com.iptv.android.dev:attr/endIconMinSize = 0x7f040199
com.iptv.android.dev:color/material_personalized_color_control_activated = 0x7f060274
com.iptv.android.dev:attr/colorPrimary = 0x7f040105
com.iptv.android.dev:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.iptv.android.dev:dimen/design_snackbar_padding_horizontal = 0x7f070085
com.iptv.android.dev:string/mtrl_picker_text_input_year_abbr = 0x7f100133
com.iptv.android.dev:id/left = 0x7f0a010d
com.iptv.android.dev:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402a8
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f110194
com.iptv.android.dev:string/material_timepicker_text_input_mode_description = 0x7f100104
com.iptv.android.dev:attr/checkedIconVisible = 0x7f0400b3
com.iptv.android.dev:attr/itemSpacing = 0x7f040249
com.iptv.android.dev:attr/limitBoundsTo = 0x7f0402a3
com.iptv.android.dev:macro/m3_comp_slider_label_container_color = 0x7f0e0112
com.iptv.android.dev:color/material_dynamic_primary60 = 0x7f06023c
com.iptv.android.dev:color/material_dynamic_neutral50 = 0x7f060221
com.iptv.android.dev:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f07016b
com.iptv.android.dev:attr/drawableSize = 0x7f040180
com.iptv.android.dev:attr/lineSpacing = 0x7f0402a5
com.iptv.android.dev:attr/flow_wrapMode = 0x7f0401f0
com.iptv.android.dev:attr/motionEasingStandardAccelerateInterpolator = 0x7f040321
com.iptv.android.dev:styleable/StateListDrawable = 0x7f120089
com.iptv.android.dev:attr/fontProviderCerts = 0x7f0401f4
com.iptv.android.dev:color/on_secondary_container = 0x7f0602ee
com.iptv.android.dev:string/bottom_sheet_behavior = 0x7f100034
com.iptv.android.dev:attr/layout_scrollInterpolator = 0x7f04029f
com.iptv.android.dev:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070229
com.iptv.android.dev:attr/helperTextTextColor = 0x7f04020c
com.iptv.android.dev:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0e00f9
com.iptv.android.dev:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0e00d8
com.iptv.android.dev:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070238
com.iptv.android.dev:attr/layout_goneMarginStart = 0x7f040298
com.iptv.android.dev:id/mtrl_calendar_frame = 0x7f0a013d
com.iptv.android.dev:dimen/mtrl_btn_disabled_elevation = 0x7f070277
com.iptv.android.dev:drawable/ic_tv_placeholder = 0x7f0800ec
com.iptv.android.dev:color/m3_ref_palette_secondary10 = 0x7f060141
com.iptv.android.dev:attr/scrubber_dragged_size = 0x7f040391
com.iptv.android.dev:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f08010c
com.iptv.android.dev:attr/colorOnPrimaryFixedVariant = 0x7f0400f6
com.iptv.android.dev:id/accessibility_custom_action_10 = 0x7f0a0012
com.iptv.android.dev:attr/layout_constraintWidth_percent = 0x7f040290
com.iptv.android.dev:attr/layout_constraintTop_toBottomOf = 0x7f040288
com.iptv.android.dev:attr/trackTint = 0x7f040495
com.iptv.android.dev:dimen/mtrl_calendar_header_height_fullscreen = 0x7f07029e
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070137
com.iptv.android.dev:id/exo_settings_listview = 0x7f0a00ce
com.iptv.android.dev:dimen/m3_appbar_size_large = 0x7f0700cd
com.iptv.android.dev:color/material_personalized_hint_foreground = 0x7f0602a1
com.iptv.android.dev:style/ExoMediaButton.Pause = 0x7f11012d
com.iptv.android.dev:dimen/appcompat_dialog_background_inset = 0x7f070051
com.iptv.android.dev:color/m3_sys_color_secondary_fixed_dim = 0x7f0601f8
com.iptv.android.dev:attr/itemHorizontalTranslationEnabled = 0x7f040238
com.iptv.android.dev:attr/cardUseCompatPadding = 0x7f0400a3
com.iptv.android.dev:attr/layout_constraintTag = 0x7f040286
com.iptv.android.dev:string/collapsed = 0x7f100055
com.iptv.android.dev:dimen/exo_styled_progress_bar_height = 0x7f0700ad
com.iptv.android.dev:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f110057
com.iptv.android.dev:attr/materialAlertDialogTitleIconStyle = 0x7f0402c1
com.iptv.android.dev:style/Base.ThemeOverlay.Material3.Dialog = 0x7f110084
com.iptv.android.dev:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0e0148
com.iptv.android.dev:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0e004f
com.iptv.android.dev:attr/checkedIcon = 0x7f0400ad
com.iptv.android.dev:attr/layout_constraintHorizontal_weight = 0x7f04027d
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f070225
com.iptv.android.dev:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f0701a8
com.iptv.android.dev:attr/titleMarginBottom = 0x7f040473
com.iptv.android.dev:attr/closeItemLayout = 0x7f0400d8
com.iptv.android.dev:attr/textAppearanceSubtitle1 = 0x7f040441
com.iptv.android.dev:id/action_bar_activity_content = 0x7f0a0032
com.iptv.android.dev:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070259
com.iptv.android.dev:attr/layout_constraintHeight_percent = 0x7f04027a
com.iptv.android.dev:id/accessibility_custom_action_3 = 0x7f0a0027
com.iptv.android.dev:attr/colorTertiaryFixedDim = 0x7f040120
com.iptv.android.dev:attr/region_widthMoreThan = 0x7f040381
com.iptv.android.dev:string/action_share = 0x7f100022
com.iptv.android.dev:integer/mtrl_btn_anim_delay_ms = 0x7f0b0031
com.iptv.android.dev:attr/thumbIcon = 0x7f040457
com.iptv.android.dev:style/Widget.Material3.Chip.Input.Elevated = 0x7f1103a4
com.iptv.android.dev:attr/boxCornerRadiusTopStart = 0x7f040086
com.iptv.android.dev:attr/layout_constraintGuide_percent = 0x7f040276
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f110042
com.iptv.android.dev:attr/barrierAllowsGoneWidgets = 0x7f040069
com.iptv.android.dev:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0e002f
com.iptv.android.dev:layout/notification_template_media_custom = 0x7f0d0079
com.iptv.android.dev:attr/layout_constraintBottom_toBottomOf = 0x7f04026c
com.iptv.android.dev:drawable/abc_textfield_default_mtrl_alpha = 0x7f080072
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f110309
com.iptv.android.dev:attr/ensureMinTouchTargetSize = 0x7f0401a0
com.iptv.android.dev:attr/indeterminateProgressStyle = 0x7f040228
com.iptv.android.dev:drawable/exo_notification_small_icon = 0x7f0800be
com.iptv.android.dev:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f110478
com.iptv.android.dev:attr/layout_constraintEnd_toEndOf = 0x7f040272
com.iptv.android.dev:string/content_empty_message = 0x7f100059
com.iptv.android.dev:dimen/m3_ripple_default_alpha = 0x7f0701f0
com.iptv.android.dev:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.iptv.android.dev:id/autoCompleteToStart = 0x7f0a0052
com.iptv.android.dev:drawable/abc_textfield_search_material = 0x7f080075
com.iptv.android.dev:attr/thumbIconTint = 0x7f040459
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f07022e
com.iptv.android.dev:attr/textAppearanceLabelMedium = 0x7f040435
com.iptv.android.dev:attr/divider = 0x7f040170
com.iptv.android.dev:string/abc_toolbar_collapse_description = 0x7f10001a
com.iptv.android.dev:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070237
com.iptv.android.dev:color/material_dynamic_secondary90 = 0x7f06024c
com.iptv.android.dev:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f110484
com.iptv.android.dev:string/selected = 0x7f100175
com.iptv.android.dev:attr/layout_optimizationLevel = 0x7f04029c
com.iptv.android.dev:attr/scrubber_enabled_size = 0x7f040393
com.iptv.android.dev:color/mtrl_fab_icon_text_color_selector = 0x7f0602c5
com.iptv.android.dev:attr/actionModeCutDrawable = 0x7f040016
com.iptv.android.dev:attr/layoutManager = 0x7f040261
com.iptv.android.dev:color/design_dark_default_color_primary = 0x7f060042
com.iptv.android.dev:color/m3_timepicker_button_background_color = 0x7f060209
com.iptv.android.dev:color/foreground_material_light = 0x7f060071
com.iptv.android.dev:attr/layoutDuringTransition = 0x7f040260
com.iptv.android.dev:attr/checkMarkTintMode = 0x7f0400a9
com.iptv.android.dev:attr/launchSingleTop = 0x7f04025d
com.iptv.android.dev:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1103bc
com.iptv.android.dev:attr/path_percent = 0x7f040356
com.iptv.android.dev:style/Widget.MaterialComponents.ChipGroup = 0x7f110441
com.iptv.android.dev:attr/flow_horizontalStyle = 0x7f0401e5
com.iptv.android.dev:attr/lastBaselineToBottomHeight = 0x7f04025b
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f1101bd
com.iptv.android.dev:id/accessibility_custom_action_31 = 0x7f0a0029
com.iptv.android.dev:attr/restoreState = 0x7f040385
com.iptv.android.dev:string/date_picker_switch_to_previous_month = 0x7f100075
com.iptv.android.dev:attr/largeFontVerticalOffsetAdjustment = 0x7f04025a
com.iptv.android.dev:id/scrollIndicatorUp = 0x7f0a0198
com.iptv.android.dev:attr/trackHeight = 0x7f040493
com.iptv.android.dev:drawable/notification_bg = 0x7f08012b
com.iptv.android.dev:attr/chipEndPadding = 0x7f0400b8
com.iptv.android.dev:attr/shouldRemoveExpandedCorners = 0x7f0403a9
com.iptv.android.dev:attr/tickColorInactive = 0x7f040463
com.iptv.android.dev:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600ec
com.iptv.android.dev:color/m3_ref_palette_error60 = 0x7f060108
com.iptv.android.dev:attr/dropDownBackgroundTint = 0x7f040188
com.iptv.android.dev:attr/labelVisibilityMode = 0x7f040259
com.iptv.android.dev:id/action_bar_subtitle = 0x7f0a0036
com.iptv.android.dev:attr/actionBarTabBarStyle = 0x7f040007
com.iptv.android.dev:color/mtrl_navigation_item_text_color = 0x7f0602d1
com.iptv.android.dev:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0401d9
com.iptv.android.dev:dimen/m3_comp_switch_disabled_track_opacity = 0x7f0701ae
com.iptv.android.dev:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0c0009
com.iptv.android.dev:attr/logo = 0x7f0402b6
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface = 0x7f060198
com.iptv.android.dev:attr/itemStrokeColor = 0x7f04024a
com.iptv.android.dev:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0e0158
com.iptv.android.dev:animator/fragment_fade_enter = 0x7f020005
com.iptv.android.dev:attr/actionModeShareDrawable = 0x7f04001b
com.iptv.android.dev:color/design_default_color_error = 0x7f060049
com.iptv.android.dev:style/TextAppearance.AppCompat.Medium = 0x7f1101d7
com.iptv.android.dev:layout/design_layout_snackbar_include = 0x7f0d0020
com.iptv.android.dev:color/design_fab_stroke_top_outer_color = 0x7f06005c
com.iptv.android.dev:attr/toolbarNavigationButtonStyle = 0x7f04047f
com.iptv.android.dev:attr/itemShapeInsetEnd = 0x7f040246
com.iptv.android.dev:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0e0031
com.iptv.android.dev:color/material_personalized_color_surface_container_high = 0x7f060293
com.iptv.android.dev:dimen/abc_text_size_small_material = 0x7f07004c
com.iptv.android.dev:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f110495
com.iptv.android.dev:attr/itemShapeAppearanceOverlay = 0x7f040243
com.iptv.android.dev:attr/layout_constraintVertical_weight = 0x7f04028c
com.iptv.android.dev:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f080101
com.iptv.android.dev:style/Widget.Design.Snackbar = 0x7f110373
com.iptv.android.dev:id/line3 = 0x7f0a0111
com.iptv.android.dev:attr/colorOnTertiaryFixedVariant = 0x7f040102
com.iptv.android.dev:dimen/m3_large_fab_size = 0x7f0701d7
com.iptv.android.dev:attr/itemShapeAppearance = 0x7f040242
com.iptv.android.dev:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f110186
com.iptv.android.dev:attr/itemRippleColor = 0x7f040241
com.iptv.android.dev:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f110108
com.iptv.android.dev:dimen/m3_side_sheet_width = 0x7f070203
com.iptv.android.dev:color/m3_sys_color_light_on_secondary = 0x7f0601d8
com.iptv.android.dev:attr/itemMaxLines = 0x7f04023c
com.iptv.android.dev:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1103b3
com.iptv.android.dev:attr/itemPaddingTop = 0x7f040240
com.iptv.android.dev:attr/shapeAppearanceLargeComponent = 0x7f0403a3
com.iptv.android.dev:id/select_dialog_listview = 0x7f0a01a5
com.iptv.android.dev:attr/motionEasingEmphasizedInterpolator = 0x7f04031d
com.iptv.android.dev:attr/thickness = 0x7f040454
com.iptv.android.dev:styleable/BottomNavigationView = 0x7f120018
com.iptv.android.dev:attr/itemIconTint = 0x7f04023b
com.iptv.android.dev:attr/backgroundTintMode = 0x7f040054
com.iptv.android.dev:dimen/exo_icon_horizontal_margin = 0x7f070095
com.iptv.android.dev:attr/maxButtonHeight = 0x7f0402ef
com.iptv.android.dev:dimen/compat_notification_large_icon_max_height = 0x7f07005b
com.iptv.android.dev:attr/constraintSetStart = 0x7f040125
com.iptv.android.dev:color/surface_variant = 0x7f060309
com.iptv.android.dev:color/m3_ref_palette_secondary50 = 0x7f060146
com.iptv.android.dev:dimen/m3_navigation_rail_elevation = 0x7f0701e6
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_background = 0x7f060185
com.iptv.android.dev:color/m3_ref_palette_neutral96 = 0x7f060123
com.iptv.android.dev:attr/isMaterial3Theme = 0x7f040232
com.iptv.android.dev:attr/menuGravity = 0x7f0402fa
com.iptv.android.dev:attr/chipIconVisible = 0x7f0400be
com.iptv.android.dev:attr/flow_lastHorizontalStyle = 0x7f0401e7
com.iptv.android.dev:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f070270
com.iptv.android.dev:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1100d8
com.iptv.android.dev:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f070185
com.iptv.android.dev:attr/colorOnPrimaryContainer = 0x7f0400f4
com.iptv.android.dev:attr/iconPadding = 0x7f040220
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0e0070
com.iptv.android.dev:layout/support_simple_spinner_dropdown_item = 0x7f0d0080
com.iptv.android.dev:attr/autoAdjustToWithinGrandparentBounds = 0x7f04003f
com.iptv.android.dev:drawable/abc_ic_clear_material = 0x7f08003f
com.iptv.android.dev:attr/textAppearanceOverline = 0x7f04043c
com.iptv.android.dev:attr/icon = 0x7f04021d
com.iptv.android.dev:attr/thumbTintMode = 0x7f040460
com.iptv.android.dev:drawable/exo_notification_play = 0x7f0800bb
com.iptv.android.dev:attr/cursorErrorColor = 0x7f040151
com.iptv.android.dev:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f11007d
com.iptv.android.dev:attr/tabTextColor = 0x7f040419
com.iptv.android.dev:attr/switchStyle = 0x7f0403fc
com.iptv.android.dev:attr/hintTextAppearance = 0x7f040216
com.iptv.android.dev:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080080
com.iptv.android.dev:attr/hintEnabled = 0x7f040215
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f11017e
com.iptv.android.dev:attr/hide_on_touch = 0x7f040213
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Headline1 = 0x7f110235
com.iptv.android.dev:id/transitionToEnd = 0x7f0a01f3
com.iptv.android.dev:color/m3_assist_chip_stroke_color = 0x7f060079
com.iptv.android.dev:attr/hideNavigationIcon = 0x7f04020f
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f110410
com.iptv.android.dev:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f06029e
com.iptv.android.dev:layout/design_navigation_item_separator = 0x7f0d0026
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600b6
com.iptv.android.dev:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.iptv.android.dev:id/unlabeled = 0x7f0a01fd
com.iptv.android.dev:attr/marginHorizontal = 0x7f0402ba
com.iptv.android.dev:attr/expandedHintEnabled = 0x7f0401af
com.iptv.android.dev:attr/colorControlNormal = 0x7f0400eb
com.iptv.android.dev:attr/overlapAnchor = 0x7f040342
com.iptv.android.dev:id/exo_ad_overlay = 0x7f0a00a7
com.iptv.android.dev:color/bright_foreground_inverse_material_light = 0x7f06002b
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0601ab
com.iptv.android.dev:attr/colorSurfaceContainerHigh = 0x7f040115
com.iptv.android.dev:attr/helperText = 0x7f040209
com.iptv.android.dev:dimen/mtrl_calendar_month_vertical_padding = 0x7f0702a6
com.iptv.android.dev:attr/cornerSizeBottomLeft = 0x7f040144
com.iptv.android.dev:dimen/m3_card_disabled_z = 0x7f070107
com.iptv.android.dev:color/m3_sys_color_light_surface_dim = 0x7f0601eb
com.iptv.android.dev:attr/dropDownListViewStyle = 0x7f040189
com.iptv.android.dev:string/cd_play_button = 0x7f10004c
com.iptv.android.dev:attr/headerLayout = 0x7f040207
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0601ad
com.iptv.android.dev:dimen/m3_extended_fab_bottom_padding = 0x7f0701cc
com.iptv.android.dev:id/hideable = 0x7f0a00f7
com.iptv.android.dev:attr/actionModeFindDrawable = 0x7f040017
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1103da
com.iptv.android.dev:style/Widget.AppCompat.Button = 0x7f11032c
com.iptv.android.dev:layout/material_chip_input_combo = 0x7f0d003f
com.iptv.android.dev:attr/haloRadius = 0x7f040206
com.iptv.android.dev:attr/searchHintIcon = 0x7f040394
com.iptv.android.dev:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080028
com.iptv.android.dev:attr/haloColor = 0x7f040205
com.iptv.android.dev:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0702b0
com.iptv.android.dev:attr/graph = 0x7f040204
com.iptv.android.dev:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f110117
com.iptv.android.dev:dimen/mtrl_switch_track_height = 0x7f070315
com.iptv.android.dev:attr/tooltipStyle = 0x7f040484
com.iptv.android.dev:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0e0007
com.iptv.android.dev:attr/colorOnSecondaryFixed = 0x7f0400fa
com.iptv.android.dev:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0e0142
com.iptv.android.dev:attr/foregroundInsidePadding = 0x7f0401ff
com.iptv.android.dev:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400e0
com.iptv.android.dev:attr/backgroundStacked = 0x7f040052
com.iptv.android.dev:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f070173
com.iptv.android.dev:attr/motionStagger = 0x7f040328
com.iptv.android.dev:attr/counterTextAppearance = 0x7f04014c
com.iptv.android.dev:dimen/abc_dialog_title_divider_material = 0x7f070026
com.iptv.android.dev:string/material_minute_selection = 0x7f1000f4
com.iptv.android.dev:attr/isMaterialTheme = 0x7f040233
com.iptv.android.dev:attr/fontProviderFetchTimeout = 0x7f0401f6
com.iptv.android.dev:attr/clockFaceBackgroundColor = 0x7f0400cd
com.iptv.android.dev:attr/colorOnPrimarySurface = 0x7f0400f7
com.iptv.android.dev:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0e00e8
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600cf
com.iptv.android.dev:attr/flow_verticalStyle = 0x7f0401ef
com.iptv.android.dev:string/live_tv_channels = 0x7f1000d7
com.iptv.android.dev:dimen/exo_settings_main_text_size = 0x7f07009f
com.iptv.android.dev:color/m3_dark_primary_text_disable_only = 0x7f060090
com.iptv.android.dev:attr/flow_maxElementsWrap = 0x7f0401ea
com.iptv.android.dev:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1102f3
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary40 = 0x7f0600df
com.iptv.android.dev:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700d1
com.iptv.android.dev:attr/singleChoiceItemLayout = 0x7f0403c3
com.iptv.android.dev:attr/flow_lastVerticalBias = 0x7f0401e8
com.iptv.android.dev:styleable/TextInputLayout = 0x7f120094
com.iptv.android.dev:attr/sizePercent = 0x7f0403c6
com.iptv.android.dev:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0e00e5
com.iptv.android.dev:attr/colorSurfaceBright = 0x7f040113
com.iptv.android.dev:attr/materialCalendarHeaderDivider = 0x7f0402cc
com.iptv.android.dev:macro/m3_comp_fab_tertiary_icon_color = 0x7f0e0041
com.iptv.android.dev:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080034
com.iptv.android.dev:attr/flow_lastHorizontalBias = 0x7f0401e6
com.iptv.android.dev:attr/layout_constraintLeft_toLeftOf = 0x7f04027f
com.iptv.android.dev:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f110171
com.iptv.android.dev:attr/state_above_anchor = 0x7f0403dc
com.iptv.android.dev:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f110170
com.iptv.android.dev:attr/flow_horizontalGap = 0x7f0401e4
com.iptv.android.dev:attr/closeIconSize = 0x7f0400d4
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f060191
com.iptv.android.dev:attr/flow_horizontalAlign = 0x7f0401e2
com.iptv.android.dev:attr/flow_firstHorizontalBias = 0x7f0401de
com.iptv.android.dev:id/tag_accessibility_actions = 0x7f0a01cd
com.iptv.android.dev:dimen/hint_alpha_material_dark = 0x7f0700b9
com.iptv.android.dev:id/textinput_suffix_text = 0x7f0a01e9
com.iptv.android.dev:attr/contentPaddingStart = 0x7f040135
com.iptv.android.dev:attr/coordinatorLayoutStyle = 0x7f04013b
com.iptv.android.dev:attr/floatingActionButtonTertiaryStyle = 0x7f0401dd
com.iptv.android.dev:layout/abc_screen_simple = 0x7f0d0015
com.iptv.android.dev:drawable/exo_notification_stop = 0x7f0800bf
com.iptv.android.dev:attr/floatingActionButtonSurfaceStyle = 0x7f0401dc
com.iptv.android.dev:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.iptv.android.dev:dimen/notification_right_side_padding_top = 0x7f070330
com.iptv.android.dev:dimen/mtrl_extended_fab_min_height = 0x7f0702ca
com.iptv.android.dev:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f070189
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0e0168
com.iptv.android.dev:attr/topInsetScrimEnabled = 0x7f040486
com.iptv.android.dev:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0401da
com.iptv.android.dev:style/Widget.MaterialComponents.CardView = 0x7f11043b
com.iptv.android.dev:dimen/m3_sys_elevation_level0 = 0x7f07020c
com.iptv.android.dev:attr/colorOnErrorContainer = 0x7f0400f2
com.iptv.android.dev:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.iptv.android.dev:color/m3_timepicker_button_text_color = 0x7f06020b
com.iptv.android.dev:color/m3_card_ripple_color = 0x7f060083
com.iptv.android.dev:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0e0080
com.iptv.android.dev:id/exitUntilCollapsed = 0x7f0a00a6
com.iptv.android.dev:attr/behavior_autoShrink = 0x7f04006d
com.iptv.android.dev:drawable/m3_radiobutton_ripple = 0x7f0800f4
com.iptv.android.dev:color/material_personalized_color_error = 0x7f060277
com.iptv.android.dev:attr/closeIconTint = 0x7f0400d6
com.iptv.android.dev:attr/imageButtonStyle = 0x7f040226
com.iptv.android.dev:attr/thumbTint = 0x7f04045f
com.iptv.android.dev:style/Animation.Material3.SideSheetDialog.Right = 0x7f110009
com.iptv.android.dev:color/material_dynamic_neutral_variant10 = 0x7f060229
com.iptv.android.dev:attr/layout_constraintGuide_end = 0x7f040275
com.iptv.android.dev:attr/radioButtonStyle = 0x7f040378
com.iptv.android.dev:attr/floatingActionButtonSecondaryStyle = 0x7f0401d5
com.iptv.android.dev:styleable/Transition = 0x7f120099
com.iptv.android.dev:dimen/mtrl_navigation_rail_compact_width = 0x7f0702e8
com.iptv.android.dev:styleable/Insets = 0x7f12003e
com.iptv.android.dev:attr/dynamicColorThemeOverlay = 0x7f04018c
com.iptv.android.dev:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401cf
com.iptv.android.dev:attr/ad_marker_color = 0x7f040027
com.iptv.android.dev:attr/layoutDescription = 0x7f04025f
com.iptv.android.dev:string/path_password_eye = 0x7f100154
com.iptv.android.dev:attr/layout_constraintCircleAngle = 0x7f04026f
com.iptv.android.dev:attr/firstBaselineToTopHeight = 0x7f0401ce
com.iptv.android.dev:color/material_grey_300 = 0x7f06025d
com.iptv.android.dev:color/m3_icon_button_icon_color_selector = 0x7f0600a5
com.iptv.android.dev:attr/actionViewClass = 0x7f040024
com.iptv.android.dev:id/SYM = 0x7f0a000b
com.iptv.android.dev:attr/textAppearanceHeadline5 = 0x7f04042f
com.iptv.android.dev:attr/textAllCaps = 0x7f040420
com.iptv.android.dev:attr/fastScrollVerticalThumbDrawable = 0x7f0401cc
com.iptv.android.dev:string/exo_track_surround = 0x7f1000c4
com.iptv.android.dev:attr/layout_goneMarginLeft = 0x7f040296
com.iptv.android.dev:style/Widget.Material3.BottomAppBar = 0x7f110380
com.iptv.android.dev:id/multiply = 0x7f0a0151
com.iptv.android.dev:attr/motionEasingStandardInterpolator = 0x7f040323
com.iptv.android.dev:attr/fastScrollHorizontalThumbDrawable = 0x7f0401ca
com.iptv.android.dev:dimen/notification_top_pad_large_text = 0x7f070335
com.iptv.android.dev:attr/progressBarPadding = 0x7f040373
com.iptv.android.dev:attr/fabCradleRoundedCornerRadius = 0x7f0401c5
com.iptv.android.dev:color/material_grey_50 = 0x7f06025e
com.iptv.android.dev:attr/fabCradleMargin = 0x7f0401c4
com.iptv.android.dev:attr/trackColorActive = 0x7f04048d
com.iptv.android.dev:color/m3_sys_color_light_surface_variant = 0x7f0601ec
com.iptv.android.dev:layout/abc_list_menu_item_icon = 0x7f0d000f
com.iptv.android.dev:attr/fabAnimationMode = 0x7f0401c3
com.iptv.android.dev:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f070232
com.iptv.android.dev:attr/dividerInsetEnd = 0x7f040173
com.iptv.android.dev:style/Widget.AppCompat.SearchView.ActionBar = 0x7f11035f
com.iptv.android.dev:string/mtrl_picker_toggle_to_day_selection = 0x7f100136
com.iptv.android.dev:attr/fabAlignmentMode = 0x7f0401c0
com.iptv.android.dev:attr/tabBackground = 0x7f0403fe
com.iptv.android.dev:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f07021a
com.iptv.android.dev:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f11010a
com.iptv.android.dev:color/white = 0x7f06031a
com.iptv.android.dev:id/disableHome = 0x7f0a008e
com.iptv.android.dev:attr/extraMultilineHeightEnabled = 0x7f0401bf
com.iptv.android.dev:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0e00e3
com.iptv.android.dev:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401be
com.iptv.android.dev:attr/shutter_background_color = 0x7f0403bc
com.iptv.android.dev:id/exo_controls_background = 0x7f0a00b2
com.iptv.android.dev:dimen/design_navigation_padding_bottom = 0x7f07007c
com.iptv.android.dev:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.iptv.android.dev:attr/expandedTitleMarginTop = 0x7f0401b5
com.iptv.android.dev:styleable/KeyTimeCycle = 0x7f120045
com.iptv.android.dev:style/Widget.MaterialComponents.ProgressIndicator = 0x7f110473
com.iptv.android.dev:string/abc_menu_meta_shortcut_label = 0x7f10000d
com.iptv.android.dev:id/open_search_view_background = 0x7f0a0165
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080014
com.iptv.android.dev:attr/cornerSize = 0x7f040143
com.iptv.android.dev:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701c7
com.iptv.android.dev:color/material_personalized_color_surface_container = 0x7f060292
com.iptv.android.dev:dimen/compat_button_inset_horizontal_material = 0x7f070056
com.iptv.android.dev:color/m3_ref_palette_secondary60 = 0x7f060147
com.iptv.android.dev:styleable/Spinner = 0x7f120087
com.iptv.android.dev:id/accessibility_custom_action_13 = 0x7f0a0015
com.iptv.android.dev:attr/expandedTitleMarginEnd = 0x7f0401b3
com.iptv.android.dev:macro/m3_comp_fab_primary_container_color = 0x7f0e0037
com.iptv.android.dev:attr/textStartPadding = 0x7f040452
com.iptv.android.dev:id/contentPanel = 0x7f0a0076
com.iptv.android.dev:attr/expanded = 0x7f0401ae
com.iptv.android.dev:attr/queryHint = 0x7f040376
com.iptv.android.dev:drawable/exo_controls_repeat_off = 0x7f080090
com.iptv.android.dev:attr/defaultMarginsEnabled = 0x7f040163
com.iptv.android.dev:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f110348
com.iptv.android.dev:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070268
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0601af
com.iptv.android.dev:attr/exitAnim = 0x7f0401ac
com.iptv.android.dev:color/m3_dynamic_dark_primary_text_disable_only = 0x7f060097
com.iptv.android.dev:id/current_indicator = 0x7f0a007c
com.iptv.android.dev:drawable/abc_list_longpressed_holo = 0x7f08004f
com.iptv.android.dev:attr/errorTextColor = 0x7f0401ab
com.iptv.android.dev:style/Widget.Material3.Toolbar = 0x7f110416
com.iptv.android.dev:layout/exo_styled_player_view = 0x7f0d0032
com.iptv.android.dev:id/exo_shuffle = 0x7f0a00cf
com.iptv.android.dev:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080021
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f110097
com.iptv.android.dev:id/exo_duration = 0x7f0a00b3
com.iptv.android.dev:attr/flow_verticalAlign = 0x7f0401ec
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080012
com.iptv.android.dev:attr/errorTextAppearance = 0x7f0401aa
com.iptv.android.dev:color/m3_dynamic_dark_default_color_secondary_text = 0x7f060094
com.iptv.android.dev:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0601a9
com.iptv.android.dev:attr/errorIconDrawable = 0x7f0401a6
com.iptv.android.dev:color/design_dark_default_color_on_surface = 0x7f060041
com.iptv.android.dev:attr/enterAnim = 0x7f0401a1
com.iptv.android.dev:id/action_bar_spinner = 0x7f0a0035
com.iptv.android.dev:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f070254
com.iptv.android.dev:attr/motionDurationLong4 = 0x7f04030f
com.iptv.android.dev:attr/endIconTint = 0x7f04019c
com.iptv.android.dev:attr/actionModeCopyDrawable = 0x7f040015
com.iptv.android.dev:attr/colorPrimarySurface = 0x7f04010b
com.iptv.android.dev:id/search_bar = 0x7f0a019c
com.iptv.android.dev:attr/behavior_overlapTop = 0x7f040073
com.iptv.android.dev:color/m3_fab_efab_background_color_selector = 0x7f06009f
com.iptv.android.dev:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.iptv.android.dev:attr/windowActionBar = 0x7f0404b3
com.iptv.android.dev:id/accessibility_custom_action_12 = 0x7f0a0014
com.iptv.android.dev:attr/listChoiceBackgroundIndicator = 0x7f0402a7
com.iptv.android.dev:attr/showDelay = 0x7f0403ac
com.iptv.android.dev:string/date_picker_switch_to_calendar_mode = 0x7f100071
com.iptv.android.dev:id/textinput_prefix_text = 0x7f0a01e8
com.iptv.android.dev:attr/endIconScaleType = 0x7f04019b
com.iptv.android.dev:color/material_personalized_color_error_container = 0x7f060278
com.iptv.android.dev:color/design_dark_default_color_secondary_variant = 0x7f060046
com.iptv.android.dev:drawable/abc_spinner_textfield_background_material = 0x7f080066
com.iptv.android.dev:dimen/mtrl_calendar_text_input_padding_top = 0x7f0702af
com.iptv.android.dev:attr/enableEdgeToEdge = 0x7f040195
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0600fc
com.iptv.android.dev:attr/customBoolean = 0x7f040153
com.iptv.android.dev:attr/emojiCompatEnabled = 0x7f040194
com.iptv.android.dev:attr/isLightTheme = 0x7f040230
com.iptv.android.dev:attr/elevationOverlayEnabled = 0x7f040193
com.iptv.android.dev:drawable/abc_star_half_black_48dp = 0x7f080068
com.iptv.android.dev:style/Widget.AppCompat.ListPopupWindow = 0x7f110352
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080010
com.iptv.android.dev:attr/buttonTint = 0x7f04009b
com.iptv.android.dev:color/cardview_light_background = 0x7f060036
com.iptv.android.dev:attr/endIconContentDescription = 0x7f040197
com.iptv.android.dev:color/material_on_background_emphasis_medium = 0x7f060269
com.iptv.android.dev:string/material_timepicker_am = 0x7f1000fe
com.iptv.android.dev:attr/elevationOverlayAccentColor = 0x7f040191
com.iptv.android.dev:dimen/m3_badge_offset = 0x7f0700d7
com.iptv.android.dev:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0e0167
com.iptv.android.dev:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.iptv.android.dev:attr/tabPaddingTop = 0x7f040412
com.iptv.android.dev:dimen/mtrl_progress_circular_inset = 0x7f0702f0
com.iptv.android.dev:attr/indeterminateAnimationType = 0x7f040227
com.iptv.android.dev:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f110046
com.iptv.android.dev:attr/editTextColor = 0x7f04018e
com.iptv.android.dev:attr/colorOutline = 0x7f040103
com.iptv.android.dev:id/transition_layout_save = 0x7f0a01f6
com.iptv.android.dev:attr/defaultScrollFlagsEnabled = 0x7f040165
com.iptv.android.dev:attr/duration = 0x7f04018b
com.iptv.android.dev:attr/dropdownListPreferredItemHeight = 0x7f04018a
com.iptv.android.dev:attr/hideMotionSpec = 0x7f04020e
com.iptv.android.dev:style/Base.Widget.Material3.ActionMode = 0x7f110101
com.iptv.android.dev:color/brand_accent = 0x7f060023
com.iptv.android.dev:color/m3_ref_palette_white = 0x7f06015a
com.iptv.android.dev:id/accessibility_custom_action_9 = 0x7f0a002f
com.iptv.android.dev:attr/transitionShapeAppearance = 0x7f04049b
com.iptv.android.dev:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f070172
com.iptv.android.dev:style/IPTVButton.Secondary = 0x7f11014e
com.iptv.android.dev:color/primary_container = 0x7f0602f4
com.iptv.android.dev:attr/minWidth = 0x7f040300
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f070216
com.iptv.android.dev:color/mtrl_switch_thumb_icon_tint = 0x7f0602d8
com.iptv.android.dev:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f110412
com.iptv.android.dev:attr/drawerLayoutCornerSize = 0x7f040186
com.iptv.android.dev:color/m3_sys_color_light_primary = 0x7f0601e0
com.iptv.android.dev:attr/played_color = 0x7f040362
com.iptv.android.dev:attr/use_artwork = 0x7f0404a5
com.iptv.android.dev:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f1101b1
com.iptv.android.dev:attr/buttonStyleSmall = 0x7f04009a
com.iptv.android.dev:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0e0016
com.iptv.android.dev:id/notification_main_column = 0x7f0a015f
com.iptv.android.dev:attr/drawerArrowStyle = 0x7f040185
com.iptv.android.dev:styleable/SwitchMaterial = 0x7f12008f
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f110041
com.iptv.android.dev:id/notification_background = 0x7f0a015e
com.iptv.android.dev:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f070253
com.iptv.android.dev:attr/wavePeriod = 0x7f0404b0
com.iptv.android.dev:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0e0149
com.iptv.android.dev:attr/simpleItemLayout = 0x7f0403bf
com.iptv.android.dev:id/material_timepicker_container = 0x7f0a0128
com.iptv.android.dev:attr/spinBars = 0x7f0403cc
com.iptv.android.dev:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1103f4
com.iptv.android.dev:attr/badgeWithTextRadius = 0x7f040062
com.iptv.android.dev:id/design_navigation_view = 0x7f0a008a
com.iptv.android.dev:attr/flow_firstVerticalBias = 0x7f0401e0
com.iptv.android.dev:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1102d5
com.iptv.android.dev:attr/layout_collapseParallaxMultiplier = 0x7f040266
com.iptv.android.dev:string/error_playback = 0x7f10008d
com.iptv.android.dev:attr/textInputOutlinedDenseStyle = 0x7f04044d
com.iptv.android.dev:attr/drawPath = 0x7f04017b
com.iptv.android.dev:attr/dividerThickness = 0x7f040176
com.iptv.android.dev:attr/suffixText = 0x7f0403f5
com.iptv.android.dev:attr/dividerHorizontal = 0x7f040172
com.iptv.android.dev:styleable/NavHost = 0x7f12006b
com.iptv.android.dev:attr/behavior_halfExpandedRatio = 0x7f040071
com.iptv.android.dev:attr/layout_constraintBaseline_toBaselineOf = 0x7f04026a
com.iptv.android.dev:color/button_focused_primary = 0x7f06002f
com.iptv.android.dev:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400e2
com.iptv.android.dev:dimen/m3_searchbar_padding_start = 0x7f0701fa
com.iptv.android.dev:attr/colorSwitchThumbNormal = 0x7f04011c
com.iptv.android.dev:attr/shapeAppearanceCornerSmall = 0x7f0403a2
com.iptv.android.dev:attr/keylines = 0x7f040255
com.iptv.android.dev:attr/action = 0x7f040000
com.iptv.android.dev:attr/colorSecondary = 0x7f04010d
com.iptv.android.dev:attr/defaultState = 0x7f040166
com.iptv.android.dev:attr/switchTextAppearance = 0x7f0403fd
com.iptv.android.dev:attr/defaultDuration = 0x7f040162
com.iptv.android.dev:attr/listChoiceIndicatorSingleAnimated = 0x7f0402a9
com.iptv.android.dev:attr/bottomAppBarStyle = 0x7f04007a
com.iptv.android.dev:attr/daySelectedStyle = 0x7f04015f
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Large = 0x7f110021
com.iptv.android.dev:attr/counterMaxLength = 0x7f040149
com.iptv.android.dev:drawable/exo_notification_fastforward = 0x7f0800b8
com.iptv.android.dev:color/m3_ref_palette_primary10 = 0x7f060134
com.iptv.android.dev:attr/actionBarSize = 0x7f040004
com.iptv.android.dev:style/Base.V14.Theme.Material3.Dark = 0x7f11008c
com.iptv.android.dev:attr/dayInvalidStyle = 0x7f04015e
com.iptv.android.dev:dimen/material_clock_hand_stroke_width = 0x7f070244
com.iptv.android.dev:string/content_play = 0x7f10005b
com.iptv.android.dev:attr/badgeShapeAppearance = 0x7f040058
com.iptv.android.dev:attr/titleTextEllipsize = 0x7f04047b
com.iptv.android.dev:styleable/ConstraintSet = 0x7f12002a
com.iptv.android.dev:id/material_timepicker_cancel_button = 0x7f0a0127
com.iptv.android.dev:animator/mtrl_card_state_list_anim = 0x7f020017
com.iptv.android.dev:drawable/$m3_avd_hide_password__1 = 0x7f080007
com.iptv.android.dev:styleable/MaterialCheckBoxStates = 0x7f120055
com.iptv.android.dev:dimen/m3_large_fab_max_image_size = 0x7f0701d6
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_container = 0x7f0601b8
com.iptv.android.dev:attr/dataPattern = 0x7f04015d
com.iptv.android.dev:macro/m3_comp_menu_container_color = 0x7f0e0060
com.iptv.android.dev:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f07030d
com.iptv.android.dev:style/Base.V26.Theme.AppCompat.Light = 0x7f1100b9
com.iptv.android.dev:attr/maxActionInlineWidth = 0x7f0402ee
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Chip = 0x7f110234
com.iptv.android.dev:interpolator/m3_sys_motion_easing_standard = 0x7f0c000b
com.iptv.android.dev:attr/fabAnchorMode = 0x7f0401c2
com.iptv.android.dev:attr/tabInlineLabel = 0x7f04040a
com.iptv.android.dev:attr/switchMinWidth = 0x7f0403fa
com.iptv.android.dev:color/primary_text_disabled_material_dark = 0x7f0602fb
com.iptv.android.dev:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.iptv.android.dev:attr/layout_goneMarginRight = 0x7f040297
com.iptv.android.dev:attr/customDimension = 0x7f040156
com.iptv.android.dev:attr/customColorDrawableValue = 0x7f040154
com.iptv.android.dev:attr/collapsedSize = 0x7f0400db
com.iptv.android.dev:layout/design_navigation_item_subheader = 0x7f0d0027
com.iptv.android.dev:color/m3_sys_color_light_on_tertiary = 0x7f0601dc
com.iptv.android.dev:dimen/mtrl_textinput_end_icon_margin_start = 0x7f07031d
com.iptv.android.dev:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.iptv.android.dev:dimen/m3_alert_dialog_icon_margin = 0x7f0700c4
com.iptv.android.dev:styleable/PopupWindow = 0x7f120076
com.iptv.android.dev:attr/progressBarStyle = 0x7f040374
com.iptv.android.dev:attr/colorOnSecondary = 0x7f0400f8
com.iptv.android.dev:attr/checkedTextViewStyle = 0x7f0400b5
com.iptv.android.dev:attr/fabCradleVerticalOffset = 0x7f0401c6
com.iptv.android.dev:id/accessibility_custom_action_27 = 0x7f0a0024
com.iptv.android.dev:attr/cornerSizeBottomRight = 0x7f040145
com.iptv.android.dev:attr/boxCornerRadiusBottomEnd = 0x7f040083
com.iptv.android.dev:attr/height = 0x7f040208
com.iptv.android.dev:style/Animation.AppCompat.Dialog = 0x7f110002
com.iptv.android.dev:attr/cornerSizeTopLeft = 0x7f040146
com.iptv.android.dev:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080035
com.iptv.android.dev:color/material_personalized_color_on_secondary_container = 0x7f06027f
com.iptv.android.dev:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.iptv.android.dev:attr/tickRadiusInactive = 0x7f040468
com.iptv.android.dev:attr/layout_constraintBaseline_creator = 0x7f040269
com.iptv.android.dev:style/Base.V24.Theme.Material3.Dark = 0x7f1100b4
com.iptv.android.dev:drawable/ic_keyboard_black_24dp = 0x7f0800e0
com.iptv.android.dev:attr/listPreferredItemHeightSmall = 0x7f0402b1
com.iptv.android.dev:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1102e4
com.iptv.android.dev:attr/buttonGravity = 0x7f040093
com.iptv.android.dev:attr/bar_height = 0x7f040068
com.iptv.android.dev:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0e00b7
com.iptv.android.dev:attr/boxBackgroundColor = 0x7f040080
com.iptv.android.dev:anim/abc_slide_in_top = 0x7f010007
com.iptv.android.dev:attr/behavior_draggable = 0x7f04006e
com.iptv.android.dev:drawable/exo_ic_skip_next = 0x7f0800a3
com.iptv.android.dev:color/material_personalized_color_surface = 0x7f060290
com.iptv.android.dev:id/exo_rew = 0x7f0a00cb
com.iptv.android.dev:attr/cornerRadius = 0x7f040142
com.iptv.android.dev:string/language_dialog_title = 0x7f1000d3
com.iptv.android.dev:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f0701ad
com.iptv.android.dev:style/Widget.MaterialComponents.Chip.Filter = 0x7f110440
com.iptv.android.dev:dimen/m3_bottom_nav_min_height = 0x7f0700e4
com.iptv.android.dev:attr/cornerFamilyBottomLeft = 0x7f04013e
com.iptv.android.dev:attr/motionEasingLinear = 0x7f04031e
com.iptv.android.dev:drawable/exo_styled_controls_overflow_hide = 0x7f0800c7
com.iptv.android.dev:styleable/KeyCycle = 0x7f120040
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.Shuffle = 0x7f11013d
com.iptv.android.dev:drawable/abc_text_cursor_material = 0x7f08006d
com.iptv.android.dev:style/MaterialAlertDialog.Material3 = 0x7f110155
com.iptv.android.dev:attr/buttonCompat = 0x7f040092
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1100e7
com.iptv.android.dev:attr/endIconCheckable = 0x7f040196
com.iptv.android.dev:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702eb
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0e0076
com.iptv.android.dev:attr/cornerFamily = 0x7f04013d
com.iptv.android.dev:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f110492
com.iptv.android.dev:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1100e0
com.iptv.android.dev:attr/layout_constraintStart_toEndOf = 0x7f040284
com.iptv.android.dev:attr/use_controller = 0x7f0404a6
com.iptv.android.dev:attr/bottomNavigationStyle = 0x7f04007c
com.iptv.android.dev:attr/materialCalendarMonth = 0x7f0402d1
com.iptv.android.dev:styleable/ColorStateListItem = 0x7f120025
com.iptv.android.dev:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1100dd
com.iptv.android.dev:dimen/material_textinput_min_width = 0x7f07025e
com.iptv.android.dev:attr/contentPaddingRight = 0x7f040134
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f110178
com.iptv.android.dev:drawable/exo_edit_mode_logo = 0x7f080096
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1103de
com.iptv.android.dev:attr/contentPaddingBottom = 0x7f040131
com.iptv.android.dev:attr/clockIcon = 0x7f0400cf
com.iptv.android.dev:style/Widget.Material3.Snackbar.FullWidth = 0x7f110405
com.iptv.android.dev:attr/contentInsetEndWithActions = 0x7f04012b
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600cb
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0e00fc
com.iptv.android.dev:attr/collapsedTitleTextAppearance = 0x7f0400dd
com.iptv.android.dev:color/mtrl_fab_bg_color_selector = 0x7f0602c4
com.iptv.android.dev:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1100aa
com.iptv.android.dev:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f11000a
com.iptv.android.dev:dimen/mtrl_btn_corner_radius = 0x7f070275
com.iptv.android.dev:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f11004a
com.iptv.android.dev:attr/subMenuArrow = 0x7f0403ea
com.iptv.android.dev:attr/content = 0x7f040128
com.iptv.android.dev:drawable/abc_btn_colored_material = 0x7f08002f
com.iptv.android.dev:attr/badgeWithTextWidth = 0x7f040065
com.iptv.android.dev:id/icon = 0x7f0a00fb
com.iptv.android.dev:attr/behavior_fitToContents = 0x7f040070
com.iptv.android.dev:drawable/exo_styled_controls_subtitle_off = 0x7f0800d4
com.iptv.android.dev:attr/commitIcon = 0x7f040121
com.iptv.android.dev:color/m3_dynamic_dark_highlighted_text = 0x7f060095
com.iptv.android.dev:attr/animateMenuItems = 0x7f040032
com.iptv.android.dev:attr/currentState = 0x7f04014f
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Body1 = 0x7f110017
com.iptv.android.dev:drawable/ic_call_answer_low = 0x7f0800d9
com.iptv.android.dev:dimen/mtrl_progress_circular_size_extra_small = 0x7f0702f6
com.iptv.android.dev:attr/colorTertiaryContainer = 0x7f04011e
com.iptv.android.dev:attr/cardElevation = 0x7f04009f
com.iptv.android.dev:style/IPTVText.Body = 0x7f110151
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0e00ff
com.iptv.android.dev:attr/iconSize = 0x7f040221
com.iptv.android.dev:attr/textAppearanceHeadlineLarge = 0x7f040431
com.iptv.android.dev:dimen/mtrl_btn_inset = 0x7f07027e
com.iptv.android.dev:string/abc_activitychooserview_choose_application = 0x7f100005
com.iptv.android.dev:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701ea
com.iptv.android.dev:attr/materialCardViewOutlinedStyle = 0x7f0402d8
com.iptv.android.dev:style/Widget.Support.CoordinatorLayout = 0x7f110498
com.iptv.android.dev:macro/m3_comp_outlined_button_outline_color = 0x7f0e00a8
com.iptv.android.dev:attr/actionModeTheme = 0x7f04001e
com.iptv.android.dev:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f070338
com.iptv.android.dev:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f070165
com.iptv.android.dev:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.iptv.android.dev:attr/colorTertiaryFixed = 0x7f04011f
com.iptv.android.dev:style/Base.Theme.Material3.Dark.Dialog = 0x7f11005b
com.iptv.android.dev:id/material_hour_text_input = 0x7f0a0121
com.iptv.android.dev:dimen/m3_navigation_item_horizontal_padding = 0x7f0701dc
com.iptv.android.dev:color/brand_primary_light = 0x7f060026
com.iptv.android.dev:attr/colorOnContainerUnchecked = 0x7f0400f0
com.iptv.android.dev:attr/mock_diagonalsColor = 0x7f040301
com.iptv.android.dev:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f1101b7
com.iptv.android.dev:id/triangle = 0x7f0a01fa
com.iptv.android.dev:attr/listPreferredItemPaddingLeft = 0x7f0402b3
com.iptv.android.dev:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.iptv.android.dev:string/tab = 0x7f100198
com.iptv.android.dev:color/m3_ref_palette_secondary99 = 0x7f06014c
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600d1
com.iptv.android.dev:color/abc_decor_view_status_guard_light = 0x7f060006
com.iptv.android.dev:macro/m3_comp_dialog_supporting_text_type = 0x7f0e0028
com.iptv.android.dev:attr/colorPrimaryVariant = 0x7f04010c
com.iptv.android.dev:attr/splitTrack = 0x7f0403d0
com.iptv.android.dev:attr/colorPrimaryFixedDim = 0x7f040109
com.iptv.android.dev:anim/abc_tooltip_enter = 0x7f01000a
com.iptv.android.dev:attr/colorPrimaryFixed = 0x7f040108
com.iptv.android.dev:styleable/NavArgument = 0x7f120068
com.iptv.android.dev:attr/colorPrimaryContainer = 0x7f040106
com.iptv.android.dev:attr/forceDefaultNavigationOnClickListener = 0x7f0401fe
com.iptv.android.dev:color/material_deep_teal_500 = 0x7f060219
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1100cc
com.iptv.android.dev:attr/colorOnTertiaryFixed = 0x7f040101
com.iptv.android.dev:id/nav_controller_view_tag = 0x7f0a0152
com.iptv.android.dev:color/m3_ref_palette_primary90 = 0x7f06013d
com.iptv.android.dev:anim/abc_slide_in_bottom = 0x7f010006
com.iptv.android.dev:attr/colorOnTertiary = 0x7f0400ff
com.iptv.android.dev:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.iptv.android.dev:dimen/m3_fab_corner_size = 0x7f0701d3
com.iptv.android.dev:attr/textColorAlertDialogListItem = 0x7f040446
com.iptv.android.dev:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1101d5
com.iptv.android.dev:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.iptv.android.dev:attr/controlBackground = 0x7f040139
com.iptv.android.dev:drawable/exo_styled_controls_fullscreen_enter = 0x7f0800c4
com.iptv.android.dev:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0c000d
com.iptv.android.dev:dimen/m3_comp_fab_primary_icon_size = 0x7f07013e
com.iptv.android.dev:attr/actionBarWidgetTheme = 0x7f04000b
com.iptv.android.dev:layout/material_timepicker = 0x7f0d004a
com.iptv.android.dev:attr/colorOnSurface = 0x7f0400fc
com.iptv.android.dev:dimen/splashscreen_icon_size_with_background = 0x7f07033c
com.iptv.android.dev:attr/thumbStrokeWidth = 0x7f04045d
com.iptv.android.dev:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f110185
com.iptv.android.dev:attr/materialCalendarHeaderSelection = 0x7f0402ce
com.iptv.android.dev:attr/constraintSetEnd = 0x7f040124
com.iptv.android.dev:style/Widget.Material3.LinearProgressIndicator = 0x7f1103cb
com.iptv.android.dev:attr/colorOnTertiaryContainer = 0x7f040100
com.iptv.android.dev:style/ThemeOverlay.Material3.Search = 0x7f1102ee
com.iptv.android.dev:string/date_input_headline = 0x7f100063
com.iptv.android.dev:integer/material_motion_path = 0x7f0b002f
com.iptv.android.dev:id/action_mode_close_button = 0x7f0a0040
com.iptv.android.dev:drawable/abc_list_pressed_holo_light = 0x7f080051
com.iptv.android.dev:dimen/m3_comp_navigation_rail_container_width = 0x7f07016d
com.iptv.android.dev:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0e0104
com.iptv.android.dev:attr/colorOnPrimaryFixed = 0x7f0400f5
com.iptv.android.dev:style/Theme.AppCompat.Light.NoActionBar = 0x7f110257
com.iptv.android.dev:dimen/mtrl_tooltip_minWidth = 0x7f070324
com.iptv.android.dev:color/abc_search_url_text = 0x7f06000d
com.iptv.android.dev:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.iptv.android.dev:string/mtrl_timepicker_cancel = 0x7f100141
com.iptv.android.dev:color/abc_secondary_text_material_light = 0x7f060012
com.iptv.android.dev:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f110241
com.iptv.android.dev:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1101c1
com.iptv.android.dev:color/design_default_color_secondary_variant = 0x7f060053
com.iptv.android.dev:attr/chipStrokeWidth = 0x7f0400c7
com.iptv.android.dev:attr/tabStyle = 0x7f040417
com.iptv.android.dev:style/Widget.MaterialComponents.TextView = 0x7f110488
com.iptv.android.dev:drawable/mtrl_switch_thumb_unchecked = 0x7f080123
com.iptv.android.dev:attr/colorOnSecondaryContainer = 0x7f0400f9
com.iptv.android.dev:attr/endIconDrawable = 0x7f040198
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f06019e
com.iptv.android.dev:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f110350
com.iptv.android.dev:attr/contentScrim = 0x7f040137
com.iptv.android.dev:attr/layout_scrollEffect = 0x7f04029d
com.iptv.android.dev:attr/collapsingToolbarLayoutStyle = 0x7f0400e3
com.iptv.android.dev:attr/itemHorizontalPadding = 0x7f040237
com.iptv.android.dev:string/material_motion_easing_linear = 0x7f1000f9
com.iptv.android.dev:attr/clockNumberTextColor = 0x7f0400d0
com.iptv.android.dev:style/Widget.Material3.Badge.AdjustToBounds = 0x7f11037f
com.iptv.android.dev:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f11008d
com.iptv.android.dev:drawable/ic_m3_chip_checked_circle = 0x7f0800e3
com.iptv.android.dev:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f07018e
com.iptv.android.dev:style/Base.Widget.AppCompat.Button.Small = 0x7f1100d5
com.iptv.android.dev:id/action_mode_bar_stub = 0x7f0a003f
com.iptv.android.dev:attr/show_rewind_button = 0x7f0403b6
com.iptv.android.dev:drawable/ic_arrow_back_black_24 = 0x7f0800d7
com.iptv.android.dev:attr/showAnimationBehavior = 0x7f0403aa
com.iptv.android.dev:attr/bar_gravity = 0x7f040067
com.iptv.android.dev:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1102d8
com.iptv.android.dev:attr/colorControlHighlight = 0x7f0400ea
com.iptv.android.dev:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f080124
com.iptv.android.dev:color/focus_background = 0x7f06006e
com.iptv.android.dev:color/design_dark_default_color_surface = 0x7f060047
com.iptv.android.dev:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0e016d
com.iptv.android.dev:attr/curveFit = 0x7f040152
com.iptv.android.dev:dimen/mtrl_btn_icon_btn_padding_left = 0x7f07027c
com.iptv.android.dev:id/deltaRelative = 0x7f0a0085
com.iptv.android.dev:attr/colorControlActivated = 0x7f0400e9
com.iptv.android.dev:color/design_default_color_primary_dark = 0x7f060050
com.iptv.android.dev:dimen/mtrl_btn_focused_z = 0x7f07027a
com.iptv.android.dev:attr/textColorSearchUrl = 0x7f040447
com.iptv.android.dev:attr/selectableItemBackground = 0x7f040399
com.iptv.android.dev:attr/fontProviderQuery = 0x7f0401f8
com.iptv.android.dev:color/material_personalized_color_surface_container_low = 0x7f060295
com.iptv.android.dev:attr/badgeTextAppearance = 0x7f04005c
com.iptv.android.dev:string/searchview_navigation_content_description = 0x7f100174
com.iptv.android.dev:attr/popUpToInclusive = 0x7f040367
com.iptv.android.dev:attr/colorOnBackground = 0x7f0400ee
com.iptv.android.dev:string/off = 0x7f100151
com.iptv.android.dev:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f07015c
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f070130
com.iptv.android.dev:color/accent_material_light = 0x7f06001a
com.iptv.android.dev:styleable/KeyFrame = 0x7f120041
com.iptv.android.dev:id/material_clock_face = 0x7f0a011b
com.iptv.android.dev:attr/initialActivityCount = 0x7f04022e
com.iptv.android.dev:dimen/abc_action_bar_elevation_material = 0x7f070005
com.iptv.android.dev:attr/colorButtonNormal = 0x7f0400e7
com.iptv.android.dev:attr/drawableStartCompat = 0x7f040181
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600d0
com.iptv.android.dev:style/Base.Widget.MaterialComponents.Chip = 0x7f110116
com.iptv.android.dev:attr/colorBackgroundFloating = 0x7f0400e6
com.iptv.android.dev:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0e010b
com.iptv.android.dev:drawable/ic_mtrl_checked_circle = 0x7f0800e5
com.iptv.android.dev:dimen/m3_carousel_small_item_size_min = 0x7f070115
com.iptv.android.dev:color/material_dynamic_primary100 = 0x7f060237
com.iptv.android.dev:color/m3_ref_palette_error30 = 0x7f060105
com.iptv.android.dev:dimen/m3_navigation_rail_icon_size = 0x7f0701e7
com.iptv.android.dev:attr/stackFromEnd = 0x7f0403d2
com.iptv.android.dev:attr/colorAccent = 0x7f0400e5
com.iptv.android.dev:styleable/NavGraphNavigator = 0x7f12006a
com.iptv.android.dev:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701d8
com.iptv.android.dev:layout/notification_media_cancel_action = 0x7f0d0070
com.iptv.android.dev:attr/titleMargins = 0x7f040477
com.iptv.android.dev:color/background_material_dark = 0x7f060020
com.iptv.android.dev:attr/behavior_peekHeight = 0x7f040074
com.iptv.android.dev:dimen/mtrl_progress_circular_size_medium = 0x7f0702f7
com.iptv.android.dev:styleable/DrawerArrowToggle = 0x7f12002f
com.iptv.android.dev:color/material_personalized_color_surface_inverse = 0x7f060298
com.iptv.android.dev:animator/fragment_open_enter = 0x7f020007
com.iptv.android.dev:attr/layout_editor_absoluteX = 0x7f040292
com.iptv.android.dev:attr/layout_constraintWidth_max = 0x7f04028e
com.iptv.android.dev:color/mtrl_textinput_focused_box_stroke_color = 0x7f0602e5
com.iptv.android.dev:attr/touchAnchorId = 0x7f040487
com.iptv.android.dev:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1100fc
com.iptv.android.dev:attr/isMaterial3DynamicColorApplied = 0x7f040231
com.iptv.android.dev:dimen/mtrl_chip_text_size = 0x7f0702be
com.iptv.android.dev:animator/m3_appbar_state_list_animator = 0x7f020009
com.iptv.android.dev:attr/voiceIcon = 0x7f0404ac
com.iptv.android.dev:attr/show_fastforward_button = 0x7f0403b3
com.iptv.android.dev:attr/bottomSheetDialogTheme = 0x7f04007d
com.iptv.android.dev:id/ALT = 0x7f0a0000
com.iptv.android.dev:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401d3
com.iptv.android.dev:drawable/abc_btn_check_material_anim = 0x7f08002c
com.iptv.android.dev:color/m3_sys_color_light_on_error = 0x7f0601d4
com.iptv.android.dev:attr/closeIconStartPadding = 0x7f0400d5
com.iptv.android.dev:color/abc_color_highlight_material = 0x7f060004
com.iptv.android.dev:attr/endIconTintMode = 0x7f04019d
com.iptv.android.dev:attr/closeIconEnabled = 0x7f0400d2
com.iptv.android.dev:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f110092
com.iptv.android.dev:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08002d
com.iptv.android.dev:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700bf
com.iptv.android.dev:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1102da
com.iptv.android.dev:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.iptv.android.dev:attr/closeIcon = 0x7f0400d1
com.iptv.android.dev:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f070272
com.iptv.android.dev:dimen/m3_comp_search_bar_container_height = 0x7f070191
com.iptv.android.dev:attr/chipIconEnabled = 0x7f0400bb
com.iptv.android.dev:style/Base.Widget.AppCompat.SeekBar = 0x7f1100f7
com.iptv.android.dev:drawable/mtrl_dialog_background = 0x7f08010f
com.iptv.android.dev:attr/colorSecondaryFixedDim = 0x7f040110
com.iptv.android.dev:dimen/material_divider_thickness = 0x7f07024d
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f110458
com.iptv.android.dev:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f11006a
com.iptv.android.dev:attr/dividerColor = 0x7f040171
com.iptv.android.dev:string/bottom_sheet_expand_description = 0x7f100038
com.iptv.android.dev:color/material_timepicker_button_background = 0x7f0602ab
com.iptv.android.dev:id/tag_on_apply_window_listener = 0x7f0a01d1
com.iptv.android.dev:attr/materialCalendarFullscreenTheme = 0x7f0402c9
com.iptv.android.dev:style/ExoStyledControls.Button.Bottom.VR = 0x7f11013e
com.iptv.android.dev:attr/searchIcon = 0x7f040395
com.iptv.android.dev:dimen/m3_carousel_small_item_default_corner_size = 0x7f070113
com.iptv.android.dev:attr/chipStrokeColor = 0x7f0400c6
com.iptv.android.dev:attr/chipSpacingVertical = 0x7f0400c3
com.iptv.android.dev:dimen/tooltip_horizontal_padding = 0x7f07033e
com.iptv.android.dev:attr/floatingActionButtonPrimaryStyle = 0x7f0401d4
com.iptv.android.dev:color/m3_ref_palette_neutral0 = 0x7f06010e
com.iptv.android.dev:id/shortcut = 0x7f0a01a8
com.iptv.android.dev:attr/actionProviderClass = 0x7f040022
com.iptv.android.dev:attr/buttonBarNeutralButtonStyle = 0x7f04008f
com.iptv.android.dev:id/always = 0x7f0a0048
com.iptv.android.dev:attr/chipSpacingHorizontal = 0x7f0400c2
com.iptv.android.dev:color/m3_ref_palette_secondary70 = 0x7f060148
com.iptv.android.dev:attr/chipMinTouchTargetSize = 0x7f0400c0
com.iptv.android.dev:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f11007e
com.iptv.android.dev:attr/chipIconTint = 0x7f0400bd
com.iptv.android.dev:dimen/m3_side_sheet_margin_detached = 0x7f070200
com.iptv.android.dev:color/dim_foreground_disabled_material_light = 0x7f060060
com.iptv.android.dev:attr/cardBackgroundColor = 0x7f04009d
com.iptv.android.dev:attr/clockHandColor = 0x7f0400ce
com.iptv.android.dev:color/design_default_color_on_error = 0x7f06004b
com.iptv.android.dev:dimen/design_snackbar_padding_vertical_2lines = 0x7f070087
com.iptv.android.dev:color/m3_ref_palette_neutral94 = 0x7f060121
com.iptv.android.dev:attr/layout_constraintVertical_chainStyle = 0x7f04028b
com.iptv.android.dev:attr/chipCornerRadius = 0x7f0400b7
com.iptv.android.dev:dimen/abc_text_size_display_1_material = 0x7f070043
com.iptv.android.dev:id/mtrl_calendar_day_selector_frame = 0x7f0a013b
com.iptv.android.dev:color/m3_sys_color_dark_surface_variant = 0x7f060182
com.iptv.android.dev:string/mtrl_switch_thumb_path_unchecked = 0x7f10013e
com.iptv.android.dev:id/material_label = 0x7f0a0123
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600bb
com.iptv.android.dev:attr/textAppearanceDisplayLarge = 0x7f040428
com.iptv.android.dev:color/material_personalized_color_control_normal = 0x7f060276
com.iptv.android.dev:attr/verticalOffset = 0x7f0404a8
com.iptv.android.dev:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f080122
com.iptv.android.dev:attr/auto_show = 0x7f040048
com.iptv.android.dev:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1100c2
com.iptv.android.dev:macro/m3_comp_fab_secondary_container_color = 0x7f0e003c
com.iptv.android.dev:attr/checkedIconGravity = 0x7f0400af
com.iptv.android.dev:attr/indicatorSize = 0x7f04022d
com.iptv.android.dev:attr/shapeCornerFamily = 0x7f0403a7
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600d7
com.iptv.android.dev:attr/expandActivityOverflowButtonDrawable = 0x7f0401ad
com.iptv.android.dev:attr/itemTextAppearanceActiveBoldEnabled = 0x7f04024e
com.iptv.android.dev:attr/circleRadius = 0x7f0400ca
com.iptv.android.dev:drawable/mtrl_checkbox_button_icon = 0x7f080107
com.iptv.android.dev:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f070192
com.iptv.android.dev:attr/animationMode = 0x7f040035
com.iptv.android.dev:macro/m3_comp_slider_label_label_text_color = 0x7f0e0113
com.iptv.android.dev:color/m3_ref_palette_neutral6 = 0x7f06011a
com.iptv.android.dev:style/Theme.MaterialComponents.Light.Bridge = 0x7f11029c
com.iptv.android.dev:color/m3_assist_chip_icon_tint_color = 0x7f060078
com.iptv.android.dev:style/Base.Widget.AppCompat.Toolbar = 0x7f1100fd
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f110035
com.iptv.android.dev:attr/shapeAppearanceCornerExtraLarge = 0x7f04039e
com.iptv.android.dev:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f080019
com.iptv.android.dev:style/Base.Theme.SplashScreen = 0x7f110078
com.iptv.android.dev:attr/paddingStart = 0x7f040349
com.iptv.android.dev:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1102de
com.iptv.android.dev:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0e001d
com.iptv.android.dev:attr/actionBarItemBackground = 0x7f040002
com.iptv.android.dev:attr/cardViewStyle = 0x7f0400a4
com.iptv.android.dev:attr/collapseContentDescription = 0x7f0400d9
com.iptv.android.dev:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701c6
com.iptv.android.dev:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f100135
com.iptv.android.dev:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0e011c
com.iptv.android.dev:dimen/design_tab_text_size = 0x7f07008b
com.iptv.android.dev:attr/chipIconSize = 0x7f0400bc
com.iptv.android.dev:attr/itemTextAppearanceInactive = 0x7f04024f
com.iptv.android.dev:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f070179
com.iptv.android.dev:layout/design_layout_tab_icon = 0x7f0d0021
com.iptv.android.dev:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.iptv.android.dev:id/info = 0x7f0a0102
com.iptv.android.dev:color/m3_ref_palette_secondary0 = 0x7f060140
com.iptv.android.dev:attr/tabPaddingBottom = 0x7f04040f
com.iptv.android.dev:attr/bottomInsetScrimEnabled = 0x7f04007b
com.iptv.android.dev:attr/itemIconPadding = 0x7f040239
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f110312
com.iptv.android.dev:color/text_tertiary = 0x7f060313
com.iptv.android.dev:layout/abc_search_view = 0x7f0d0019
com.iptv.android.dev:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f07011e
com.iptv.android.dev:style/Base.Widget.AppCompat.ImageButton = 0x7f1100df
com.iptv.android.dev:integer/m3_sys_shape_corner_small_corner_family = 0x7f0b0028
com.iptv.android.dev:attr/colorErrorContainer = 0x7f0400ed
com.iptv.android.dev:attr/tickMark = 0x7f040464
com.iptv.android.dev:dimen/mtrl_btn_padding_right = 0x7f070283
com.iptv.android.dev:color/secondary_text_default_material_dark = 0x7f060301
com.iptv.android.dev:string/date_picker_today_description = 0x7f100078
com.iptv.android.dev:dimen/mtrl_btn_padding_top = 0x7f070284
com.iptv.android.dev:attr/buttonPanelSideLayout = 0x7f040098
com.iptv.android.dev:attr/buttonIconDimen = 0x7f040095
com.iptv.android.dev:attr/actionModePopupWindowStyle = 0x7f040019
com.iptv.android.dev:styleable/NavInclude = 0x7f12006c
com.iptv.android.dev:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0800ff
com.iptv.android.dev:dimen/design_bottom_navigation_active_item_max_width = 0x7f07005f
com.iptv.android.dev:color/m3_ref_palette_primary80 = 0x7f06013c
com.iptv.android.dev:attr/colorSecondaryContainer = 0x7f04010e
com.iptv.android.dev:attr/buttonBarPositiveButtonStyle = 0x7f040090
com.iptv.android.dev:attr/animate_relativeTo = 0x7f040034
com.iptv.android.dev:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.iptv.android.dev:color/m3_text_button_background_color_selector = 0x7f060201
com.iptv.android.dev:style/Widget.Material3.Button.OutlinedButton = 0x7f110390
com.iptv.android.dev:attr/actionModeCloseDrawable = 0x7f040014
com.iptv.android.dev:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0e00cb
com.iptv.android.dev:integer/m3_sys_motion_duration_medium1 = 0x7f0b001a
com.iptv.android.dev:attr/touchAnchorSide = 0x7f040488
com.iptv.android.dev:style/IPTVText = 0x7f110150
com.iptv.android.dev:macro/m3_comp_dialog_container_shape = 0x7f0e0024
com.iptv.android.dev:attr/boxStrokeColor = 0x7f040087
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f110290
com.iptv.android.dev:attr/tickColorActive = 0x7f040462
com.iptv.android.dev:styleable/NavigationBarActiveIndicator = 0x7f12006d
com.iptv.android.dev:color/material_personalized_primary_inverse_text_disable_only = 0x7f0602a3
com.iptv.android.dev:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1102db
com.iptv.android.dev:attr/thumbTextPadding = 0x7f04045e
com.iptv.android.dev:integer/m3_sys_motion_duration_extra_long1 = 0x7f0b0012
com.iptv.android.dev:attr/layout_constraintRight_toLeftOf = 0x7f040282
com.iptv.android.dev:style/ShapeAppearance.MaterialComponents = 0x7f1101ad
com.iptv.android.dev:attr/backHandlingEnabled = 0x7f040049
com.iptv.android.dev:attr/materialSearchViewStyle = 0x7f0402e5
com.iptv.android.dev:attr/splashScreenIconSize = 0x7f0403cf
com.iptv.android.dev:attr/flow_padding = 0x7f0401eb
com.iptv.android.dev:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f110462
com.iptv.android.dev:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.iptv.android.dev:attr/fabCustomSize = 0x7f0401c7
com.iptv.android.dev:drawable/exo_ic_check = 0x7f080098
com.iptv.android.dev:attr/data = 0x7f04015c
com.iptv.android.dev:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700d0
com.iptv.android.dev:attr/dragThreshold = 0x7f04017a
com.iptv.android.dev:attr/allowStacking = 0x7f04002e
com.iptv.android.dev:dimen/design_navigation_max_width = 0x7f07007b
com.iptv.android.dev:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f070135
com.iptv.android.dev:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f110357
com.iptv.android.dev:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f110192
com.iptv.android.dev:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f07028d
com.iptv.android.dev:dimen/mtrl_calendar_action_height = 0x7f07028e
com.iptv.android.dev:attr/boxCollapsedPaddingTop = 0x7f040082
com.iptv.android.dev:id/transition_current_scene = 0x7f0a01f5
com.iptv.android.dev:drawable/abc_item_background_holo_dark = 0x7f08004a
com.iptv.android.dev:dimen/design_textinput_caption_translate_y = 0x7f07008d
com.iptv.android.dev:attr/chipGroupStyle = 0x7f0400b9
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110163
com.iptv.android.dev:color/mtrl_indicator_text_color = 0x7f0602ca
com.iptv.android.dev:string/settings_video_quality = 0x7f10018f
com.iptv.android.dev:attr/thumbIconTintMode = 0x7f04045a
com.iptv.android.dev:color/m3_ref_palette_neutral40 = 0x7f060118
com.iptv.android.dev:attr/bottomSheetStyle = 0x7f04007f
com.iptv.android.dev:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f110286
com.iptv.android.dev:color/accent_material_dark = 0x7f060019
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f110304
com.iptv.android.dev:id/exo_rew_with_amount = 0x7f0a00cc
com.iptv.android.dev:animator/mtrl_btn_state_list_anim = 0x7f020015
com.iptv.android.dev:attr/bottomSheetDragHandleStyle = 0x7f04007e
com.iptv.android.dev:drawable/mtrl_ic_arrow_drop_down = 0x7f080111
com.iptv.android.dev:drawable/material_cursor_drawable = 0x7f0800fa
com.iptv.android.dev:style/Widget.Material3.Badge = 0x7f11037e
com.iptv.android.dev:string/time_picker_hour_selection = 0x7f1001a0
com.iptv.android.dev:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.iptv.android.dev:attr/checkedIconTint = 0x7f0400b2
com.iptv.android.dev:attr/textAppearanceDisplaySmall = 0x7f04042a
com.iptv.android.dev:id/progress_horizontal = 0x7f0a0185
com.iptv.android.dev:attr/borderlessButtonStyle = 0x7f040079
com.iptv.android.dev:attr/expandedTitleMarginStart = 0x7f0401b4
com.iptv.android.dev:attr/controller_layout_id = 0x7f04013a
com.iptv.android.dev:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f07013d
com.iptv.android.dev:color/m3_sys_color_light_outline_variant = 0x7f0601df
com.iptv.android.dev:style/TextAppearance.Material3.LabelMedium = 0x7f110226
com.iptv.android.dev:attr/motionDurationExtraLong1 = 0x7f040308
com.iptv.android.dev:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1103bf
com.iptv.android.dev:attr/constraint_referenced_ids = 0x7f040126
com.iptv.android.dev:attr/borderWidth = 0x7f040078
com.iptv.android.dev:attr/titleEnabled = 0x7f040471
com.iptv.android.dev:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1103f7
com.iptv.android.dev:attr/subheaderInsetStart = 0x7f0403ed
com.iptv.android.dev:id/textSpacerNoTitle = 0x7f0a01de
com.iptv.android.dev:id/normal = 0x7f0a015d
com.iptv.android.dev:attr/behavior_skipCollapsed = 0x7f040077
com.iptv.android.dev:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0702a4
com.iptv.android.dev:attr/barrierMargin = 0x7f04006b
com.iptv.android.dev:attr/behavior_hideable = 0x7f040072
com.iptv.android.dev:id/animateToStart = 0x7f0a004b
com.iptv.android.dev:attr/layout_constraintWidth_default = 0x7f04028d
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f110301
com.iptv.android.dev:color/abc_search_url_text_normal = 0x7f06000e
com.iptv.android.dev:color/design_snackbar_background_color = 0x7f06005e
com.iptv.android.dev:style/Widget.MaterialComponents.ActionMode = 0x7f11041e
com.iptv.android.dev:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601bd
com.iptv.android.dev:attr/colorOnSecondaryFixedVariant = 0x7f0400fb
com.iptv.android.dev:string/mtrl_picker_text_input_date_hint = 0x7f10012e
com.iptv.android.dev:attr/minTouchTargetSize = 0x7f0402ff
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f110345
com.iptv.android.dev:dimen/material_emphasis_disabled_background = 0x7f07024f
com.iptv.android.dev:attr/itemPadding = 0x7f04023e
com.iptv.android.dev:style/TextAppearance.Material3.HeadlineSmall = 0x7f110224
com.iptv.android.dev:attr/marginTopSystemWindowInsets = 0x7f0402bd
com.iptv.android.dev:color/abc_tint_edittext = 0x7f060015
com.iptv.android.dev:attr/deltaPolarAngle = 0x7f040168
com.iptv.android.dev:color/material_on_background_emphasis_high_type = 0x7f060268
com.iptv.android.dev:attr/chipSpacing = 0x7f0400c1
com.iptv.android.dev:color/m3_ref_palette_secondary100 = 0x7f060142
com.iptv.android.dev:attr/badgeWithTextShapeAppearanceOverlay = 0x7f040064
com.iptv.android.dev:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.iptv.android.dev:dimen/mtrl_progress_circular_size_small = 0x7f0702f8
com.iptv.android.dev:dimen/mtrl_tooltip_cornerSize = 0x7f070322
com.iptv.android.dev:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0e0050
com.iptv.android.dev:attr/show_vr_button = 0x7f0403ba
com.iptv.android.dev:attr/theme = 0x7f040453
com.iptv.android.dev:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1103e1
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f060188
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f11031d
com.iptv.android.dev:drawable/mtrl_switch_track = 0x7f080126
com.iptv.android.dev:attr/badgeVerticalPadding = 0x7f04005e
com.iptv.android.dev:animator/design_fab_hide_motion_spec = 0x7f020001
com.iptv.android.dev:style/Widget.MaterialComponents.Button.Icon = 0x7f110430
com.iptv.android.dev:string/action_edit = 0x7f10001e
com.iptv.android.dev:id/material_timepicker_ok_button = 0x7f0a012a
com.iptv.android.dev:attr/autoShowKeyboard = 0x7f040041
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600c8
com.iptv.android.dev:string/material_slider_range_end = 0x7f1000fb
com.iptv.android.dev:integer/material_motion_duration_long_1 = 0x7f0b0029
com.iptv.android.dev:color/m3_radiobutton_button_tint = 0x7f0600b2
com.iptv.android.dev:array/exo_controls_playback_speeds = 0x7f030000
com.iptv.android.dev:attr/chipSurfaceColor = 0x7f0400c9
com.iptv.android.dev:attr/layout_constraintRight_creator = 0x7f040281
com.iptv.android.dev:attr/expandedTitleGravity = 0x7f0401b0
com.iptv.android.dev:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0601c0
com.iptv.android.dev:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1101dd
com.iptv.android.dev:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.iptv.android.dev:string/abc_menu_space_shortcut_label = 0x7f10000f
com.iptv.android.dev:attr/backgroundInsetStart = 0x7f04004e
com.iptv.android.dev:dimen/m3_btn_icon_only_icon_padding = 0x7f0700f8
com.iptv.android.dev:id/add = 0x7f0a0044
com.iptv.android.dev:attr/backgroundInsetEnd = 0x7f04004d
com.iptv.android.dev:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0e0021
com.iptv.android.dev:id/accessibility_custom_action_28 = 0x7f0a0025
com.iptv.android.dev:attr/startIconMinSize = 0x7f0403d8
com.iptv.android.dev:dimen/notification_small_icon_background_padding = 0x7f070331
com.iptv.android.dev:attr/checkedIconSize = 0x7f0400b1
com.iptv.android.dev:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.iptv.android.dev:attr/motionEasingEmphasized = 0x7f04031a
com.iptv.android.dev:attr/startIconTint = 0x7f0403da
com.iptv.android.dev:attr/badgeHeight = 0x7f040056
com.iptv.android.dev:animator/fragment_fade_exit = 0x7f020006
com.iptv.android.dev:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.iptv.android.dev:attr/badgeTextColor = 0x7f04005d
com.iptv.android.dev:attr/artwork_display_mode = 0x7f04003d
com.iptv.android.dev:attr/navigationContentDescription = 0x7f04032f
com.iptv.android.dev:attr/layout_constraintDimensionRatio = 0x7f040271
com.iptv.android.dev:macro/m3_comp_navigation_rail_label_text_type = 0x7f0e00a1
com.iptv.android.dev:dimen/m3_comp_assist_chip_container_height = 0x7f07011d
com.iptv.android.dev:attr/actionModeCloseContentDescription = 0x7f040013
com.iptv.android.dev:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1101da
com.iptv.android.dev:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070239
com.iptv.android.dev:attr/dividerPadding = 0x7f040175
com.iptv.android.dev:attr/autoSizeMaxTextSize = 0x7f040042
com.iptv.android.dev:color/abc_tint_switch_track = 0x7f060018
com.iptv.android.dev:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0600fa
com.iptv.android.dev:attr/cornerFamilyTopLeft = 0x7f040140
com.iptv.android.dev:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1100a2
com.iptv.android.dev:string/mtrl_picker_invalid_format_example = 0x7f100121
com.iptv.android.dev:drawable/$m3_avd_hide_password__2 = 0x7f080008
com.iptv.android.dev:attr/contentPaddingTop = 0x7f040136
com.iptv.android.dev:attr/itemShapeInsetBottom = 0x7f040245
com.iptv.android.dev:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f110346
com.iptv.android.dev:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.iptv.android.dev:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f11035a
com.iptv.android.dev:dimen/m3_btn_translation_z_base = 0x7f070105
com.iptv.android.dev:attr/contentInsetLeft = 0x7f04012c
com.iptv.android.dev:attr/enforceMaterialTheme = 0x7f04019e
com.iptv.android.dev:attr/animation_enabled = 0x7f040036
com.iptv.android.dev:styleable/ShapeAppearance = 0x7f120081
com.iptv.android.dev:dimen/abc_button_inset_vertical_material = 0x7f070013
com.iptv.android.dev:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0e00e9
com.iptv.android.dev:attr/layout_constraintTop_toTopOf = 0x7f040289
com.iptv.android.dev:style/TextAppearance.MaterialComponents.Headline2 = 0x7f110236
com.iptv.android.dev:integer/m3_card_anim_delay_ms = 0x7f0b000f
com.iptv.android.dev:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.iptv.android.dev:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0e0126
com.iptv.android.dev:attr/materialCardViewStyle = 0x7f0402d9
com.iptv.android.dev:attr/alphabeticModifiers = 0x7f040030
com.iptv.android.dev:string/exo_controls_seek_bar_description = 0x7f1000a5
com.iptv.android.dev:attr/navigationIconTint = 0x7f040331
com.iptv.android.dev:color/material_blue_grey_800 = 0x7f060214
com.iptv.android.dev:dimen/m3_comp_switch_track_width = 0x7f0701b5
com.iptv.android.dev:attr/scrubber_drawable = 0x7f040392
com.iptv.android.dev:drawable/exo_ic_rewind = 0x7f0800a1
com.iptv.android.dev:attr/horizontalOffset = 0x7f04021a
com.iptv.android.dev:layout/mtrl_calendar_vertical = 0x7f0d005c
com.iptv.android.dev:attr/addElevationShadow = 0x7f040029
com.iptv.android.dev:attr/iconStartPadding = 0x7f040222
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f060199
com.iptv.android.dev:anim/design_snackbar_out = 0x7f01001b
com.iptv.android.dev:attr/closeIconEndPadding = 0x7f0400d3
com.iptv.android.dev:attr/ad_marker_width = 0x7f040028
com.iptv.android.dev:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080027
com.iptv.android.dev:attr/expandedTitleMarginBottom = 0x7f0401b2
com.iptv.android.dev:attr/motionDurationMedium3 = 0x7f040312
com.iptv.android.dev:attr/iconTint = 0x7f040223
com.iptv.android.dev:string/cd_pause_button = 0x7f10004b
com.iptv.android.dev:dimen/m3_comp_outlined_card_outline_width = 0x7f070178
com.iptv.android.dev:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f1101b0
com.iptv.android.dev:attr/autoCompleteTextViewStyle = 0x7f040040
com.iptv.android.dev:color/success = 0x7f060306
com.iptv.android.dev:color/design_default_color_primary_variant = 0x7f060051
com.iptv.android.dev:attr/itemShapeFillColor = 0x7f040244
com.iptv.android.dev:attr/cornerFamilyTopRight = 0x7f040141
com.iptv.android.dev:id/startToEnd = 0x7f0a01c2
com.iptv.android.dev:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.iptv.android.dev:attr/actionTextColorAlpha = 0x7f040023
com.iptv.android.dev:attr/boxCornerRadiusTopEnd = 0x7f040085
com.iptv.android.dev:dimen/abc_list_item_height_large_material = 0x7f070030
com.iptv.android.dev:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0c000c
com.iptv.android.dev:attr/onShow = 0x7f040340
com.iptv.android.dev:id/surface_view = 0x7f0a01cb
com.iptv.android.dev:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f060187
com.iptv.android.dev:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.iptv.android.dev:anim/abc_fade_out = 0x7f010001
com.iptv.android.dev:color/foreground_material_dark = 0x7f060070
com.iptv.android.dev:color/vector_tint_color = 0x7f060317
com.iptv.android.dev:attr/cornerSizeTopRight = 0x7f040147
com.iptv.android.dev:attr/actionLayout = 0x7f04000e
com.iptv.android.dev:style/Theme.MaterialComponents = 0x7f11027d
com.iptv.android.dev:layout/notification_template_big_media_narrow_custom = 0x7f0d0074
com.iptv.android.dev:attr/simpleItems = 0x7f0403c2
com.iptv.android.dev:attr/titleTextAppearance = 0x7f040479
com.iptv.android.dev:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f110119
com.iptv.android.dev:dimen/highlight_alpha_material_dark = 0x7f0700b7
com.iptv.android.dev:color/material_dynamic_secondary70 = 0x7f06024a
com.iptv.android.dev:attr/subtitleTextStyle = 0x7f0403f4
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f11003e
com.iptv.android.dev:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0e012c
com.iptv.android.dev:color/m3_ref_palette_neutral4 = 0x7f060117
com.iptv.android.dev:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0e0107
com.iptv.android.dev:attr/prefixTextColor = 0x7f040370
com.iptv.android.dev:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.iptv.android.dev:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.iptv.android.dev:attr/prefixTextAppearance = 0x7f04036f
com.iptv.android.dev:dimen/compat_button_padding_horizontal_material = 0x7f070058
com.iptv.android.dev:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1103b0
com.iptv.android.dev:macro/m3_comp_navigation_bar_container_color = 0x7f0e006d
com.iptv.android.dev:attr/autoSizePresetSizes = 0x7f040044
com.iptv.android.dev:attr/actionModeStyle = 0x7f04001d
com.iptv.android.dev:attr/constraints = 0x7f040127
com.iptv.android.dev:dimen/m3_comp_badge_large_size = 0x7f070122
com.iptv.android.dev:attr/brightness = 0x7f04008b
com.iptv.android.dev:style/Base.Widget.AppCompat.PopupWindow = 0x7f1100ef
com.iptv.android.dev:attr/queryBackground = 0x7f040375
com.iptv.android.dev:dimen/m3_btn_icon_only_default_size = 0x7f0700f7
com.iptv.android.dev:color/m3_ref_palette_primary20 = 0x7f060136
com.iptv.android.dev:drawable/exo_controls_vr = 0x7f080095
com.iptv.android.dev:style/Theme.MaterialComponents.Bridge = 0x7f11027f
com.iptv.android.dev:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f0701b1
com.iptv.android.dev:color/m3_sys_color_dark_primary_container = 0x7f060177
com.iptv.android.dev:attr/hintTextColor = 0x7f040217
com.iptv.android.dev:attr/actionMenuTextColor = 0x7f040010
com.iptv.android.dev:layout/abc_alert_dialog_button_bar_material = 0x7f0d0008
com.iptv.android.dev:attr/popUpToSaveState = 0x7f040368
com.iptv.android.dev:styleable/StyledPlayerControlView = 0x7f12008c
com.iptv.android.dev:attr/motionDurationExtraLong2 = 0x7f040309
com.iptv.android.dev:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0e0071
com.iptv.android.dev:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.iptv.android.dev:animator/fragment_close_exit = 0x7f020004
com.iptv.android.dev:attr/colorSurfaceContainerHighest = 0x7f040116
com.iptv.android.dev:string/app_name = 0x7f100024
com.iptv.android.dev:attr/dragDirection = 0x7f040178
com.iptv.android.dev:id/media_controller_compat_view_tag = 0x7f0a012f
com.iptv.android.dev:attr/alertDialogStyle = 0x7f04002c
com.iptv.android.dev:attr/shortcutMatchRequired = 0x7f0403a8
com.iptv.android.dev:attr/listDividerAlertDialog = 0x7f0402aa
com.iptv.android.dev:attr/actionBarStyle = 0x7f040006
com.iptv.android.dev:string/nav_settings = 0x7f10014e
com.iptv.android.dev:animator/design_fab_show_motion_spec = 0x7f020002
com.iptv.android.dev:string/search_no_results_title = 0x7f10016d
com.iptv.android.dev:attr/colorSurfaceContainerLowest = 0x7f040118
com.iptv.android.dev:id/accessibility_custom_action_11 = 0x7f0a0013
com.iptv.android.dev:color/m3_sys_color_dark_inverse_on_surface = 0x7f060166
com.iptv.android.dev:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.iptv.android.dev:attr/tickMarkTintMode = 0x7f040466
com.iptv.android.dev:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1100c8
com.iptv.android.dev:attr/counterOverflowTextAppearance = 0x7f04014a
com.iptv.android.dev:string/player_placeholder_message = 0x7f10015e
com.iptv.android.dev:color/m3_dark_default_color_secondary_text = 0x7f06008d
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents = 0x7f1102f9
com.iptv.android.dev:attr/expandedTitleTextColor = 0x7f0401b7
com.iptv.android.dev:attr/buttonIconTintMode = 0x7f040097
com.iptv.android.dev:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600c9
com.iptv.android.dev:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f110095
com.iptv.android.dev:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.iptv.android.dev:dimen/mtrl_extended_fab_end_padding = 0x7f0702c6
com.iptv.android.dev:styleable/KeyFramesVelocity = 0x7f120043
com.iptv.android.dev:dimen/design_snackbar_background_corner_radius = 0x7f070080
com.iptv.android.dev:attr/lStar = 0x7f040256
com.iptv.android.dev:attr/tabContentStart = 0x7f0403ff
com.iptv.android.dev:drawable/exo_ic_speed = 0x7f0800a5
com.iptv.android.dev:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f070158
com.iptv.android.dev:attr/autoSizeMinTextSize = 0x7f040043
com.iptv.android.dev:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.iptv.android.dev:attr/hoveredFocusedTranslationZ = 0x7f04021c
com.iptv.android.dev:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f110442
com.iptv.android.dev:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.iptv.android.dev:style/Base.TextAppearance.AppCompat.Small = 0x7f11002b
com.iptv.android.dev:attr/badgeWithTextShapeAppearance = 0x7f040063
com.iptv.android.dev:attr/show_buffering = 0x7f0403b2
com.iptv.android.dev:dimen/mtrl_btn_elevation = 0x7f070279
com.iptv.android.dev:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1102e6
com.iptv.android.dev:attr/showAsAction = 0x7f0403ab
com.iptv.android.dev:style/TextAppearance.AppCompat.Button = 0x7f1101c9
com.iptv.android.dev:dimen/m3_comp_navigation_bar_container_height = 0x7f07015e
com.iptv.android.dev:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f11010c
com.iptv.android.dev:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0e0133
com.iptv.android.dev:id/exo_content_frame = 0x7f0a00af
com.iptv.android.dev:color/m3_ref_palette_dynamic_primary80 = 0x7f0600e3
com.iptv.android.dev:attr/materialCalendarDayOfWeekLabel = 0x7f0402c8
com.iptv.android.dev:attr/centerIfNoTextEnabled = 0x7f0400a5
com.iptv.android.dev:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.iptv.android.dev:attr/touchRegionId = 0x7f040489
com.iptv.android.dev:anim/abc_popup_exit = 0x7f010004
com.iptv.android.dev:attr/fontVariationSettings = 0x7f0401fb
com.iptv.android.dev:anim/abc_fade_in = 0x7f010000
com.iptv.android.dev:styleable/SwitchCompat = 0x7f12008e
com.iptv.android.dev:id/parentRelative = 0x7f0a0178
com.iptv.android.dev:drawable/exo_styled_controls_play = 0x7f0800ca
com.iptv.android.dev:attr/itemIconSize = 0x7f04023a
com.iptv.android.dev:string/mtrl_picker_a11y_prev_month = 0x7f100115
com.iptv.android.dev:color/m3_ref_palette_tertiary10 = 0x7f06014e
com.iptv.android.dev:attr/chipStandaloneStyle = 0x7f0400c4
com.iptv.android.dev:attr/counterEnabled = 0x7f040148
com.iptv.android.dev:drawable/abc_btn_radio_material = 0x7f080031
com.iptv.android.dev:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.iptv.android.dev:color/m3_sys_color_dark_tertiary = 0x7f060183
com.iptv.android.dev:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f110161
com.iptv.android.dev:macro/m3_comp_fab_surface_container_color = 0x7f0e003e
com.iptv.android.dev:attr/backgroundOverlayColorAlpha = 0x7f040050
com.iptv.android.dev:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f11047f
com.iptv.android.dev:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f080102
com.iptv.android.dev:style/Widget.Design.FloatingActionButton = 0x7f110370
com.iptv.android.dev:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f070214
com.iptv.android.dev:styleable/MaterialAlertDialogTheme = 0x7f12004d
com.iptv.android.dev:color/on_surface_variant = 0x7f0602f0
com.iptv.android.dev:macro/m3_comp_filled_button_label_text_type = 0x7f0e0046
com.iptv.android.dev:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.iptv.android.dev:styleable/Tooltip = 0x7f120097
com.iptv.android.dev:color/m3_textfield_indicator_text_color = 0x7f060205
com.iptv.android.dev:attr/clickAction = 0x7f0400cc
com.iptv.android.dev:color/m3_ref_palette_neutral80 = 0x7f06011d
com.iptv.android.dev:attr/chipMinHeight = 0x7f0400bf
com.iptv.android.dev:style/Widget.AppCompat.DrawerArrowToggle = 0x7f110337
com.iptv.android.dev:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f0700be
com.iptv.android.dev:dimen/design_bottom_navigation_height = 0x7f070063
com.iptv.android.dev:color/background_floating_material_dark = 0x7f06001e
com.iptv.android.dev:attr/activeIndicatorLabelPadding = 0x7f040025
com.iptv.android.dev:attr/actionButtonStyle = 0x7f04000c
com.iptv.android.dev:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.iptv.android.dev:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f11019a
com.iptv.android.dev:string/close_drawer = 0x7f100053
com.iptv.android.dev:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f060210
com.iptv.android.dev:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f110310
com.iptv.android.dev:attr/textInputFilledExposedDropdownMenuStyle = 0x7f04044a
com.iptv.android.dev:attr/shapeAppearanceOverlay = 0x7f0403a5
com.iptv.android.dev:string/exo_controls_vr_description = 0x7f1000ac
com.iptv.android.dev:id/action_context_bar = 0x7f0a0039
com.iptv.android.dev:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f07014d
com.iptv.android.dev:attr/badgeText = 0x7f04005b
com.iptv.android.dev:style/Base.V28.Theme.AppCompat.Light = 0x7f1100bc
com.iptv.android.dev:attr/sliderStyle = 0x7f0403c7
com.iptv.android.dev:attr/actionBarPopupTheme = 0x7f040003
com.iptv.android.dev:id/design_menu_item_action_area = 0x7f0a0087
com.iptv.android.dev:color/exo_error_message_background_color = 0x7f06006a
com.iptv.android.dev:drawable/exo_styled_controls_overflow_show = 0x7f0800c8
com.iptv.android.dev:color/m3_sys_color_on_primary_fixed = 0x7f0601ef
com.iptv.android.dev:id/search_plate = 0x7f0a01a2
com.iptv.android.dev:attr/mock_showDiagonals = 0x7f040305
com.iptv.android.dev:attr/arrowHeadLength = 0x7f04003b
com.iptv.android.dev:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080022
com.iptv.android.dev:color/m3_tabs_icon_color_secondary = 0x7f0601fc
com.iptv.android.dev:attr/altSrc = 0x7f040031
com.iptv.android.dev:id/tag_screen_reader_focusable = 0x7f0a01d4
com.iptv.android.dev:color/m3_sys_color_light_surface_bright = 0x7f0601e5
com.iptv.android.dev:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.iptv.android.dev:id/date_picker_actions = 0x7f0a0080
com.iptv.android.dev:attr/collapseIcon = 0x7f0400da
com.iptv.android.dev:dimen/exo_small_icon_padding_horizontal = 0x7f0700a5
com.iptv.android.dev:color/material_dynamic_neutral_variant95 = 0x7f060233
com.iptv.android.dev:attr/popExitAnim = 0x7f040365
com.iptv.android.dev:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0401d7
com.iptv.android.dev:color/m3_sys_color_on_secondary_fixed = 0x7f0601f1
com.iptv.android.dev:attr/customPixelDimension = 0x7f04015a
com.iptv.android.dev:attr/show_next_button = 0x7f0403b4
