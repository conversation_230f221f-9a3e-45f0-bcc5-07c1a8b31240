pub mod config;
pub mod database;
pub mod error;
pub mod handlers;
pub mod middleware;
pub mod models;
pub mod services;
pub mod validation;

pub use config::Config;
pub use database::Database;

use axum::{
    Extension,

    http::StatusCode,
    response::Json,
    routing::{delete, get, post, put},
    Router,
};

use serde_json::{json, Value};
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;

use services::{AuthService, UserService, DeviceService, ContentService, TmdbService};
use services::activity_log::ActivityLogService;
use services::user_activity::UserActivityService;

#[derive(Clone)]
pub struct AppState {
    pub database: Database,
    pub auth_service: services::AuthService,
    pub user_service: services::UserService,
    pub device_service: services::DeviceService,
    pub content_service: services::ContentService,
    pub tmdb_service: services::TmdbService,
    pub activity_log_service: ActivityLogService,
    pub user_activity_service: UserActivityService,
    pub user_agent_service: services::UserAgentService,
    pub security_service: services::security::SecurityService,
    pub config: config::Config,
}

pub
async fn create_app(database: Database, config: Config) -> anyhow::Result<Router> {
    // Initialize auth service with JWT secret from config
    let auth_service = AuthService::new(config.jwt_secret.clone());

    // Initialize user service
    let user_service = UserService::new(database.clone(), auth_service.clone());
    
    // Initialize device service
    let device_service = DeviceService::new(database.clone());
    
    // Initialize TMDB service
    let tmdb_service = TmdbService::new(database.pool.clone());

    // Initialize content service with TMDB service
    let content_service = ContentService::new(database.pool.clone())
        .with_tmdb_service(tmdb_service.clone());

    // Initialize activity log service
    let activity_log_service = ActivityLogService::new(database.clone());
    // Initialize user activity service
    let user_activity_service = UserActivityService::new(database.clone());
    // Initialize user agent service
    let user_agent_service = services::UserAgentService::new(config.jwt_secret.clone());
    // Initialize security service
    let security_service = services::security::SecurityService::new(database.pool.clone());

    
    // Create shared app state
    let app_state = AppState {
        database,
        auth_service,
        user_service,
        device_service,
        content_service,
        tmdb_service,
        activity_log_service,
        user_activity_service,
        user_agent_service,
        security_service,
        config: config.clone(),
    };

    // Create admin content routes (require admin role)
    let admin_content_routes = Router::new()
        .nest("/api/admin/content", handlers::content::admin_content_routes())
        .with_state(app_state.content_service.clone())
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::require_admin,
        ));

    // Create admin series routes (require admin role)
    let admin_series_routes = Router::new()
        .nest("/api/admin/series", handlers::series::admin_series_routes())
        .with_state(app_state.content_service.clone())
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::require_admin,
        ));

    // Create admin specialized content routes (require admin role)
    let admin_specialized_routes = Router::new()
        .nest("/api/admin/specialized", handlers::specialized::admin_specialized_content_routes())
        .with_state(app_state.content_service.clone())
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::require_admin,
        ));

    // Create admin routes (require admin role)
    let admin_routes = Router::new()
        .route("/api/admin/admins", post(handlers::admin::create_admin))
        .route("/api/admin/resellers", get(handlers::admin::get_resellers))
        .route("/api/admin/resellers", post(handlers::admin::create_reseller))
        .route("/api/admin/resellers/:id/credits", put(handlers::admin::update_reseller_credits))
        .route("/api/admin/resellers/:id/users", get(handlers::admin::get_reseller_users))
        .route("/api/admin/users", get(handlers::admin::get_all_end_users))
        .route("/api/admin/users", post(handlers::admin::create_end_user))
        .route("/api/admin/users/:id", get(handlers::admin::get_user))
        .route("/api/admin/users/:id", put(handlers::admin::update_user))
        .route("/api/admin/users/:id", delete(handlers::admin::delete_user))
        .route("/api/admin/users/:id/status", put(handlers::admin::set_user_active_status))
        .route("/api/admin/users/:id/credits", put(handlers::admin::update_user_credits))
        .route("/api/admin/users/:id/password", put(handlers::admin::reset_user_password))
        .route("/api/admin/users/:id/transfer", put(handlers::admin::transfer_user))
        .route("/api/admin/users/:id/parental-pin", put(handlers::admin::set_user_parental_pin))
        .route("/api/admin/users/:id/parental-pin", delete(handlers::admin::remove_user_parental_pin))
        .route("/api/admin/users/export", get(handlers::admin::export_users))
        .route("/api/admin/users/bulk", put(handlers::admin::bulk_update_users))
        .route("/api/admin/users/:id/devices", get(handlers::device::admin_get_user_devices))
        .route("/api/admin/users/:id/devices/:device_id", delete(handlers::device::admin_remove_user_device))
        .route("/api/admin/devices/:device_id/disconnect", post(handlers::device::admin_disconnect_device))
        .route("/api/admin/users/:id/stats", get(handlers::admin::get_user_stats))
        .route("/api/admin/users/:id/subscriptions", put(handlers::admin::update_user_subscriptions))
        .route("/api/admin/devices", get(handlers::device::admin_get_all_devices))
        .route("/api/admin/devices/stats", get(handlers::device::admin_get_device_stats))
        .route("/api/admin/config", get(handlers::admin::get_config))
        .route("/api/admin/config", put(handlers::admin::update_config))
        .route("/api/admin/config/credits", put(handlers::admin::update_credit_config))
        .route("/api/admin/config/tmdb", put(handlers::admin::update_tmdb_config))
        .route("/api/admin/config/tmdb/test", post(handlers::admin::test_tmdb_key))
        .route("/api/admin/credits/rates", get(handlers::admin::get_credit_rates))
        .route("/api/admin/stats", get(handlers::admin::get_system_stats))
        .route("/api/admin/resellers/:id/demo-limit", put(handlers::admin::update_reseller_demo_limit))
        .route("/api/admin/categories", get(handlers::admin::get_categories))
        .route("/api/admin/logs", get(handlers::get_admin_activity_logs))
        // User activity tracking routes (Admin only)
        .route("/api/admin/activity/login-history", get(handlers::user_activity::get_login_history))
        .route("/api/admin/activity/viewing-history", get(handlers::user_activity::get_viewing_history))
        .route("/api/admin/activity/dashboard-metrics", get(handlers::user_activity::get_dashboard_metrics))
        // Ticket management routes (Admin)
        .route("/api/admin/tickets", get(handlers::get_tickets))
        .route("/api/admin/tickets", post(handlers::create_ticket_admin))
        .route("/api/admin/tickets/unread-count", get(handlers::get_unread_count_total))
        .route("/api/admin/tickets/:id", put(handlers::update_ticket_admin))
        .route("/api/admin/tickets/:id", delete(handlers::delete_ticket_admin))
        .route("/api/admin/tickets/:id/assign", post(handlers::assign_ticket_to_self))
        .route("/api/admin/tickets/:id/messages", get(handlers::get_ticket_messages))
        .route("/api/admin/tickets/:id/messages", post(handlers::add_ticket_message))
        .route("/api/admin/tickets/:id/read", post(handlers::mark_ticket_messages_read))
        // Security management routes (Admin only)
        .route("/api/admin/security/stats", get(handlers::security::get_security_stats))
        .route("/api/admin/security/incidents", get(handlers::security::get_security_incidents))
        .route("/api/admin/security/incidents", post(handlers::security::create_security_incident))
        .route("/api/admin/security/incidents/:id", delete(handlers::security::delete_security_incident))
        .route("/api/admin/security/bans", get(handlers::security::get_security_bans))
        .route("/api/admin/security/bans", post(handlers::security::create_security_ban))
        .route("/api/admin/security/bans/cleanup", post(handlers::security::cleanup_expired_bans))
        .route("/api/admin/security/users/:username/ban-status", get(handlers::security::check_user_ban_status))
        .route("/api/admin/security/users/:username/unban", post(handlers::security::unban_user))
        // Master API Key routes
        .route("/api/admin/master-api-keys", get(handlers::get_master_api_keys))
        .route("/api/admin/master-api-keys", post(handlers::create_master_api_key))
        .route("/api/admin/master-api-keys/:id", get(handlers::get_master_api_key_by_id))
        .route("/api/admin/master-api-keys/:id", put(handlers::update_master_api_key))
        .route("/api/admin/master-api-keys/:id", delete(handlers::delete_master_api_key))
        .route("/api/admin/master-api-keys/:id/regenerate", post(handlers::regenerate_master_api_key))
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::require_admin,
        ));

    // Create reseller routes (require reseller role)
    let reseller_routes = Router::new()
        .route("/api/reseller/users", get(handlers::reseller::get_my_end_users))
        .route("/api/reseller/users", post(handlers::reseller::create_end_user))
        .route("/api/reseller/users/:id", put(handlers::reseller::update_end_user))
        .route("/api/reseller/users/:id", axum::routing::delete(handlers::reseller::delete_end_user))
        .route("/api/reseller/users/:id/extend", put(handlers::reseller::extend_user_subscription))
        .route("/api/reseller/users/:id/status", put(handlers::reseller::set_end_user_active_status))
        .route("/api/reseller/users/:id/devices", get(handlers::device::reseller_get_user_devices))
        .route("/api/reseller/users/:id/parental-pin", put(handlers::reseller_set_user_parental_pin))
        .route("/api/reseller/users/:id/parental-pin", delete(handlers::reseller_remove_user_parental_pin))
        .route("/api/reseller/credits", get(handlers::reseller::get_credit_balance))
        .route("/api/reseller/credits/calculate/:devices", get(handlers::reseller::calculate_credit_cost))
        .route("/api/reseller/credits/usage", get(handlers::reseller::get_credit_usage_stats))
        .route("/api/reseller/credits/rates", get(handlers::reseller::get_credit_rates))
        .route("/api/reseller/demos", post(handlers::reseller::create_demo_user))
        .route("/api/reseller/demos/stats", get(handlers::reseller::get_demo_stats))
        .route("/api/reseller/categories", get(handlers::admin::get_categories))
        .route("/api/reseller/stats", get(handlers::reseller::get_reseller_stats))
        .route("/api/reseller/logs", get(handlers::get_reseller_activity_logs))
        // Ticket management routes (Reseller)
        .route("/api/reseller/tickets", post(handlers::create_ticket))
        .route("/api/reseller/tickets", get(handlers::get_tickets))
        .route("/api/reseller/tickets/unread-count", get(handlers::get_unread_count_total))
        .route("/api/reseller/tickets/:id/messages", get(handlers::get_ticket_messages))
        .route("/api/reseller/tickets/:id/messages", post(handlers::add_ticket_message))
        .route("/api/reseller/tickets/:id/read", post(handlers::mark_ticket_messages_read))
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::reseller_or_admin,
        ));

    // Create device routes (require authentication)
    let device_routes = Router::new()
        .route("/api/devices", get(handlers::device::get_user_devices))
        .route("/api/devices", post(handlers::device::register_device))
        .route("/api/devices/:device_id", axum::routing::delete(handlers::device::remove_device))
        .route("/api/devices/:device_id/check", get(handlers::device::check_device_connection));



    // Create content routes (require authentication)
    let content_routes = Router::new()
        .nest("/api/content", handlers::content::content_routes())
        .with_state(app_state.content_service.clone());

    // Create movies routes (require authentication)
    let movies_routes = Router::new()
        .nest("/api/movies", handlers::content::movies_routes())
        .with_state(app_state.content_service.clone());

    // Create TV routes (require authentication)
    let tv_routes = Router::new()
        .nest("/api/tv", handlers::content::tv_routes())
        .with_state(app_state.content_service.clone());

    // Create series routes (require authentication)
    let series_routes = Router::new()
        .nest("/api/series", handlers::series::series_routes())
        .with_state(app_state.content_service.clone());

    // Create episodes routes (require authentication)
    let episodes_routes = Router::new()
        .nest("/api/episodes", handlers::series::episodes_routes())
        .with_state(app_state.content_service.clone());

    // Create specialized content routes (require authentication)
    let specialized_routes = Router::new()
        .nest("/api/specialized", handlers::specialized::specialized_content_routes())
        .with_state(app_state.content_service.clone());

    // Create specialized delivery routes (require authentication)
    let specialized_delivery_routes = Router::new()
        .nest("/api/delivery", handlers::specialized::specialized_delivery_routes())
        .with_state(app_state.content_service.clone());

    // Create enhanced content delivery routes (require authentication) - Req 4.2, 4.3, 4.4, 8.2, 1.3, 1.4
    let enhanced_delivery_routes = Router::new()
        .nest("/api/delivery", handlers::content::delivery_routes())
        .with_state(app_state.content_service.clone());

    // Create TMDB routes (require authentication)
    let tmdb_routes = Router::new()
        .nest("/api/tmdb", handlers::tmdb::tmdb_routes())
        .with_state(app_state.tmdb_service.clone());

    // Create protected routes that require authentication
    let protected_routes = Router::new()
        .route("/api/protected", get(protected_endpoint))
        .merge(admin_routes)
        .merge(admin_content_routes)
        .merge(admin_series_routes)
        .merge(admin_specialized_routes)
        .merge(reseller_routes)
        .merge(device_routes)
        .nest("/api/user", handlers::user_profile::routes().layer(Extension(app_state.database.pool.clone())))
        // User history routes
        .route("/api/user/history", axum::routing::post(handlers::user_history::save_watch_progress))
        .route("/api/user/history", axum::routing::get(handlers::user_history::get_watch_history))
        .route("/api/user/history/clear", axum::routing::delete(handlers::user_history::clear_watch_history))
        .merge(content_routes)
        .merge(movies_routes)
        .merge(tv_routes)
        .merge(series_routes)
        .merge(episodes_routes)
        .merge(specialized_routes)
        .merge(specialized_delivery_routes)
        .merge(enhanced_delivery_routes)
        .merge(tmdb_routes)
        // Content delivery with activity tracking
        .route("/api/content/:id/stream-tracked", get(handlers::content::get_content_for_delivery_with_tracking))
        .route("/api/episodes/:id/stream-tracked", get(handlers::series::get_episode_for_delivery_with_tracking))

        .route("/api/novelas", axum::routing::get(|| async { axum::Json(serde_json::json!([])) }))
        .route("/api/events", axum::routing::get(|| async { axum::Json(serde_json::json!([])) }))
        .layer(axum::middleware::from_fn(middleware::request_logging_middleware))
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::master_api_key_middleware,
        ))
        .layer(axum::middleware::from_fn(middleware::input_validation_middleware))
        .layer(axum::middleware::from_fn(middleware::rate_limiting_middleware));

    // JWT-only protected routes (no User-Agent validation, no Master API Key middleware)
    let jwt_only_routes = Router::new()
        .route("/api/auth/renew-user-agent", post(handlers::auth::renew_user_agent_with_secret))
        .route("/api/auth/validate-admin", post(handlers::auth::validate_admin))
        .route("/api/auth/validate-reseller", post(handlers::auth::validate_reseller))
        .layer(axum::middleware::from_fn(middleware::request_logging_middleware))
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::jwt_only_auth_middleware,  // Use JWT-only middleware instead
        ))
        .layer(axum::middleware::from_fn(middleware::input_validation_middleware))
        .layer(axum::middleware::from_fn(middleware::rate_limiting_middleware));

    let app = Router::new()
        .route("/api/health", get(health_check))
        // Test endpoint to verify routing works
        .route("/api/test/hello", get(|| async { "Hello World!" }))
        // Authentication routes (no auth required)
        .route("/api/auth/login", post(handlers::auth::login))
        .route("/api/auth/logout", post(handlers::auth::logout))
        .route("/api/auth/refresh", post(handlers::auth::refresh_token))


        // Merge protected routes
        .merge(protected_routes)
        .merge(jwt_only_routes)
        .layer(CorsLayer::permissive())
        .layer(TraceLayer::new_for_http())
        .with_state(app_state);

    Ok(app)
}

async fn health_check() -> Result<Json<Value>, StatusCode> {
    Ok(Json(json!({
        "status": "ok",
        "message": "IPTV Backend is running"
    })))
}

async fn protected_endpoint(
    request: axum::extract::Request,
) -> Result<Json<Value>, StatusCode> {
    let claims = request
        .extensions()
        .get::<services::Claims>()
        .ok_or(StatusCode::UNAUTHORIZED)?;

    Ok(Json(json!({
        "message": "This is a protected endpoint",
        "user": {
            "id": claims.sub,
            "username": claims.username,
            "role": claims.role
        }
    })))
}


impl AppState {
    /// Get app secret from database configuration
    pub async fn get_app_secret(&self) -> Result<String, crate::error::AppError> {
        use crate::models::SystemConfig;

        let config = sqlx::query_as::<_, SystemConfig>(
            "SELECT key, value FROM config WHERE key = 'app_secret'"
        )
        .fetch_optional(&self.database.pool)
        .await?;

        match config {
            Some(config) if !config.value.is_empty() => Ok(config.value),
            _ => {
                // Fallback to config file value
                Ok(self.config.app_secret.clone())
            }
        }
    }

    /// Validate if provided app secret is valid (supports both mobile app and admin panel)
    pub async fn is_valid_app_secret(&self, provided_secret: &str) -> Result<bool, crate::error::AppError> {
        use crate::models::SystemConfig;

        // Get all valid app secrets from database
        let configs = sqlx::query_as::<_, SystemConfig>(
            "SELECT key, value FROM config WHERE key IN ('app_secret', 'admin_panel_app_secret')"
        )
        .fetch_all(&self.database.pool)
        .await?;

        // Check against database values
        for config in configs {
            if !config.value.is_empty() && config.value == provided_secret {
                return Ok(true);
            }
        }

        // Fallback: check against config file value
        if self.config.app_secret == provided_secret {
            return Ok(true);
        }

        Ok(false)
    }
}
