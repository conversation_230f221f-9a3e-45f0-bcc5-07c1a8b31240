use axum::{
    extract::{State, Extension},
    <PERSON><PERSON>,
    response::<PERSON><PERSON> as Response<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use tracing::info;

use crate::{
    models::{
        user_activity::{CreateViewingHistoryRequest, UserViewingHistory},
    },
    services::Claims,
    error::AppError,
    AppState,
};

#[derive(Debug, Deserialize)]
pub struct SaveWatchProgressRequest {
    #[serde(rename = "contentId")]
    pub content_id: String,
    pub duration: i32,
    pub progress: f64,
}

#[derive(Debug, Serialize)]
pub struct SaveWatchProgressResponse {
    pub success: bool,
    pub message: String,
}

#[derive(Debug, Deserialize)]
pub struct GetWatchHistoryQuery {
    pub page: Option<i64>,
    pub limit: Option<i64>,
}

#[derive(Debug, Serialize)]
pub struct WatchHistoryItem {
    pub id: i64,
    pub content_id: Option<i64>,
    pub content_title: String,
    pub content_type: String,
    pub episode_title: Option<String>,
    pub season_number: Option<i32>,
    pub episode_number: Option<i32>,
    pub start_timestamp: String,
    pub progress: Option<f64>,
    pub duration: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct GetWatchHistoryResponse {
    pub success: bool,
    pub history: Vec<WatchHistoryItem>,
    pub total: i64,
    pub page: i64,
    pub limit: i64,
    pub total_pages: i64,
}

/// Save user watch progress
pub async fn save_watch_progress(
    State(app_state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<SaveWatchProgressRequest>,
) -> Result<ResponseJson<SaveWatchProgressResponse>, AppError> {
    let user_id = claims.sub.parse::<i64>()
        .map_err(|_| AppError::AuthenticationFailed)?;

    info!("Saving watch progress for user {}: content_id={}, progress={}, duration={}", 
          user_id, request.content_id, request.progress, request.duration);

    // Get user info for logging
    let user = app_state.user_service.get_user_by_id(user_id).await?;

    // Parse content_id to i64
    let content_id = request.content_id.parse::<i64>()
        .map_err(|_| AppError::BadRequest("Invalid content_id format".to_string()))?;

    // Create viewing history request
    let viewing_request = CreateViewingHistoryRequest {
        user_id,
        username: user.username.clone(),
        content_type: "watch_progress".to_string(),
        content_id: Some(content_id),
        content_title: format!("Content {}", content_id),
        episode_title: None,
        season_number: None,
        episode_number: None,
        ip_address: None, // Could be extracted from request headers if needed
        device_info: None, // Could be extracted from User-Agent if needed
        user_agent: None, // Could be extracted from request headers if needed
    };

    // Log the viewing activity
    app_state.user_activity_service.log_viewing(viewing_request).await?;

    // Also save progress in a dedicated watch_progress table (if it exists)
    // For now, we'll use the viewing history table
    
    info!("Watch progress saved successfully for user {} on content {}", user_id, content_id);

    Ok(ResponseJson(SaveWatchProgressResponse {
        success: true,
        message: "Watch progress saved successfully".to_string(),
    }))
}

/// Get user watch history
pub async fn get_watch_history(
    State(app_state): State<AppState>,
    Extension(claims): Extension<Claims>,
    axum::extract::Query(query): axum::extract::Query<GetWatchHistoryQuery>,
) -> Result<ResponseJson<GetWatchHistoryResponse>, AppError> {
    let user_id = claims.sub.parse::<i64>()
        .map_err(|_| AppError::AuthenticationFailed)?;

    let page = query.page.unwrap_or(1);
    let limit = query.limit.unwrap_or(50);
    let offset = (page - 1) * limit;

    info!("Getting watch history for user {}: page={}, limit={}", user_id, page, limit);

    // Get viewing history for this user
    let pool = &app_state.database.pool;

    // Get total count
    let total: i64 = sqlx::query_scalar(
        "SELECT COUNT(*) FROM user_viewing_history WHERE user_id = ?"
    )
    .bind(user_id)
    .fetch_one(pool)
    .await?;

    // Get paginated results
    let history_records = sqlx::query_as::<_, UserViewingHistory>(
        "SELECT * FROM user_viewing_history WHERE user_id = ? ORDER BY start_timestamp DESC LIMIT ? OFFSET ?"
    )
    .bind(user_id)
    .bind(limit)
    .bind(offset)
    .fetch_all(pool)
    .await?;

    // Convert to response format
    let history: Vec<WatchHistoryItem> = history_records
        .into_iter()
        .map(|record| WatchHistoryItem {
            id: record.id,
            content_id: record.content_id,
            content_title: record.content_title,
            content_type: record.content_type,
            episode_title: record.episode_title,
            season_number: record.season_number,
            episode_number: record.episode_number,
            start_timestamp: record.start_timestamp,
            progress: None, // We could add this to the database schema if needed
            duration: None, // We could add this to the database schema if needed
        })
        .collect();

    let total_pages = (total + limit - 1) / limit;

    info!("Retrieved {} watch history items for user {}", history.len(), user_id);

    Ok(ResponseJson(GetWatchHistoryResponse {
        success: true,
        history,
        total,
        page,
        limit,
        total_pages,
    }))
}

/// Clear user watch history
pub async fn clear_watch_history(
    State(app_state): State<AppState>,
    Extension(claims): Extension<Claims>,
) -> Result<ResponseJson<SaveWatchProgressResponse>, AppError> {
    let user_id = claims.sub.parse::<i64>()
        .map_err(|_| AppError::AuthenticationFailed)?;

    info!("Clearing watch history for user {}", user_id);

    let pool = &app_state.database.pool;

    // Delete all viewing history for this user
    let result = sqlx::query("DELETE FROM user_viewing_history WHERE user_id = ?")
        .bind(user_id)
        .execute(pool)
        .await?;

    info!("Cleared {} watch history records for user {}", result.rows_affected(), user_id);

    Ok(ResponseJson(SaveWatchProgressResponse {
        success: true,
        message: format!("Cleared {} watch history records", result.rows_affected()),
    }))
}
