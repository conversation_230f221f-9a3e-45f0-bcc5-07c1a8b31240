pub mod auth;
pub mod admin;
pub mod reseller;
pub mod content;
pub mod device;
pub mod tmdb;
pub mod series;
pub mod specialized;
pub mod user_profile;
pub mod user_history;
pub mod activity_log;
pub mod user_activity;
pub mod ticket;
pub mod security;
pub mod master_api_key;

pub use auth::*;
// Re-export only the specific admin/reseller symbols used by lib.rs to avoid ambiguous glob warnings
pub use admin::{
    get_resellers,
    create_admin,
    create_reseller,
    create_end_user,
    get_all_end_users,
    get_reseller_users,
    update_reseller_credits,
    set_user_active_status,
    get_config,
    update_config,
    update_tmdb_config,
    test_tmdb_key,
    get_credit_rates,
    get_user,
    update_user,
    delete_user,
    update_user_credits,
    reset_user_password,
    transfer_user,
    export_users,
    set_user_parental_pin,
    remove_user_parental_pin,
    get_system_stats,
    get_user_stats,
    bulk_update_users,
    update_credit_config,
    update_reseller_demo_limit,
    get_categories,
    update_user_subscriptions,
};
pub use reseller::{
    get_my_end_users,
    create_end_user as reseller_create_end_user,
    extend_user_subscription,
    set_end_user_active_status,
    get_credit_balance,
    calculate_credit_cost,
    get_credit_rates as reseller_get_credit_rates,
    delete_end_user,
    get_credit_usage_stats,
    set_user_parental_pin as reseller_set_user_parental_pin,
    remove_user_parental_pin as reseller_remove_user_parental_pin,
};
pub use content::*;
pub use device::*;
pub use activity_log::{get_admin_activity_logs, get_reseller_activity_logs};
pub use ticket::{
    create_ticket,
    create_ticket_admin,
    get_tickets,
    get_ticket_detail,
    update_ticket,
    add_message,
    get_ticket_stats,
    get_ticket_messages,
    add_ticket_message,
    mark_ticket_messages_read,
    update_ticket_admin,
    assign_ticket_to_self,
    get_unread_count_total,
    delete_ticket_admin,
};
pub mod parental_control;
pub use parental_control::*;
pub use security::*;

pub use master_api_key::{
    get_master_api_keys,
    get_master_api_key_by_id,
    create_master_api_key,
    update_master_api_key,
    delete_master_api_key,
    regenerate_master_api_key,
};
