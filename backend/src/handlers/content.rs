use axum::{
    extract::{Path, Query, State, ConnectInfo},
    http::{StatusCode, HeaderMap},
    response::<PERSON><PERSON>,
    routing::{get, put},
    Router,
    Extension,
};
use std::net::SocketAddr;
use serde::{Deserialize, Serialize};

use crate::{
    error::AppError,
    models::{Category, Content, UserRole},
    models::user_activity::ContentType,
    services::{ContentService, Claims, DrmService},
    handlers::user_activity,
};

#[derive(Debug, Serialize)]
pub struct CategoryResponse {
    pub id: i64,
    pub name: String,
    pub category_type: String,
    pub created_at: String,
}

#[derive(Debug, Serialize)]
pub struct ContentResponse {
    pub id: i64,
    pub category_id: i64,
    pub title: String,
    pub description: Option<String>,
    pub poster_url: Option<String>,
    pub image_url: Option<String>, // Added for Android compatibility
    pub backdrop_url: Option<String>,
    pub video_url: String,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    pub runtime: Option<i32>,
    pub channel_number: Option<i32>,
    pub is_live: Option<bool>,
    pub active: bool,
    pub created_at: String,
    pub content_type: String,
    // TV Channel specific fields
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>,
    pub drm_keys: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateCategoryRequest {
    pub name: String,
    pub category_type: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateCategoryRequest {
    pub name: Option<String>,
    pub category_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct AssignCategoryRequest {
    pub category_id: i64,
}

#[derive(Debug, Deserialize)]
pub struct CreateContentRequest {
    pub category_id: i64,
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub backdrop_url: Option<String>,
    pub stream_url: String,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CreateContentFromTmdbRequest {
    pub category_id: i64,
    pub tmdb_id: i64,
    pub media_type: String, // "movie" or "tv"
    pub stream_url: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateMovieRequest {
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub stream_url: String,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    pub runtime: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CreateTvChannelRequest {
    pub name: String,
    pub description: Option<String>,
    pub logo_url: Option<String>,
    pub stream_url: String,
    pub channel_number: Option<i32>,
    pub is_live: bool,
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>, // 'DRM' or 'M3U8'
    pub drm_keys: Option<String>, // ClearKey keys in hexadecimal format (key:kid pairs)
    pub active: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateContentRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub backdrop_url: Option<String>,
    pub stream_url: Option<String>,
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>,
    pub channel_number: Option<i32>,
    pub is_live: Option<bool>,
    pub drm_keys: Option<String>,
    pub active: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<i32>,
    pub limit: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    pub q: String,
    pub category: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct EnhancedSearchQuery {
    pub q: String,
    pub category: Option<String>,
    pub year: Option<i32>,
    pub tmdb_id: Option<i64>,
    pub page: Option<i32>,
    pub limit: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct ContentQuery {
    pub category: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ContentDeliveryResponse {
    pub id: i64,
    pub category_id: i64,
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub stream_url: String, // Decrypted URL for delivery
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    pub channel_number: Option<i32>, // Channel number for display
    pub created_at: String,
    pub drm_keys: Option<String>, // Original hexadecimal format (key:kid pairs)
    pub drm_keys_exo: Option<String>, // ClearKey JSON for ExoPlayer (Base64URL format)
}

#[derive(Debug, Serialize)]
pub struct EnhancedContentResponse {
    pub id: i64,
    pub category: CategoryResponse,
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub backdrop_url: Option<String>,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    pub created_at: String,
}

#[derive(Debug, Serialize)]
pub struct ContentListResponse {
    pub content: Vec<EnhancedContentResponse>,
    pub total: i64,
    pub page: i32,
    pub limit: i32,
    pub has_more: bool,
}

impl From<Category> for CategoryResponse {
    fn from(category: Category) -> Self {
        Self {
            id: category.id,
            name: category.name,
            category_type: category.category_type,
            created_at: category.created_at.to_rfc3339(),
        }
    }
}

// Helper function to verify user is active and has access to content
async fn verify_user_access(
    content_service: &ContentService,
    claims: &Claims,
    category_type: Option<&str>,
) -> Result<(), AppError> {
    // Get user details to check if active
    let user = content_service.get_user_by_id(claims.sub.parse::<i64>().map_err(|_| AppError::AuthenticationFailed)?).await?;

    // Check if user is active
    if !user.active {
        return Err(AppError::Forbidden);
    }

    // Special access control for XXX content (Req 1.4)
    if let Some(cat_type) = category_type {
        if cat_type == "XXX" && user.role == UserRole::EndUser {
            // For now, end users can access XXX content, but this could be configurable
            // In a real system, you might want to add a special permission flag
        }
    }

    Ok(())
}

impl From<Content> for ContentResponse {
    fn from(content: Content) -> Self {
        Self {
            id: content.id,
            category_id: content.category_id,
            title: content.title,
            description: content.description,
            poster_url: content.image_url.clone(), // Map image_url to poster_url for frontend
            image_url: content.image_url, // Direct mapping for Android compatibility
            backdrop_url: content.backdrop_url, // Now supported!
            video_url: content.encrypted_stream_url, // Note: This will be encrypted, use get_content_for_edit for decrypted
            tmdb_id: content.tmdb_id,
            year: content.year,
            runtime: None, // Not in Content model
            channel_number: content.channel_number,
            is_live: content.is_live,
            active: content.active,
            created_at: content.created_at.to_rfc3339(),
            content_type: match content.category_id {
                1 => "vod".to_string(),    // VOD
                2 => "tv".to_string(),     // TV
                3 => "xxx".to_string(),    // XXX
                4 => "novelas".to_string(), // Novelas
                5 => "events".to_string(),  // Eventos
                6 => "kids".to_string(),    // Kids
                7 => "anime".to_string(),   // Anime
                8 => "series".to_string(),  // Series
                _ => "vod".to_string(),     // Default
            },
            // TV Channel specific fields
            provider: content.provider,
            country: content.country,
            format: content.format,
            drm_keys: content.drm_keys,
        }
    }
}

// GET /api/content/categories - Get all categories
pub async fn get_categories(
    State(content_service): State<ContentService>,
    Extension(_claims): Extension<Claims>, // Require authentication
) -> Result<Json<Vec<CategoryResponse>>, AppError> {
    let categories = content_service.get_all_categories().await?;
    let response: Vec<CategoryResponse> = categories.into_iter().map(Into::into).collect();
    Ok(Json(response))
}

// GET /api/content/categories/:id - Get category by ID
pub async fn get_category_by_id(
    State(content_service): State<ContentService>,
    Path(id): Path<i64>,
    Extension(_claims): Extension<Claims>, // Require authentication
) -> Result<Json<CategoryResponse>, AppError> {
    let category = content_service
        .get_category_by_id(id)
        .await?
        .ok_or(AppError::NotFound)?;

    Ok(Json(category.into()))
}

// GET /api/content - Get content by category (query parameter)
pub async fn get_content(
    State(content_service): State<ContentService>,
    Query(params): Query<ContentQuery>,
    Extension(_claims): Extension<Claims>, // Require authentication
) -> Result<Json<Vec<ContentResponse>>, AppError> {
    let content = if let Some(category_type) = params.category {
        // Validate category type
        if !ContentService::is_valid_category_type(&category_type) {
            return Err(AppError::BadRequest("Invalid category type".to_string()));
        }
        
        // Get category by type first
        let category = content_service
            .get_category_by_type(&category_type)
            .await?
            .ok_or(AppError::NotFound)?;
        
        // Get content for this category
        content_service.get_content_by_category(category.id, None, None).await?
    } else {
        // If no category specified, return empty list
        Vec::new()
    };
    
    let response: Vec<ContentResponse> = content.into_iter().map(Into::into).collect();
    Ok(Json(response))
}

// GET /api/content/:id - Get content by ID
pub async fn get_content_by_id(
    State(content_service): State<ContentService>,
    Path(id): Path<i64>,
    Extension(_claims): Extension<Claims>, // Require authentication
) -> Result<Json<ContentResponse>, AppError> {
    let content = content_service
        .get_content_by_id(id)
        .await?
        .ok_or(AppError::NotFound)?;

    Ok(Json(content.into()))
}

// POST /api/admin/categories - Create new category (admin only)
pub async fn create_category(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateCategoryRequest>,
) -> Result<(StatusCode, Json<CategoryResponse>), AppError> {
    // Only admins can create categories
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let service_request = crate::services::content::CreateCategoryRequest {
        name: request.name,
        category_type: request.category_type,
    };

    let category = content_service.create_category(service_request).await?;
    Ok((StatusCode::CREATED, Json(category.into())))
}

// PUT /api/admin/categories/:id - Update category (admin only)
pub async fn update_category(
    State(content_service): State<ContentService>,
    Path(id): Path<i64>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<UpdateCategoryRequest>,
) -> Result<Json<CategoryResponse>, AppError> {
    // Only admins can update categories
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let service_request = crate::services::content::UpdateCategoryRequest {
        name: request.name,
        category_type: request.category_type,
    };

    let category = content_service.update_category(id, service_request).await?;
    Ok(Json(category.into()))
}

// DELETE /api/admin/categories/:id - Delete category (admin only)
pub async fn delete_category(
    State(content_service): State<ContentService>,
    Path(id): Path<i64>,
    Extension(claims): Extension<Claims>,
) -> Result<StatusCode, AppError> {
    // Only admins can delete categories
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    content_service.delete_category(id).await?;
    Ok(StatusCode::NO_CONTENT)
}

// PUT /api/admin/content/:id/status - Toggle content active status (admin only)
pub async fn toggle_content_status(
    State(content_service): State<ContentService>,
    Path(id): Path<i64>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<ContentResponse>, AppError> {
    // Only admins can toggle content status
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let content = content_service.toggle_content_status(id).await?;
    Ok(Json(content.into()))
}

// DELETE /api/admin/content/:id - Delete content (admin only)
pub async fn delete_content(
    State(content_service): State<ContentService>,
    Path(id): Path<i64>,
    Extension(claims): Extension<Claims>,
) -> Result<StatusCode, AppError> {
    // Only admins can delete content
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    content_service.delete_content(id).await?;
    Ok(StatusCode::NO_CONTENT)
}

// PUT /api/content/:id/category - Assign content to category (admin only)
pub async fn assign_content_to_category(
    State(content_service): State<ContentService>,
    Path(content_id): Path<i64>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<AssignCategoryRequest>,
) -> Result<StatusCode, AppError> {
    // Only admins can assign content to categories
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    content_service
        .assign_content_to_category(content_id, request.category_id)
        .await?;

    Ok(StatusCode::OK)
}

// POST /api/admin/content - Create content manually (admin only)
pub async fn create_content_manual(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateContentRequest>,
) -> Result<(StatusCode, Json<ContentResponse>), AppError> {
    // Only admins can create content
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    println!("DEBUG: create_content_manual received: category_id={}, title={}, tmdb_id={:?}, backdrop_url={:?}",
             request.category_id, request.title, request.tmdb_id, request.backdrop_url);

    let service_request = crate::services::content::CreateContentRequest {
        category_id: request.category_id,
        title: request.title,
        description: request.description,
        image_url: request.image_url,
        backdrop_url: request.backdrop_url,
        stream_url: request.stream_url,
        tmdb_id: request.tmdb_id,
        year: request.year,
        provider: None,
        country: None,
        format: None,
        channel_number: None,
        is_live: None,
        drm_keys: None,
    };

    let content = content_service.create_content_manual(service_request).await?;
    Ok((StatusCode::CREATED, Json(content.into())))
}

// POST /api/admin/content/tmdb - Create content from TMDB (admin only)
pub async fn create_content_from_tmdb(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateContentFromTmdbRequest>,
) -> Result<(StatusCode, Json<ContentResponse>), AppError> {
    // Only admins can create content
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let service_request = crate::services::content::CreateContentFromTmdbRequest {
        category_id: request.category_id,
        tmdb_id: request.tmdb_id,
        media_type: request.media_type,
        stream_url: request.stream_url,
    };

    let content = content_service.create_content_from_tmdb(service_request).await?;
    Ok((StatusCode::CREATED, Json(content.into())))
}

// POST /api/admin/movies - Create movie (admin only)
pub async fn create_movie(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateMovieRequest>,
) -> Result<(StatusCode, Json<ContentResponse>), AppError> {
    // Only admins can create movies
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let service_request = crate::services::content::CreateMovieRequest {
        title: request.title,
        description: request.description,
        image_url: request.image_url,
        stream_url: request.stream_url,
        tmdb_id: request.tmdb_id,
        year: request.year,
        runtime: request.runtime,
    };

    let content = content_service.create_movie(service_request).await?;
    Ok((StatusCode::CREATED, Json(content.into())))
}

// POST /api/admin/tv-channels - Create TV channel (admin only)
pub async fn create_tv_channel(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateTvChannelRequest>,
) -> Result<(StatusCode, Json<ContentResponse>), AppError> {
    println!("DEBUG: create_tv_channel received: name={}, stream_url={}, drm_keys={:?}",
             request.name, request.stream_url, request.drm_keys);

    // Only admins can create TV channels
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    // Validate DRM keys if provided and not empty
    if let Some(ref drm_keys) = request.drm_keys {
        if !drm_keys.trim().is_empty() {
            DrmService::validate_hex_keys(drm_keys)?;
        }
    }

    let service_request = crate::services::content::CreateTvChannelRequest {
        name: request.name,
        description: request.description,
        logo_url: request.logo_url,
        stream_url: request.stream_url,
        channel_number: request.channel_number,
        is_live: request.is_live,
        provider: request.provider,
        country: request.country,
        format: request.format,
        drm_keys: request.drm_keys,
    };

    let content = content_service.create_tv_channel(service_request).await?;
    Ok((StatusCode::CREATED, Json(content.into())))
}

// PUT /api/admin/tv-channels/{id} - Update TV channel (admin only)
pub async fn update_tv_channel(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Path(id): Path<i64>,
    Json(request): Json<CreateTvChannelRequest>,
) -> Result<Json<ContentResponse>, AppError> {
    // Only admins can update TV channels
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    // Validate DRM keys if provided
    if let Some(ref drm_keys) = request.drm_keys {
        if !drm_keys.trim().is_empty() {
            DrmService::validate_hex_keys(drm_keys)?;
        }
    }

    let service_request = crate::services::content::UpdateTvChannelRequest {
        name: request.name,
        description: request.description,
        logo_url: request.logo_url,
        stream_url: request.stream_url,
        channel_number: request.channel_number,
        is_live: request.is_live,
        provider: request.provider,
        country: request.country,
        format: request.format,
        drm_keys: request.drm_keys,
        active: request.active,
    };

    let content = content_service.update_tv_channel(id, service_request).await?;
    Ok(Json(content.into()))
}

// PUT /api/admin/content/:id - Update content (admin only)
pub async fn update_content(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Path(content_id): Path<i32>,
    Json(request): Json<UpdateContentRequest>,
) -> Result<Json<ContentResponse>, AppError> {
    // Only admins can update content
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let service_request = crate::services::content::UpdateContentRequest {
        title: request.title,
        description: request.description,
        image_url: request.image_url,
        backdrop_url: request.backdrop_url,
        stream_url: request.stream_url,
        provider: request.provider,
        country: request.country,
        format: request.format,
        channel_number: request.channel_number,
        is_live: request.is_live,
        drm_keys: request.drm_keys,
        active: request.active,
    };

    let content = content_service.update_content(content_id, service_request).await?;
    Ok(Json(content.into()))
}

// GET /api/movies - Get movies with pagination
pub async fn get_movies(
    State(content_service): State<ContentService>,
    Query(params): Query<PaginationQuery>,
    Extension(_claims): Extension<Claims>,
) -> Result<Json<Vec<ContentResponse>>, AppError> {
    let movies = content_service.get_movies(params.page, params.limit).await?;
    let response: Vec<ContentResponse> = movies.into_iter().map(Into::into).collect();
    Ok(Json(response))
}

// GET /api/tv-channels - Get TV channels with pagination
pub async fn get_tv_channels(
    State(content_service): State<ContentService>,
    Query(params): Query<PaginationQuery>,
    Extension(_claims): Extension<Claims>,
) -> Result<Json<Vec<ContentResponse>>, AppError> {
    let channels = content_service.get_tv_channels(params.page, params.limit).await?;
    let response: Vec<ContentResponse> = channels.into_iter().map(Into::into).collect();
    Ok(Json(response))
}

// GET /api/content/search - Search content
pub async fn search_content(
    State(content_service): State<ContentService>,
    Query(params): Query<SearchQuery>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<Vec<ContentResponse>>, AppError> {
    // Verify user access
    verify_user_access(&content_service, &claims, None).await?;

    let content = content_service.search_content(&params.q, params.category.as_deref()).await?;
    let response: Vec<ContentResponse> = content.into_iter().map(Into::into).collect();
    Ok(Json(response))
}

// GET /api/content/:id/stream - Get content with decrypted URL for delivery (Req 8.2)
pub async fn get_content_for_delivery(
    State(content_service): State<ContentService>,
    Path(id): Path<i64>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<ContentDeliveryResponse>, AppError> {
    // First get content to check category
    let content_info = content_service
        .get_content_by_id(id)
        .await?
        .ok_or(AppError::NotFound)?;

    // Get category to check access permissions
    let category = content_service.get_category_by_id(content_info.category_id).await?
        .ok_or(AppError::NotFound)?;

    // Verify user access including category-specific permissions
    verify_user_access(&content_service, &claims, Some(&category.category_type)).await?;

    // Check parental control for XXX category (ID = 3)
    if category.id == 3 {
        if !content_service.verify_parental_access(claims.sub.parse::<i64>().unwrap_or(0)).await? {
            return Err(AppError::ParentalControlRequired);
        }
    }

    // Now get content with decrypted URL
    let content = content_service
        .get_content_for_delivery(id)
        .await?
        .ok_or(AppError::NotFound)?;

    // Get original hex keys and convert to ExoPlayer format
    let (drm_keys_hex, drm_keys_exo) = if let Some(ref hex_keys) = content.drm_keys {
        if !hex_keys.trim().is_empty() {
            let exo_keys = DrmService::convert_hex_keys_to_clearkey_json(hex_keys)?;
            (Some(hex_keys.clone()), Some(exo_keys))
        } else {
            (None, None)
        }
    } else {
        (None, None)
    };

    let response = ContentDeliveryResponse {
        id: content.id,
        category_id: content.category_id,
        title: content.title,
        description: content.description,
        image_url: content.image_url,
        stream_url: content.stream_url,
        tmdb_id: content.tmdb_id,
        year: content.year,
        channel_number: content.channel_number,
        created_at: content.created_at,
        drm_keys: drm_keys_hex,
        drm_keys_exo,
    };

    Ok(Json(response))
}

// GET /api/content/:id/stream-tracked - Get content with decrypted URL for delivery with activity tracking
pub async fn get_content_for_delivery_with_tracking(
    State(app_state): State<crate::AppState>,
    headers: HeaderMap,
    connect_info: Option<ConnectInfo<SocketAddr>>,
    Path(id): Path<i64>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<ContentDeliveryResponse>, AppError> {
    let content_service = &app_state.content_service;

    // First get content to check category
    let content_info = content_service
        .get_content_by_id(id)
        .await?
        .ok_or(AppError::NotFound)?;

    // Get category to check access permissions
    let category = content_service.get_category_by_id(content_info.category_id).await?
        .ok_or(AppError::NotFound)?;

    // Verify user access including category-specific permissions
    verify_user_access(&content_service, &claims, Some(&category.category_type)).await?;

    // Check parental control for XXX category (ID = 3)
    if category.id == 3 {
        if !content_service.verify_parental_access(claims.sub.parse::<i64>().unwrap_or(0)).await? {
            return Err(AppError::ParentalControlRequired);
        }
    }

    // Now get content with decrypted URL
    let content = content_service
        .get_content_for_delivery(id)
        .await?
        .ok_or(AppError::NotFound)?;

    // Get original hex keys and convert to ExoPlayer format
    let (drm_keys_hex, drm_keys_exo) = if let Some(ref hex_keys) = content.drm_keys {
        if !hex_keys.trim().is_empty() {
            let exo_keys = DrmService::convert_hex_keys_to_clearkey_json(hex_keys)?;
            (Some(hex_keys.clone()), Some(exo_keys))
        } else {
            (None, None)
        }
    } else {
        (None, None)
    };

    // Log content viewing activity
    let user_id = claims.sub.parse::<i64>().unwrap_or(0);
    let content_type = if category.category_type == "TV" {
        ContentType::LiveTv
    } else {
        ContentType::Movie
    };

    if let Err(e) = user_activity::log_content_viewing(
        &app_state,
        user_id,
        &claims.username,
        content_type,
        Some(content.id),
        &content.title,
        None, // episode_title
        None, // season_number
        None, // episode_number
        &headers,
        connect_info,
    ).await {
        tracing::warn!("Failed to log content viewing activity: {}", e);
    }

    let response = ContentDeliveryResponse {
        id: content.id,
        category_id: content.category_id,
        title: content.title,
        description: content.description,
        image_url: content.image_url,
        stream_url: content.stream_url,
        tmdb_id: content.tmdb_id,
        year: content.year,
        channel_number: content.channel_number,
        created_at: content.created_at,
        drm_keys: drm_keys_hex,
        drm_keys_exo,
    };

    Ok(Json(response))
}

// GET /api/admin/content/{id}/edit - Get content for editing (admin only)
pub async fn get_content_for_edit(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Path(id): Path<i64>,
) -> Result<Json<ContentResponse>, AppError> {
    // Only admins can edit content
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let content = content_service.get_content_for_edit(id).await?;

    // Debug log to see what we're returning
    println!("DEBUG: get_content_for_edit returning: id={}, backdrop_url={:?}, provider={:?}, country={:?}, format={:?}, drm_keys={:?}",
        content.id, content.backdrop_url, content.provider, content.country, content.format, content.drm_keys);

    // Create response with decrypted URL mapped to video_url field
    let response = ContentResponse {
        id: content.id,
        category_id: content.category_id,
        title: content.title,
        description: content.description,
        poster_url: content.image_url.clone(), // Map image_url to poster_url for frontend
        image_url: content.image_url, // Direct mapping for Android compatibility
        backdrop_url: content.backdrop_url, // Now supported!
        video_url: content.encrypted_stream_url, // This is now decrypted from get_content_for_edit
        tmdb_id: content.tmdb_id,
        year: content.year,
        runtime: None, // Not in Content model
        channel_number: content.channel_number,
        is_live: content.is_live,
        active: content.active,
        created_at: content.created_at.to_rfc3339(),
        content_type: match content.category_id {
            1 => "vod".to_string(),    // VOD
            2 => "tv".to_string(),     // TV
            3 => "xxx".to_string(),    // XXX
            4 => "novelas".to_string(), // Novelas
            5 => "events".to_string(),  // Eventos
            6 => "kids".to_string(),    // Kids
            7 => "anime".to_string(),   // Anime
            8 => "series".to_string(),  // Series
            _ => "vod".to_string(),     // Default
        },
        // TV Channel specific fields
        provider: content.provider,
        country: content.country,
        format: content.format,
        drm_keys: content.drm_keys,
    };

    println!("DEBUG: ContentResponse backdrop_url={:?}, provider={:?}, country={:?}, format={:?}, drm_keys={:?}",
        response.backdrop_url, response.provider, response.country, response.format, response.drm_keys);

    Ok(Json(response))
}

// GET /api/admin/content/:id/edit-with-seasons - Get content for editing with total_seasons
pub async fn get_content_with_seasons_for_edit(
    State(content_service): State<ContentService>,
    Extension(claims): Extension<Claims>,
    Path(id): Path<i64>,
) -> Result<Json<serde_json::Value>, AppError> {
    // Only admins can edit content
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let content_json = content_service.get_content_with_seasons_for_edit(id).await?;
    Ok(Json(content_json))
}

// GET /api/delivery/content/category/:id - Enhanced content listing by category (Req 4.2, 4.3, 4.4)
pub async fn get_enhanced_content_by_category(
    State(content_service): State<ContentService>,
    Path(category_id): Path<i64>,
    Query(params): Query<PaginationQuery>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<ContentListResponse>, AppError> {
    // Check authorization
    if claims.role != UserRole::Admin && claims.role != UserRole::Reseller && claims.role != UserRole::EndUser {
        return Err(AppError::Forbidden);
    }

    // Verify user access
    verify_user_access(&content_service, &claims, None).await?;

    // Get category info
    let category = content_service.get_category_by_id(category_id).await?
        .ok_or(AppError::NotFound)?;

    // Verify access to specific category types
    verify_user_access(&content_service, &claims, Some(&category.category_type)).await?;

    // Check parental control for XXX category (ID = 3)
    if category.id == 3 {
        if !content_service.verify_parental_access(claims.sub.parse::<i64>().unwrap_or(0)).await? {
            return Err(AppError::ParentalControlRequired);
        }
    }

    let page = params.page.unwrap_or(1).max(1);
    let limit = params.limit.unwrap_or(10).min(50).max(1);

    let content = content_service.get_content_by_category(category_id, Some(page), Some(limit)).await?;
    let total_content = content_service.count_content_by_category(category_id).await?;

    let enhanced_content: Vec<EnhancedContentResponse> = content.into_iter().map(|c| {
        EnhancedContentResponse {
            id: c.id,
            category: CategoryResponse::from(category.clone()),
            title: c.title,
            description: c.description,
            image_url: c.image_url,
            backdrop_url: c.backdrop_url,
            tmdb_id: c.tmdb_id,
            year: c.year,
            created_at: c.created_at.to_rfc3339(),
        }
    }).collect();

    let has_more = i64::from(page * limit) < total_content;

    let response = ContentListResponse {
        content: enhanced_content,
        total: total_content,
        page,
        limit,
        has_more,
    };

    Ok(Json(response))
}

// GET /api/delivery/content/search - Enhanced search with better filtering (Req 1.3, 1.4)
pub async fn enhanced_search_content(
    State(content_service): State<ContentService>,
    Query(params): Query<EnhancedSearchQuery>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<ContentListResponse>, AppError> {
    // Verify user access
    verify_user_access(&content_service, &claims, None).await?;

    let page = params.page.unwrap_or(1).max(1);
    let limit = params.limit.unwrap_or(10).min(50).max(1);

    let content = content_service.enhanced_search_content(&params.q, params.category.as_deref(), params.year, params.tmdb_id).await?;
    let total_content = content.len() as i64;

    // Apply pagination
    let start = ((page - 1) * limit) as usize;
    let end = (start + limit as usize).min(content.len());
    let paginated_content = content[start..end].to_vec();

    // Preload categories for the paginated content to avoid N+1 lookups
    let mut category_cache: std::collections::HashMap<i64, Category> = std::collections::HashMap::new();
    let unique_ids: std::collections::HashSet<i64> = paginated_content.iter().map(|c| c.category_id).collect();
    for id in unique_ids {
        if let Some(cat) = content_service.get_category_by_id(id).await? {
            category_cache.insert(id, cat);
        }
    }

    let enhanced_content: Vec<EnhancedContentResponse> = paginated_content.into_iter().map(|c| {
        // Get category info for each content
        let category = if let Some(cat) = category_cache.get(&c.category_id) {
            CategoryResponse::from(cat.clone())
        } else {
            CategoryResponse {
                id: c.category_id,
                name: "Unknown".to_string(),
                category_type: "Unknown".to_string(),
                created_at: chrono::Utc::now().to_rfc3339(),
            }
        };

        EnhancedContentResponse {
            id: c.id,
            category,
            title: c.title,
            description: c.description,
            image_url: c.image_url,
            backdrop_url: c.backdrop_url,
            tmdb_id: c.tmdb_id,
            year: c.year,
            created_at: c.created_at.to_rfc3339(),
        }
    }).collect();

    let has_more = i64::from(page * limit) < total_content;

    let response = ContentListResponse {
        content: enhanced_content,
        total: total_content,
        page,
        limit,
        has_more,
    };

    Ok(Json(response))
}

pub fn content_routes() -> Router<ContentService> {
    Router::new()
        .route("/categories", get(get_categories))
        .route("/categories/:id", get(get_category_by_id))
        .route("/", get(get_content))
        .route("/:id", get(get_content_by_id))
        .route("/:id/category", put(assign_content_to_category))
        .route("/:id/stream", get(get_content_for_delivery))
        .route("/search", get(search_content))
}

// Enhanced delivery routes for better content access control (Req 1.3, 1.4, 4.2, 4.3, 4.4)
pub fn delivery_routes() -> Router<ContentService> {
    Router::new()
        .route("/content/category/:id", get(get_enhanced_content_by_category))
        .route("/content/search", get(enhanced_search_content))
}

pub fn movies_routes() -> Router<ContentService> {
    Router::new()
        .route("/", get(get_movies))
}

pub fn tv_routes() -> Router<ContentService> {
    Router::new()
        .route("/channels", get(get_tv_channels))
}

// GET /api/admin/content - Get content for admin with filtering (admin only)
pub async fn admin_get_content(
    State(content_service): State<ContentService>,
    Query(params): Query<ContentQuery>,
    Extension(claims): Extension<Claims>,
) -> Result<Json<Vec<ContentResponse>>, AppError> {
    // Only admins can access this endpoint
    if claims.role != UserRole::Admin {
        return Err(AppError::Forbidden);
    }

    let content = if let Some(category_type) = params.category {
        // Get category by type
        let category = content_service
            .get_category_by_type(&category_type)
            .await?
            .ok_or(AppError::NotFound)?;

        // Get content for this category
        content_service.get_content_by_category(category.id, None, None).await?
    } else {
        // If no category specified, get all content
        content_service.get_all_content().await?
    };

    let response: Vec<ContentResponse> = content.into_iter().map(Into::into).collect();
    Ok(Json(response))
}

pub fn admin_content_routes() -> Router<ContentService> {
    Router::new()
        .route("/", axum::routing::get(admin_get_content))
        .route("/categories", axum::routing::post(create_category))
        .route("/categories/:id", axum::routing::put(update_category))
        .route("/categories/:id", axum::routing::delete(delete_category))
        .route("/", axum::routing::post(create_content_manual))
        .route("/:id", axum::routing::put(update_content))
        .route("/:id/status", axum::routing::put(toggle_content_status))
        .route("/:id", axum::routing::delete(delete_content))
        .route("/tmdb", axum::routing::post(create_content_from_tmdb))
        .route("/movies", axum::routing::post(create_movie))
        .route("/tv-channels", axum::routing::post(create_tv_channel))
        .route("/tv-channels/:id", axum::routing::put(update_tv_channel))
        .route("/:id/edit", axum::routing::get(get_content_for_edit))
        .route("/:id/edit-with-seasons", axum::routing::get(get_content_with_seasons_for_edit))
}

