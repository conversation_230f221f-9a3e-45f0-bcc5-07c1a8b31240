use crate::error::AppError;
use crate::models::{
    Category, Content, Series, Season, Episode, SeriesWithDetails, SeasonWithEpisodes, EpisodeWithDecryptedUrl,
    Novela, NovelaChapter, NovelaWithChapters, ChapterWithDecryptedUrl,
    NovelaSeason, NovelaEpisode, NovelaEpisodeWithDecryptedUrl,
    Event, EventSchedule, EventWithSchedules, ScheduleWithDecryptedUrl
};
use crate::services::TmdbService;
use sqlx::SqlitePool;
use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize)]
pub struct CreateCategoryRequest {
    pub name: String,
    pub category_type: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateCategoryRequest {
    pub name: Option<String>,
    pub category_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateContentRequest {
    pub category_id: i64,
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub backdrop_url: Option<String>, // Added backdrop URL for movies and series
    pub stream_url: String,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    // TV Channel specific fields
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>,
    pub channel_number: Option<i32>,
    pub is_live: Option<bool>,
    pub drm_keys: Option<String>, // ClearKey keys in hexadecimal format (key:kid pairs)
}

#[derive(Debug, Deserialize)]
pub struct CreateContentFromTmdbRequest {
    pub category_id: i64,
    pub tmdb_id: i64,
    pub media_type: String, // "movie" or "tv"
    pub stream_url: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateMovieRequest {
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub stream_url: String,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    pub runtime: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CreateTvChannelRequest {
    pub name: String,
    pub description: Option<String>,
    pub logo_url: Option<String>,
    pub stream_url: String,
    pub channel_number: Option<i32>,
    pub is_live: bool,
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>, // 'DRM' or 'M3U8'
    pub drm_keys: Option<String>, // ClearKey keys in hexadecimal format (key:kid pairs)
}

#[derive(Debug, Deserialize)]
pub struct UpdateTvChannelRequest {
    pub name: String,
    pub description: Option<String>,
    pub logo_url: Option<String>,
    pub stream_url: String,
    pub channel_number: Option<i32>,
    pub is_live: bool,
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>, // 'DRM' or 'M3U8'
    pub drm_keys: Option<String>, // ClearKey keys in hexadecimal format (key:kid pairs)
    pub active: Option<bool>,
}

#[derive(Debug)]
pub struct UpdateContentRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub backdrop_url: Option<String>,
    pub stream_url: Option<String>,
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>,
    pub channel_number: Option<i32>,
    pub is_live: Option<bool>,
    pub drm_keys: Option<String>,
    pub active: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct ContentWithDecryptedUrl {
    pub id: i64,
    pub category_id: i64,
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub stream_url: String, // Decrypted URL for delivery
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    pub channel_number: Option<i32>, // Channel number for TV channels
    pub created_at: String,
    pub drm_keys: Option<String>, // ClearKey JSON for ExoPlayer (Base64URL format)
}

// Series management requests
#[derive(Debug, Deserialize)]
pub struct CreateSeriesRequest {
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
    pub total_seasons: i32,
}

#[derive(Debug, Deserialize)]
pub struct CreateSeriesFromTmdbRequest {
    pub tmdb_id: i64,
    pub episode_urls: Option<std::collections::HashMap<String, String>>, // "season-episode" -> "url"
}

#[derive(Debug, Deserialize)]
pub struct CreateAnimeSeriesFromTmdbRequest {
    pub tmdb_id: i64,
    pub episode_urls: Option<std::collections::HashMap<String, String>>, // "season-episode" -> "url"
}

#[derive(Debug, Deserialize)]
pub struct CreateSeasonRequest {
    pub series_id: i64,
    pub season_number: i32,
}

#[derive(Debug, Deserialize)]
pub struct CreateEpisodeRequest {
    pub season_id: i64,
    pub episode_number: i32,
    pub title: String,
    pub description: Option<String>,
    pub stream_url: String,
    pub image_url: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateMultipleEpisodesRequest {
    pub season_id: i64,
    pub episodes: Vec<EpisodeData>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateEpisodeRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub stream_url: Option<String>,
    pub image_url: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct EpisodeData {
    pub episode_number: i32,
    pub title: String,
    pub description: Option<String>,
    pub stream_url: String,
    pub image_url: Option<String>,
}

// Specialized content requests (Req 7.1, 7.2, 7.3, 7.4)

// XXX content requests (Req 7.1)
#[derive(Debug, Deserialize)]
pub struct CreateXxxContentRequest {
    pub name: String,
    pub stream_url: String,
    pub image_url: Option<String>,
}

// Novelas requests (Req 7.2)
#[derive(Debug, Deserialize)]
pub struct CreateNovelaRequest {
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub year: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CreateNovelaFromTmdbRequest {
    pub tmdb_id: i64,
    pub episode_urls: Option<std::collections::HashMap<String, String>>, // "season-episode" -> "url"
}

#[derive(Debug, Deserialize)]
pub struct CreateChapterRequest {
    pub novela_id: i64,
    pub chapter_number: i32,
    pub title: String,
    pub description: Option<String>,
    pub stream_url: String,
    pub image_url: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateChapterRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub stream_url: Option<String>,
    pub image_url: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateMultipleChaptersRequest {
    pub novela_id: i64,
    pub chapters: Vec<ChapterData>,
}

#[derive(Debug, Deserialize)]
pub struct ChapterData {
    pub chapter_number: i32,
    pub title: String,
    pub description: Option<String>,
    pub stream_url: String,
    pub image_url: Option<String>,
}

// Events requests (Req 7.3)
#[derive(Debug, Deserialize)]
pub struct CreateEventRequest {
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub event_type: String, // 'live', 'scheduled', 'recurring'
    pub provider: Option<String>,
    pub country: Option<String>,
    pub format: Option<String>,
    pub drm_keys: Option<String>,
    pub stream_url: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateEventScheduleRequest {
    pub event_id: i64,
    pub schedule_date: chrono::NaiveDate,
    pub start_time: chrono::NaiveTime,
    pub end_time: Option<chrono::NaiveTime>,
    pub stream_url: String,
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
}

// Kids/Anime content requests (Req 7.4)
#[derive(Debug, Deserialize)]
pub struct CreateKidsContentRequest {
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub stream_url: String,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CreateAnimeContentRequest {
    pub title: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub stream_url: String,
    pub tmdb_id: Option<i64>,
    pub year: Option<i32>,
}



#[derive(Clone)]
pub struct ContentService {
    db: SqlitePool,
    tmdb_service: Option<TmdbService>,
}

impl ContentService {
    pub fn new(db: SqlitePool) -> Self {
        Self {
            db,
            tmdb_service: None,
        }
    }

    pub fn with_tmdb_service(mut self, tmdb_service: TmdbService) -> Self {
        self.tmdb_service = Some(tmdb_service);
        self
    }



    // Category management methods
    pub async fn get_all_categories(&self) -> Result<Vec<Category>, AppError> {
        let categories = sqlx::query_as::<_, Category>(
            "SELECT id, name, type as category_type, created_at FROM categories ORDER BY name"
        )
        .fetch_all(&self.db)
        .await?;

        Ok(categories)
    }

    pub async fn get_user_categories(&self, user_id: i64) -> Result<Vec<crate::handlers::admin::CategoryResponse>, AppError> {
        let categories = sqlx::query!(
            r#"
            SELECT c.id, c.name, c.type as category_type
            FROM categories c
            INNER JOIN user_subscriptions us ON c.id = us.category_id
            WHERE us.user_id = ?
            ORDER BY c.name
            "#,
            user_id
        )
        .fetch_all(&self.db)
        .await?;

        let response = categories.into_iter().map(|row| {
            crate::handlers::admin::CategoryResponse {
                id: row.id,
                name: row.name,
                category_type: row.category_type,
            }
        }).collect();

        Ok(response)
    }

    pub async fn get_category_by_id(&self, id: i64) -> Result<Option<Category>, AppError> {
        let category = sqlx::query_as::<_, Category>(
            "SELECT id, name, type as category_type, created_at FROM categories WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.db)
        .await?;

        Ok(category)
    }

    pub async fn get_category_by_type(&self, category_type: &str) -> Result<Option<Category>, AppError> {
        let category = sqlx::query_as::<_, Category>(
            "SELECT id, name, type as category_type, created_at FROM categories WHERE type = ? ORDER BY created_at DESC LIMIT 1"
        )
        .bind(category_type)
        .fetch_optional(&self.db)
        .await?;

        Ok(category)
    }

    // Content management methods
    pub async fn get_content_by_category(&self, category_id: i64, page: Option<i32>, limit: Option<i32>) -> Result<Vec<Content>, AppError> {
        if let (Some(page), Some(limit)) = (page, limit) {
            let page = page.max(1);
            let limit = limit.min(50).max(1);
            let offset = (page - 1) * limit;

            let content = sqlx::query_as::<_, Content>(
                r#"
                SELECT id, category_id, title, description, image_url, backdrop_url,
                       encrypted_stream_url, tmdb_id, year, active, created_at,
                       provider, country, format, channel_number, is_live, drm_keys
                FROM content
                WHERE category_id = ?
                ORDER BY title
                LIMIT ? OFFSET ?
                "#
            )
            .bind(category_id)
            .bind(limit)
            .bind(offset)
            .fetch_all(&self.db)
            .await?;

            Ok(content)
        } else {
            let content = sqlx::query_as::<_, Content>(
                r#"
                SELECT id, category_id, title, description, image_url, backdrop_url,
                       encrypted_stream_url, tmdb_id, year, active, created_at,
                       provider, country, format, channel_number, is_live, drm_keys
                FROM content
                WHERE category_id = ?
                ORDER BY title
                "#
            )
            .bind(category_id)
            .fetch_all(&self.db)
            .await?;

            Ok(content)
        }
    }

    // Count content by category
    pub async fn count_content_by_category(&self, category_id: i64) -> Result<i64, AppError> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM content WHERE category_id = ?"
        )
        .bind(category_id)
        .fetch_one(&self.db)
        .await?;

        Ok(count)
    }

    pub async fn get_content_by_id(&self, id: i64) -> Result<Option<Content>, AppError> {
        let content = sqlx::query_as::<_, Content>(
            r#"
            SELECT id, category_id, title, description, image_url, backdrop_url,
                   encrypted_stream_url, tmdb_id, year, active, created_at,
                   provider, country, format, channel_number, is_live, drm_keys
            FROM content
            WHERE id = ?
            "#
        )
        .bind(id)
        .fetch_optional(&self.db)
        .await?;

        Ok(content)
    }

    pub async fn get_all_content(&self) -> Result<Vec<Content>, AppError> {
        let content = sqlx::query_as::<_, Content>(
            r#"
            SELECT id, category_id, title, description, image_url, backdrop_url,
                   encrypted_stream_url, tmdb_id, year, active, created_at,
                   provider, country, format, channel_number, is_live, drm_keys
            FROM content
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&self.db)
        .await?;

        Ok(content)
    }

    pub async fn assign_content_to_category(&self, content_id: i64, category_id: i64) -> Result<(), AppError> {
        // First verify the category exists
        let category = self.get_category_by_id(category_id).await?;
        if category.is_none() {
            return Err(AppError::NotFound);
        }

        // Update the content's category assignment
        let result = sqlx::query(
            "UPDATE content SET category_id = ? WHERE id = ?"
        )
        .bind(category_id)
        .bind(content_id)
        .execute(&self.db)
        .await?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound);
        }

        Ok(())
    }

    // Category creation and management methods (admin only)
    pub async fn create_category(&self, request: CreateCategoryRequest) -> Result<Category, AppError> {
        // Validate category type
        if !Self::is_valid_category_type(&request.category_type) {
            return Err(AppError::BadRequest(
                "Invalid category type. Must be one of: VOD, TV, XXX, Novelas, Eventos, Kids, Anime".to_string()
            ));
        }

        // Check if category with this name already exists
        let existing = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM categories WHERE name = ?"
        )
        .bind(&request.name)
        .fetch_one(&self.db)
        .await?;

        if existing > 0 {
            return Err(AppError::BadRequest(
                "Category with this name already exists".to_string()
            ));
        }

        // Create the category
        let category = sqlx::query_as::<_, Category>(
            r#"
            INSERT INTO categories (name, type)
            VALUES (?, ?)
            RETURNING id, name, type as category_type, created_at
            "#
        )
        .bind(&request.name)
        .bind(&request.category_type)
        .fetch_one(&self.db)
        .await?;

        Ok(category)
    }

    pub async fn update_category(&self, id: i64, request: UpdateCategoryRequest) -> Result<Category, AppError> {
        // Check if category exists
        let existing = self.get_category_by_id(id).await?;
        if existing.is_none() {
            return Err(AppError::NotFound);
        }

        let existing = existing.unwrap();

        // Prepare update values
        let name = request.name.unwrap_or(existing.name);
        let category_type = request.category_type.unwrap_or(existing.category_type);

        // Validate category type if it's being changed
        if !Self::is_valid_category_type(&category_type) {
            return Err(AppError::BadRequest(
                "Invalid category type. Must be one of: VOD, TV, XXX, Novelas, Eventos, Kids, Anime".to_string()
            ));
        }

        // Check for conflicts with other categories (excluding current one)
        let conflicts = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM categories WHERE name = ? AND id != ?"
        )
        .bind(&name)
        .bind(id)
        .fetch_one(&self.db)
        .await?;

        if conflicts > 0 {
            return Err(AppError::BadRequest(
                "Category with this name already exists".to_string()
            ));
        }

        // Update the category
        let category = sqlx::query_as::<_, Category>(
            r#"
            UPDATE categories
            SET name = ?, type = ?
            WHERE id = ?
            RETURNING id, name, type as category_type, created_at
            "#
        )
        .bind(&name)
        .bind(&category_type)
        .bind(id)
        .fetch_one(&self.db)
        .await?;

        Ok(category)
    }

    pub async fn delete_category(&self, id: i64) -> Result<(), AppError> {
        // Check if category has any content assigned
        let content_count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM content WHERE category_id = ?"
        )
        .bind(id)
        .fetch_one(&self.db)
        .await?;

        if content_count > 0 {
            return Err(AppError::BadRequest(
                "Cannot delete category that has content assigned to it".to_string()
            ));
        }

        // Delete the category
        let result = sqlx::query("DELETE FROM categories WHERE id = ?")
            .bind(id)
            .execute(&self.db)
            .await?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound);
        }

        Ok(())
    }

    pub async fn toggle_content_status(&self, id: i64) -> Result<Content, AppError> {
        // First get the current status
        let current_content = sqlx::query_as::<_, Content>(
            "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(AppError::NotFound)?;

        // Toggle the status
        let new_status = !current_content.active;

        sqlx::query(
            "UPDATE content SET active = ? WHERE id = ?"
        )
        .bind(new_status)
        .bind(id)
        .execute(&self.db)
        .await?;

        // Return the updated content
        let updated_content = sqlx::query_as::<_, Content>(
            "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
        )
        .bind(id)
        .fetch_one(&self.db)
        .await?;

        Ok(updated_content)
    }

    // Update content (Req 4.1, 4.2)
    pub async fn update_content(&self, id: i32, request: UpdateContentRequest) -> Result<Content, AppError> {
        // Check if content exists
        let existing_content = sqlx::query_as::<_, Content>(
            "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.db)
        .await?;

        let content = existing_content.ok_or(AppError::NotFound)?;

        // Prepare update fields
        let title = request.title.unwrap_or(content.title);
        let description = request.description.or(content.description);
        let image_url = request.image_url.or(content.image_url);
        let backdrop_url = request.backdrop_url.or(content.backdrop_url);
        let provider = request.provider.or(content.provider);
        let country = request.country.or(content.country);
        let format = request.format.or(content.format);
        let channel_number = request.channel_number.or(content.channel_number);
        let is_live = request.is_live.or(content.is_live);
        let drm_keys = request.drm_keys.or(content.drm_keys);
        let active = request.active.unwrap_or(content.active);

        // Handle stream URL directly (no encryption)
        let encrypted_stream_url = if let Some(stream_url) = request.stream_url {
            stream_url
        } else {
            content.encrypted_stream_url
        };

        // Update the content
        sqlx::query(
            r#"
            UPDATE content
            SET title = ?, description = ?, image_url = ?, backdrop_url = ?, encrypted_stream_url = ?,
                provider = ?, country = ?, format = ?, channel_number = ?, is_live = ?, drm_keys = ?, active = ?
            WHERE id = ?
            "#
        )
        .bind(&title)
        .bind(&description)
        .bind(&image_url)
        .bind(&backdrop_url)
        .bind(&encrypted_stream_url)
        .bind(&provider)
        .bind(&country)
        .bind(&format)
        .bind(channel_number)
        .bind(is_live)
        .bind(&drm_keys)
        .bind(active)
        .bind(id)
        .execute(&self.db)
        .await?;

        // Return the updated content
        let updated_content = sqlx::query_as::<_, Content>(
            "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
        )
        .bind(id)
        .fetch_one(&self.db)
        .await?;

        Ok(updated_content)
    }

    pub async fn delete_content(&self, id: i64) -> Result<(), AppError> {
        // First check if content exists
        let content = sqlx::query_as::<_, Content>(
            "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.db)
        .await?;

        let content = content.ok_or(AppError::NotFound)?;

        // Get category to determine content type
        let category = self.get_category_by_id(content.category_id).await?
            .ok_or(AppError::NotFound)?;

        // Delete related records based on content type
        match category.category_type.as_str() {
            "Series" | "Anime" => {
                // Delete episodes first, then seasons, then series
                if let Ok(Some(series)) = sqlx::query_as::<_, Series>(
                    "SELECT id, content_id, total_seasons, created_at FROM series WHERE content_id = ?"
                )
                .bind(id)
                .fetch_optional(&self.db)
                .await {
                    // Delete episodes
                    sqlx::query("DELETE FROM episodes WHERE season_id IN (SELECT id FROM seasons WHERE series_id = ?)")
                        .bind(series.id)
                        .execute(&self.db)
                        .await?;

                    // Delete seasons
                    sqlx::query("DELETE FROM seasons WHERE series_id = ?")
                        .bind(series.id)
                        .execute(&self.db)
                        .await?;

                    // Delete series
                    sqlx::query("DELETE FROM series WHERE content_id = ?")
                        .bind(id)
                        .execute(&self.db)
                        .await?;
                }
            },
            "Novelas" => {
                // Delete novela episodes, seasons, chapters, and novela
                if let Ok(Some(novela)) = sqlx::query_as::<_, Novela>(
                    "SELECT id, content_id, title, description, image_url, total_chapters, created_at FROM novelas WHERE content_id = ?"
                )
                .bind(id)
                .fetch_optional(&self.db)
                .await {
                    // Delete novela episodes
                    sqlx::query("DELETE FROM novela_episodes WHERE novela_season_id IN (SELECT id FROM novela_seasons WHERE novela_id = ?)")
                        .bind(novela.id)
                        .execute(&self.db)
                        .await?;

                    // Delete novela seasons
                    sqlx::query("DELETE FROM novela_seasons WHERE novela_id = ?")
                        .bind(novela.id)
                        .execute(&self.db)
                        .await?;

                    // Delete novela chapters (old system)
                    sqlx::query("DELETE FROM novela_chapters WHERE novela_id = ?")
                        .bind(novela.id)
                        .execute(&self.db)
                        .await?;

                    // Delete novela
                    sqlx::query("DELETE FROM novelas WHERE content_id = ?")
                        .bind(id)
                        .execute(&self.db)
                        .await?;
                }
            },
            _ => {
                // For other content types, no special handling needed
            }
        }

        // Finally delete the content
        let result = sqlx::query("DELETE FROM content WHERE id = ?")
            .bind(id)
            .execute(&self.db)
            .await?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound);
        }

        Ok(())
    }

    // Content creation methods with TMDB integration
    pub async fn create_content_manual(&self, request: CreateContentRequest) -> Result<Content, AppError> {
        // Verify the category exists
        let category = self.get_category_by_id(request.category_id).await?;
        if category.is_none() {
            return Err(AppError::NotFound);
        }

        // Store stream URL directly (no encryption)
        let encrypted_stream_url = request.stream_url;

        // Create the content
        let content = sqlx::query_as::<_, Content>(
            r#"
            INSERT INTO content (category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, provider, country, format, channel_number, is_live, drm_keys)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys
            "#
        )
        .bind(request.category_id)
        .bind(&request.title)
        .bind(&request.description)
        .bind(&request.image_url)
        .bind(&request.backdrop_url)
        .bind(&encrypted_stream_url)
        .bind(request.tmdb_id)
        .bind(request.year)
        .bind(true) // Default to active
        .bind(&request.provider)
        .bind(&request.country)
        .bind(&request.format)
        .bind(request.channel_number)
        .bind(request.is_live)
        .bind(&request.drm_keys)
        .fetch_one(&self.db)
        .await?;

        Ok(content)
    }

    pub async fn create_content_from_tmdb(&self, request: CreateContentFromTmdbRequest) -> Result<Content, AppError> {
        // Verify the category exists
        let category = self.get_category_by_id(request.category_id).await?;
        if category.is_none() {
            return Err(AppError::NotFound);
        }

        // Get TMDB service or return error
        let tmdb_service = self.tmdb_service.as_ref()
            .ok_or_else(|| AppError::BadRequest("TMDB service not available".to_string()))?;

        // Check if TMDB is available
        if !tmdb_service.is_available().await? {
            return Err(AppError::BadRequest("TMDB API not configured".to_string()));
        }

        // Fetch details from TMDB based on media type
        let (title, description, image_url, backdrop_url, year) = match request.media_type.as_str() {
            "movie" => {
                let movie = tmdb_service.get_movie_details(request.tmdb_id).await?;
                let year = movie.release_date.as_ref().and_then(|date| TmdbService::extract_year(date));
                let poster_url = movie.poster_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w500"));
                let backdrop_url = movie.backdrop_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w1280"));

                println!("DEBUG: TMDB movie backdrop_path={:?}, backdrop_url={:?}", movie.backdrop_path, backdrop_url);

                (movie.title, movie.overview, poster_url, backdrop_url, year)
            },
            "tv" => {
                let tv = tmdb_service.get_tv_details(request.tmdb_id).await?;
                let year = tv.first_air_date.as_ref().and_then(|date| TmdbService::extract_year(date));
                let poster_url = tv.poster_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w500"));
                let backdrop_url = tv.backdrop_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w1280"));
                (tv.name, tv.overview, poster_url, backdrop_url, year)
            },
            _ => return Err(AppError::BadRequest("Invalid media type. Must be 'movie' or 'tv'".to_string())),
        };

        // Store stream URL directly (no encryption)
        let encrypted_stream_url = request.stream_url;

        println!("DEBUG: About to insert content with backdrop_url={:?}", backdrop_url);

        // Create the content with TMDB data
        let content = sqlx::query_as::<_, Content>(
            r#"
            INSERT INTO content (category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys
            "#
        )
        .bind(request.category_id)
        .bind(&title)
        .bind(&description)
        .bind(&image_url)
        .bind(&backdrop_url)
        .bind(&encrypted_stream_url)
        .bind(Some(request.tmdb_id))
        .bind(year)
        .bind(true) // Default to active
        .fetch_one(&self.db)
        .await?;

        Ok(content)
    }

    // Movie-specific content creation (Req 4.2, 7.1)
    pub async fn create_movie(&self, request: CreateMovieRequest) -> Result<Content, AppError> {
        // Get VOD category
        let vod_category = self.get_category_by_type("VOD").await?
            .ok_or_else(|| AppError::BadRequest("VOD category not found".to_string()))?;

        // Create content request
        let content_request = CreateContentRequest {
            category_id: vod_category.id,
            title: request.title,
            description: request.description,
            image_url: request.image_url,
            backdrop_url: None, // Movies can have backdrop_url but not provided in this request
            stream_url: request.stream_url,
            tmdb_id: request.tmdb_id,
            year: request.year,
            provider: None,
            country: None,
            format: None,
            channel_number: None,
            is_live: None,
            drm_keys: None,
        };

        self.create_content_manual(content_request).await
    }

    // TV Channel creation (Req 4.2, 7.1)
    pub async fn create_tv_channel(&self, request: CreateTvChannelRequest) -> Result<Content, AppError> {
        // Get TV category
        let tv_category = self.get_category_by_type("TV").await?
            .ok_or_else(|| AppError::BadRequest("TV category not found".to_string()))?;

        // Create content request for TV channel
        let content_request = CreateContentRequest {
            category_id: tv_category.id,
            title: request.name,
            description: request.description,
            image_url: request.logo_url,
            backdrop_url: None, // TV channels don't use backdrop
            stream_url: request.stream_url,
            tmdb_id: None, // TV channels don't have TMDB IDs
            year: None,
            provider: request.provider,
            country: request.country,
            format: request.format,
            channel_number: request.channel_number,
            is_live: Some(request.is_live),
            drm_keys: request.drm_keys,
        };

        self.create_content_manual(content_request).await
    }

    /// Update TV channel
    pub async fn update_tv_channel(&self, channel_id: i64, request: UpdateTvChannelRequest) -> Result<Content, AppError> {
        // Check if content exists and is a TV channel
        let existing_content = self.get_content_by_id(channel_id).await?
            .ok_or(AppError::NotFound)?;

        // Get TV category to verify this is a TV channel
        let tv_category = self.get_category_by_type("TV").await?
            .ok_or_else(|| AppError::BadRequest("TV category not found".to_string()))?;

        if existing_content.category_id != tv_category.id {
            return Err(AppError::BadRequest("Content is not a TV channel".to_string()));
        }

        // Store stream URL directly (no encryption)
        let encrypted_stream_url = request.stream_url.clone();

        // Update the content
        let updated_content = sqlx::query_as::<_, Content>(
            r#"
            UPDATE content
            SET title = ?, description = ?, image_url = ?, encrypted_stream_url = ?,
                provider = ?, country = ?, format = ?, channel_number = ?,
                is_live = ?, drm_keys = ?, active = ?
            WHERE id = ?
            RETURNING id, category_id, title, description, image_url, encrypted_stream_url,
                      tmdb_id, year, active, created_at, provider, country, format,
                      channel_number, is_live, drm_keys
            "#
        )
        .bind(&request.name)
        .bind(&request.description)
        .bind(&request.logo_url)
        .bind(&encrypted_stream_url)
        .bind(&request.provider)
        .bind(&request.country)
        .bind(&request.format)
        .bind(request.channel_number)
        .bind(request.is_live)
        .bind(&request.drm_keys)
        .bind(request.active.unwrap_or(true))
        .bind(channel_id)
        .fetch_one(&self.db)
        .await?;

        Ok(updated_content)
    }

    // Get content with decrypted URLs for delivery (Req 8.2)
    pub async fn get_content_for_delivery(&self, content_id: i64) -> Result<Option<ContentWithDecryptedUrl>, AppError> {
        let content = self.get_content_by_id(content_id).await?;

        match content {
            Some(content) => {
                // URLs are now direct (no decryption needed)
                let stream_url = content.encrypted_stream_url.clone();

                // Keep DRM keys in original hexadecimal format
                let drm_keys_hex = content.drm_keys.clone();

                let decrypted_content = ContentWithDecryptedUrl {
                    id: content.id,
                    category_id: content.category_id,
                    title: content.title,
                    description: content.description,
                    image_url: content.image_url,
                    stream_url,
                    tmdb_id: content.tmdb_id,
                    year: content.year,
                    channel_number: content.channel_number,
                    created_at: content.created_at.to_rfc3339(),
                    drm_keys: drm_keys_hex,
                };

                Ok(Some(decrypted_content))
            },
            None => Ok(None),
        }
    }

    /// Get content for editing (returns direct URL - no encryption)
    pub async fn get_content_for_edit(&self, id: i64) -> Result<Content, AppError> {
        // Get the content - URLs are now direct, no decryption needed
        let content = self.get_content_by_id(id).await?
            .ok_or(AppError::NotFound)?;

        Ok(content)
    }

    /// Get content with total_seasons for editing (specifically for series/novelas)
    pub async fn get_content_with_seasons_for_edit(&self, id: i64) -> Result<serde_json::Value, AppError> {
        // Get the content - URLs are now direct
        let content = self.get_content_for_edit(id).await?;

        // Create base content response
        let mut content_json = serde_json::json!({
            "id": content.id,
            "category_id": content.category_id,
            "title": content.title,
            "description": content.description,
            "image_url": content.image_url,
            "backdrop_url": content.backdrop_url,
            "encrypted_stream_url": content.encrypted_stream_url, // Direct URL now
            "tmdb_id": content.tmdb_id,
            "year": content.year,
            "active": content.active,
            "created_at": content.created_at,
            "provider": content.provider,
            "country": content.country,
            "format": content.format,
            "channel_number": content.channel_number,
            "is_live": content.is_live,
            "drm_keys": content.drm_keys
        });

        // For novelas, anime, and series, add total_seasons count
        if content.category_id == 4 { // Novelas category
            // Check if this is a novela with seasons
            let novela = sqlx::query_as::<_, Novela>(
                "SELECT id, content_id, title, description, image_url, total_chapters, created_at FROM novelas WHERE content_id = ?"
            )
            .bind(id)
            .fetch_optional(&self.db)
            .await?;

            if let Some(novela) = novela {
                // Use the total_chapters from the novela table (what the user set)
                content_json["total_seasons"] = serde_json::Value::Number(serde_json::Number::from(novela.total_chapters));
            }
        } else if content.category_id == 7 || content.category_id == 8 { // Anime (7) and Series (8) categories
            // Check if this is a series/anime with seasons
            let series = sqlx::query_as::<_, Series>(
                "SELECT id, content_id, total_seasons, created_at FROM series WHERE content_id = ?"
            )
            .bind(id)
            .fetch_optional(&self.db)
            .await?;

            if let Some(series) = series {
                // Use the total_seasons from the series table (what the user set)
                println!("Found series with total_seasons: {}", series.total_seasons);
                content_json["total_seasons"] = serde_json::Value::Number(serde_json::Number::from(series.total_seasons));
                println!("Content JSON after adding total_seasons: {}", content_json);
            }
        }

        Ok(content_json)
    }

    // Get movies (VOD content) with pagination
    pub async fn get_movies(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<Content>, AppError> {
        let vod_category = self.get_category_by_type("VOD").await?
            .ok_or_else(|| AppError::BadRequest("VOD category not found".to_string()))?;

        let page = page.unwrap_or(1).max(1);
        let limit = limit.unwrap_or(50).min(500).max(1); // Increased for large movie collections
        let offset = (page - 1) * limit;

        let content = sqlx::query_as::<_, Content>(
            r#"
            SELECT id, category_id, title, description, image_url, backdrop_url,
                   encrypted_stream_url, tmdb_id, year, active, created_at,
                   provider, country, format, channel_number, is_live, drm_keys
            FROM content
            WHERE category_id = ?
            ORDER BY title
            LIMIT ? OFFSET ?
            "#
        )
        .bind(vod_category.id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.db)
        .await?;

        Ok(content)
    }

    // Get TV channels with pagination
    pub async fn get_tv_channels(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<Content>, AppError> {
        let tv_category = self.get_category_by_type("TV").await?
            .ok_or_else(|| AppError::BadRequest("TV category not found".to_string()))?;

        let page = page.unwrap_or(1).max(1);
        let limit = limit.unwrap_or(200).min(500).max(1); // Increased default to 200, max to 500
        let offset = (page - 1) * limit;

        let content = sqlx::query_as::<_, Content>(
            r#"
            SELECT id, category_id, title, description, image_url, backdrop_url,
                   encrypted_stream_url, tmdb_id, year, active, created_at,
                   provider, country, format, channel_number, is_live, drm_keys
            FROM content
            WHERE category_id = ?
            ORDER BY title
            LIMIT ? OFFSET ?
            "#
        )
        .bind(tv_category.id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.db)
        .await?;

        Ok(content)
    }

    // Search content by title
    pub async fn search_content(&self, query: &str, category_type: Option<&str>) -> Result<Vec<Content>, AppError> {
        let search_pattern = format!("%{}%", query);

        let content = if let Some(cat_type) = category_type {
            // Validate category type
            if !Self::is_valid_category_type(cat_type) {
                return Err(AppError::BadRequest("Invalid category type".to_string()));
            }

            let category = self.get_category_by_type(cat_type).await?
                .ok_or_else(|| AppError::BadRequest("Category not found".to_string()))?;

            sqlx::query_as::<_, Content>(
                r#"
                SELECT id, category_id, title, description, image_url, backdrop_url,
                       encrypted_stream_url, tmdb_id, year, active, created_at,
                       provider, country, format, channel_number, is_live, drm_keys
                FROM content
                WHERE category_id = ? AND (title LIKE ? OR description LIKE ?)
                ORDER BY title
                LIMIT 50
                "#
            )
            .bind(category.id)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .fetch_all(&self.db)
            .await?
        } else {
            sqlx::query_as::<_, Content>(
                r#"
                SELECT id, category_id, title, description, image_url, backdrop_url,
                       encrypted_stream_url, tmdb_id, year, active, created_at,
                       provider, country, format, channel_number, is_live, drm_keys
                FROM content
                WHERE title LIKE ? OR description LIKE ?
                ORDER BY title
                LIMIT 50
                "#
            )
            .bind(&search_pattern)
            .bind(&search_pattern)
            .fetch_all(&self.db)
            .await?
        };

        Ok(content)
    }

    // Enhanced search content with additional filters (Req 1.3, 1.4)
    pub async fn enhanced_search_content(&self, query: &str, category: Option<&str>, year: Option<i32>, tmdb_id: Option<i64>) -> Result<Vec<Content>, AppError> {
        // Start with basic search
        let content = self.search_content(query, category).await?;

        // Apply additional filters in memory (not ideal for large datasets, but works for this implementation)
        let filtered_content: Vec<Content> = content.into_iter().filter(|c| {
            if let Some(year) = year {
                if c.year != Some(year) {
                    return false;
                }
            }
            if let Some(tmdb_id) = tmdb_id {
                if c.tmdb_id != Some(tmdb_id) {
                    return false;
                }
            }
            true
        }).collect();

        Ok(filtered_content)
    }

    // Get user by ID for access control (Req 1.3, 1.4)
    pub async fn get_user_by_id(&self, user_id: i64) -> Result<crate::models::User, AppError> {
        let user = sqlx::query_as::<_, crate::models::User>(
            r#"SELECT id, username, password_hash, email, role, active, credits, created_by, max_devices,
                      expires_at, created_at, daily_demo_limit, demos_created_today,
                      last_demo_reset_date, is_demo, demo_expires_at, parental_pin
               FROM users WHERE id = ?"#
        )
        .bind(user_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or(AppError::NotFound)?;

        Ok(user)
    }

    // Series management methods (Req 6.1, 6.2, 6.3, 6.4, 6.5, 6.6)

    // Create series manually (Req 6.4, 6.5)
    pub async fn create_series_manual(&self, request: CreateSeriesRequest) -> Result<SeriesWithDetails, AppError> {
        // Get Series category for series
        let series_category = self.get_category_by_type("Series").await?
            .ok_or_else(|| AppError::BadRequest("Series category not found".to_string()))?;

        // Create content entry for the series
        let content_request = CreateContentRequest {
            category_id: series_category.id,
            title: request.title,
            description: request.description,
            image_url: request.image_url,
            backdrop_url: None, // Series can have backdrop but not provided in this request
            stream_url: "".to_string(), // Series don't have direct stream URLs
            tmdb_id: request.tmdb_id,
            year: request.year,
            provider: None,
            country: None,
            format: None,
            channel_number: None,
            is_live: None,
            drm_keys: None,
        };

        let content = self.create_content_manual(content_request).await?;

        // Create series entry
        let series = sqlx::query_as::<_, Series>(
            r#"
            INSERT INTO series (content_id, total_seasons)
            VALUES (?, ?)
            RETURNING id, content_id, total_seasons, created_at
            "#
        )
        .bind(content.id)
        .bind(request.total_seasons)
        .fetch_one(&self.db)
        .await?;

        // Create seasons for the series
        let mut seasons = Vec::new();
        for season_num in 1..=request.total_seasons {
            let season = self.create_season_internal(series.id, season_num).await?;
            seasons.push(SeasonWithEpisodes {
                id: season.id,
                series_id: season.series_id,
                season_number: season.season_number,
                total_episodes: season.total_episodes,
                title: season.title,
                description: season.description,
                poster_url: season.poster_url,
                episodes: Vec::new(),
                created_at: season.created_at,
            });
        }

        Ok(SeriesWithDetails {
            id: series.id,
            content,
            total_seasons: series.total_seasons,
            seasons,
            created_at: series.created_at,
        })
    }

    // Create series from TMDB (Req 6.1, 6.2)
    pub async fn create_series_from_tmdb(&self, request: CreateSeriesFromTmdbRequest) -> Result<SeriesWithDetails, AppError> {
        // Get TMDB service or return error
        let tmdb_service = self.tmdb_service.as_ref()
            .ok_or_else(|| AppError::BadRequest("TMDB service not available".to_string()))?;

        // Check if TMDB is available
        if !tmdb_service.is_available().await? {
            return Err(AppError::BadRequest("TMDB API not configured".to_string()));
        }

        // Fetch TV show details from TMDB
        let tv_details = tmdb_service.get_tv_details(request.tmdb_id).await?;
        let year = tv_details.first_air_date.as_ref().and_then(|date| TmdbService::extract_year(date));
        let poster_url = tv_details.poster_path.map(|path| tmdb_service.build_image_url(&path, "w500"));
        let backdrop_url = tv_details.backdrop_path.map(|path| tmdb_service.build_image_url(&path, "w1280"));

        // Get series category
        let series_category = self.get_category_by_type("Series").await?
            .ok_or_else(|| AppError::BadRequest("Series category not found".to_string()))?;

        // Create content entry for the series
        let content_request = CreateContentRequest {
            category_id: series_category.id,
            title: tv_details.name.clone(),
            description: tv_details.overview.clone(),
            image_url: poster_url.clone(),
            backdrop_url: backdrop_url.clone(),
            stream_url: "".to_string(), // Series don't have direct stream URLs
            tmdb_id: Some(request.tmdb_id),
            year,
            provider: None,
            country: None,
            format: None,
            channel_number: None,
            is_live: None,
            drm_keys: None,
        };

        let content = self.create_content_manual(content_request).await?;

        // Create series entry
        let series = sqlx::query_as::<_, Series>(
            r#"
            INSERT INTO series (content_id, total_seasons)
            VALUES (?, ?)
            RETURNING id, content_id, total_seasons, created_at
            "#
        )
        .bind(content.id)
        .bind(tv_details.number_of_seasons)
        .fetch_one(&self.db)
        .await?;

        // Create seasons with episodes from TMDB
        let mut seasons_with_episodes = Vec::new();
        for season_num in 1..=tv_details.number_of_seasons {
            // Get season details from TMDB first
            let season_details = tmdb_service.get_season_details(request.tmdb_id, season_num).await?;

            // Prepare season data from TMDB
            let season_title = season_details.name.clone();
            let season_description = season_details.overview.clone();
            let season_poster = season_details.poster_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w500"));

            // Create season with TMDB data
            let season = self.create_season_with_tmdb_data(series.id, season_num, season_title, season_description, season_poster).await?;

            // Create episodes for this season
            let mut episodes = Vec::new();
            for episode_data in season_details.episodes {
                let episode_poster = episode_data.still_path.map(|path| tmdb_service.build_image_url(&path, "w500"));

                // Check if we have a URL for this episode
                let episode_key = format!("{}-{}", season_num, episode_data.episode_number);
                let episode_url = request.episode_urls
                    .as_ref()
                    .and_then(|urls| urls.get(&episode_key))
                    .cloned()
                    .unwrap_or_default(); // Empty string if no URL provided

                // Create episode with URL if provided
                let episode = sqlx::query_as::<_, Episode>(
                    r#"
                    INSERT INTO episodes (season_id, episode_number, title, description, encrypted_url, image_url)
                    VALUES (?, ?, ?, ?, ?, ?)
                    RETURNING id, season_id, episode_number, title, description, encrypted_url, image_url, created_at
                    "#
                )
                .bind(season.id)
                .bind(episode_data.episode_number)
                .bind(episode_data.name)
                .bind(episode_data.overview)
                .bind(episode_url) // Use provided URL or empty string
                .bind(episode_poster)
                .fetch_one(&self.db)
                .await?;

                episodes.push(episode);
            }

            // Update season total_episodes count
            let episode_count = episodes.len() as i32;
            sqlx::query!(
                "UPDATE seasons SET total_episodes = ? WHERE id = ?",
                episode_count,
                season.id
            )
            .execute(&self.db)
            .await?;

            seasons_with_episodes.push(SeasonWithEpisodes {
                id: season.id,
                series_id: season.series_id,
                season_number: season.season_number,
                total_episodes: episodes.len() as i32,
                title: season.title,
                description: season.description,
                poster_url: season.poster_url,
                episodes,
                created_at: season.created_at,
            });
        }

        Ok(SeriesWithDetails {
            id: series.id,
            content,
            total_seasons: series.total_seasons,
            seasons: seasons_with_episodes,
            created_at: series.created_at,
        })
    }

    // Create anime series from TMDB (similar to regular series but in anime category)
    pub async fn create_anime_series_from_tmdb(&self, request: CreateAnimeSeriesFromTmdbRequest) -> Result<SeriesWithDetails, AppError> {
        // Get TMDB service or return error
        let tmdb_service = self.tmdb_service.as_ref()
            .ok_or_else(|| AppError::BadRequest("TMDB service not available".to_string()))?;

        // Check if TMDB is available
        if !tmdb_service.is_available().await? {
            return Err(AppError::BadRequest("TMDB service is not available".to_string()));
        }

        // Get anime category
        let anime_category = self.get_category_by_type("Anime").await?
            .ok_or_else(|| AppError::BadRequest("Anime category not found".to_string()))?;

        // Get TV show details from TMDB
        let tv_details = tmdb_service.get_tv_details(request.tmdb_id).await?;

        // Extract year from first air date
        let year = tv_details.first_air_date.as_ref().and_then(|date| TmdbService::extract_year(date));

        // Build poster URL
        let poster_url = tv_details.poster_path.map(|path| tmdb_service.build_image_url(&path, "w500"));
        let backdrop_url = tv_details.backdrop_path.map(|path| tmdb_service.build_image_url(&path, "w1280"));

        // Create content entry for anime series
        let content = sqlx::query_as::<_, Content>(
            r#"
            INSERT INTO content (category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, provider, country, format, channel_number, is_live, drm_keys)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys
            "#
        )
        .bind(anime_category.id)
        .bind(&tv_details.name)
        .bind(&tv_details.overview)
        .bind(&poster_url)
        .bind(&backdrop_url)
        .bind("") // encrypted_stream_url - empty for series
        .bind(request.tmdb_id)
        .bind(year)
        .bind(true) // active
        .bind(None::<String>) // provider
        .bind(None::<String>) // country
        .bind(None::<String>) // format
        .bind(None::<i32>) // channel_number
        .bind(None::<bool>) // is_live
        .bind(None::<String>) // drm_keys
        .fetch_one(&self.db)
        .await?;

        // Create series entry
        let series = sqlx::query_as::<_, Series>(
            r#"
            INSERT INTO series (content_id, total_seasons)
            VALUES (?, ?)
            RETURNING id, content_id, total_seasons, created_at
            "#
        )
        .bind(content.id)
        .bind(tv_details.number_of_seasons)
        .fetch_one(&self.db)
        .await?;

        // Create seasons and episodes with URLs if provided
        let mut seasons_with_episodes = Vec::new();
        for season_number in 1..=tv_details.number_of_seasons {
            // Get season details from TMDB
            let season_details = tmdb_service.get_season_details(request.tmdb_id, season_number).await?;

            // Prepare season data from TMDB
            let season_title = season_details.name.clone();
            let season_description = season_details.overview.clone();
            let season_poster = season_details.poster_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w500"));

            // Create season with TMDB data
            let season = self.create_season_with_tmdb_data(series.id, season_number, season_title, season_description, season_poster).await?;

            // Create episodes for this season
            let mut episodes = Vec::new();
            for episode in season_details.episodes {
                let episode_key = format!("{}-{}", season_number, episode.episode_number);
                let episode_url = request.episode_urls.as_ref()
                    .and_then(|urls| urls.get(&episode_key))
                    .cloned()
                    .unwrap_or_default();

                // Create episode (with or without URL)
                let episode_request = CreateEpisodeRequest {
                    season_id: season.id,
                    episode_number: episode.episode_number,
                    title: episode.name.clone(),
                    description: episode.overview.clone(),
                    stream_url: episode_url, // Can be empty string if no URL provided
                    image_url: episode.still_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w300")),
                };
                let created_episode = self.create_episode(episode_request).await?;
                episodes.push(created_episode);
            }

            seasons_with_episodes.push(SeasonWithEpisodes {
                id: season.id,
                series_id: season.series_id,
                season_number: season.season_number,
                total_episodes: episodes.len() as i32,
                title: season.title,
                description: season.description,
                poster_url: season.poster_url,
                episodes,
                created_at: season.created_at,
            });
        }

        Ok(SeriesWithDetails {
            id: series.id,
            content,
            total_seasons: series.total_seasons,
            seasons: seasons_with_episodes,
            created_at: series.created_at,
        })
    }

    // Internal method to create a season
    async fn create_season_internal(&self, series_id: i64, season_number: i32) -> Result<Season, AppError> {
        let season = sqlx::query_as::<_, Season>(
            r#"
            INSERT INTO seasons (series_id, season_number, total_episodes, title, description, poster_url)
            VALUES (?, ?, 0, ?, ?, ?)
            RETURNING id, series_id, season_number, total_episodes, title, description, poster_url, created_at
            "#
        )
        .bind(series_id)
        .bind(season_number)
        .bind(format!("Season {}", season_number))
        .bind(None::<String>) // description
        .bind(None::<String>) // poster_url
        .fetch_one(&self.db)
        .await?;

        Ok(season)
    }

    // Internal method to create a season with TMDB data
    async fn create_season_with_tmdb_data(&self, series_id: i64, season_number: i32, title: String, description: Option<String>, poster_url: Option<String>) -> Result<Season, AppError> {
        let season = sqlx::query_as::<_, Season>(
            r#"
            INSERT INTO seasons (series_id, season_number, total_episodes, title, description, poster_url)
            VALUES (?, ?, 0, ?, ?, ?)
            RETURNING id, series_id, season_number, total_episodes, title, description, poster_url, created_at
            "#
        )
        .bind(series_id)
        .bind(season_number)
        .bind(title)
        .bind(description)
        .bind(poster_url)
        .fetch_one(&self.db)
        .await?;

        Ok(season)
    }

    // Create season manually
    pub async fn create_season(&self, request: CreateSeasonRequest) -> Result<Season, AppError> {
        // Verify series exists
        let _series = self.get_series_by_id(request.series_id).await?
            .ok_or_else(|| AppError::NotFound)?;

        // Check if season already exists
        let existing = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM seasons WHERE series_id = ? AND season_number = ?"
        )
        .bind(request.series_id)
        .bind(request.season_number)
        .fetch_one(&self.db)
        .await?;

        if existing > 0 {
            return Err(AppError::BadRequest("Season already exists".to_string()));
        }

        self.create_season_internal(request.series_id, request.season_number).await
    }

    // Get series by content ID with full details
    pub async fn get_series_by_content_id(&self, content_id: i64) -> Result<Option<SeriesWithDetails>, AppError> {
        // Get series and content
        let series_data = sqlx::query!(
            r#"
            SELECT s.id, s.content_id, s.total_seasons, s.created_at as series_created_at,
                   c.category_id, c.title, c.description, c.image_url, c.backdrop_url,
                   c.encrypted_stream_url, c.tmdb_id, c.year, c.created_at as content_created_at
            FROM series s
            JOIN content c ON s.content_id = c.id
            WHERE s.content_id = ?
            "#,
            content_id
        )
        .fetch_optional(&self.db)
        .await?;

        match series_data {
            Some(data) => {
                let content = Content {
                    id: data.content_id.unwrap_or(0),
                    category_id: data.category_id.unwrap_or(0),
                    title: data.title,
                    description: data.description,
                    image_url: data.image_url,
                    backdrop_url: data.backdrop_url,
                    encrypted_stream_url: data.encrypted_stream_url.unwrap_or_default(),
                    tmdb_id: data.tmdb_id.map(|x| x as i64),
                    year: data.year.map(|x| x as i32),
                    created_at: chrono::DateTime::from_naive_utc_and_offset(data.content_created_at.unwrap_or_default(), chrono::Utc),
                    provider: None,
                    country: None,
                    active: true,
                    format: None,
                    channel_number: None,
                    is_live: None,
                    drm_keys: None,
                };

                // Get seasons with episodes
                let seasons = self.get_seasons_with_episodes(data.id).await?;

                Ok(Some(SeriesWithDetails {
                    id: data.id,
                    content,
                    total_seasons: data.total_seasons.unwrap_or(0) as i32,
                    seasons,
                    created_at: chrono::DateTime::from_naive_utc_and_offset(data.series_created_at.unwrap_or_default(), chrono::Utc),
                }))
            },
            None => Ok(None),
        }
    }

    // Get series by ID with full details
    pub async fn get_series_by_id(&self, id: i64) -> Result<Option<SeriesWithDetails>, AppError> {
        // Get series and content
        let series_data = sqlx::query!(
            r#"
            SELECT s.id, s.content_id, s.total_seasons, s.created_at as series_created_at,
                   c.category_id, c.title, c.description, c.image_url,
                   c.encrypted_stream_url, c.tmdb_id, c.year, c.created_at as content_created_at
            FROM series s
            JOIN content c ON s.content_id = c.id
            WHERE s.id = ?
            "#,
            id
        )
        .fetch_optional(&self.db)
        .await?;

        match series_data {
            Some(data) => {
                let content = Content {
                    id: data.content_id.unwrap_or(0),
                    category_id: data.category_id.unwrap_or(0),
                    title: data.title,
                    description: data.description,
                    image_url: data.image_url,
                    backdrop_url: None, // Not available in this query
                    encrypted_stream_url: data.encrypted_stream_url.unwrap_or_default(),
                    tmdb_id: data.tmdb_id.map(|x| x as i64),
                    year: data.year.map(|x| x as i32),
                    created_at: chrono::DateTime::from_naive_utc_and_offset(data.content_created_at.unwrap_or_default(), chrono::Utc),
                    provider: None,
                    country: None,
                    active: true,
                    format: None,
                    channel_number: None,
                    is_live: None,
                    drm_keys: None,
                };

                // Get seasons with episodes
                let seasons = self.get_seasons_with_episodes(id).await?;

                Ok(Some(SeriesWithDetails {
                    id: data.id,
                    content,
                    total_seasons: data.total_seasons.unwrap_or(0) as i32,
                    seasons,
                    created_at: chrono::DateTime::from_naive_utc_and_offset(data.series_created_at.unwrap_or_default(), chrono::Utc),
                }))
            },
            None => Ok(None),
        }
    }

    // Get seasons with episodes for a series
    async fn get_seasons_with_episodes(&self, series_id: i64) -> Result<Vec<SeasonWithEpisodes>, AppError> {
        let seasons = sqlx::query_as::<_, Season>(
            "SELECT id, series_id, season_number, total_episodes, title, description, poster_url, created_at FROM seasons WHERE series_id = ? ORDER BY season_number"
        )
        .bind(series_id)
        .fetch_all(&self.db)
        .await?;

        let mut seasons_with_episodes = Vec::new();
        for season in seasons {
            let episodes = sqlx::query_as::<_, Episode>(
                "SELECT id, season_id, episode_number, title, description, encrypted_url, image_url, created_at FROM episodes WHERE season_id = ? ORDER BY episode_number"
            )
            .bind(season.id)
            .fetch_all(&self.db)
            .await?;

            seasons_with_episodes.push(SeasonWithEpisodes {
                id: season.id,
                series_id: season.series_id,
                season_number: season.season_number,
                total_episodes: season.total_episodes,
                title: season.title,
                description: season.description,
                poster_url: season.poster_url,
                episodes,
                created_at: season.created_at,
            });
        }

        Ok(seasons_with_episodes)
    }

    // Episode management methods (Req 6.3, 6.5, 6.6)

    // Create single episode (Req 6.3)
    pub async fn create_episode(&self, request: CreateEpisodeRequest) -> Result<Episode, AppError> {
        // Verify season exists
        let _season = sqlx::query_as::<_, Season>(
            "SELECT id, series_id, season_number, total_episodes, title, description, poster_url, created_at FROM seasons WHERE id = ?"
        )
        .bind(request.season_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or_else(|| AppError::NotFound)?;

        // Check if episode already exists
        let existing = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM episodes WHERE season_id = ? AND episode_number = ?"
        )
        .bind(request.season_id)
        .bind(request.episode_number)
        .fetch_one(&self.db)
        .await?;

        if existing > 0 {
            return Err(AppError::BadRequest("Episode already exists".to_string()));
        }

        // Store stream URL directly (no encryption)
        let encrypted_url = request.stream_url;

        // Create episode
        let episode = sqlx::query_as::<_, Episode>(
            r#"
            INSERT INTO episodes (season_id, episode_number, title, description, encrypted_url, image_url)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING id, season_id, episode_number, title, description, encrypted_url, image_url, created_at
            "#
        )
        .bind(request.season_id)
        .bind(request.episode_number)
        .bind(&request.title)
        .bind(&request.description)
        .bind(&encrypted_url)
        .bind(&request.image_url)
        .fetch_one(&self.db)
        .await?;

        // Update season's total episodes count
        sqlx::query(
            "UPDATE seasons SET total_episodes = (SELECT COUNT(*) FROM episodes WHERE season_id = ?) WHERE id = ?"
        )
        .bind(request.season_id)
        .bind(request.season_id)
        .execute(&self.db)
        .await?;

        Ok(episode)
    }

    // Create multiple episodes for rapid insertion (Req 6.6)
    pub async fn create_multiple_episodes(&self, request: CreateMultipleEpisodesRequest) -> Result<Vec<Episode>, AppError> {
        // Verify season exists
        let _season = sqlx::query_as::<_, Season>(
            "SELECT id, series_id, season_number, total_episodes, title, description, poster_url, created_at FROM seasons WHERE id = ?"
        )
        .bind(request.season_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or_else(|| AppError::NotFound)?;

        let mut created_episodes = Vec::new();

        // Create episodes in a transaction for consistency
        let mut tx = self.db.begin().await?;

        for episode_data in request.episodes {
            // Check if episode already exists
            let existing = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM episodes WHERE season_id = ? AND episode_number = ?"
            )
            .bind(request.season_id)
            .bind(episode_data.episode_number)
            .fetch_one(&mut *tx)
            .await?;

            if existing > 0 {
                return Err(AppError::BadRequest(
                    format!("Episode {} already exists", episode_data.episode_number)
                ));
            }

            // Encrypt the stream URL only if it's not empty
            // Store stream URL directly (no encryption)
            let encrypted_url = episode_data.stream_url;

            // Create episode
            let episode = sqlx::query_as::<_, Episode>(
                r#"
                INSERT INTO episodes (season_id, episode_number, title, description, encrypted_url, image_url)
                VALUES (?, ?, ?, ?, ?, ?)
                RETURNING id, season_id, episode_number, title, description, encrypted_url, image_url, created_at
                "#
            )
            .bind(request.season_id)
            .bind(episode_data.episode_number)
            .bind(&episode_data.title)
            .bind(&episode_data.description)
            .bind(&encrypted_url)
            .bind(&episode_data.image_url)
            .fetch_one(&mut *tx)
            .await?;

            created_episodes.push(episode);
        }

        // Update season's total episodes count
        sqlx::query(
            "UPDATE seasons SET total_episodes = (SELECT COUNT(*) FROM episodes WHERE season_id = ?) WHERE id = ?"
        )
        .bind(request.season_id)
        .bind(request.season_id)
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok(created_episodes)
    }

    // Update episode
    pub async fn update_episode(&self, episode_id: i64, request: UpdateEpisodeRequest) -> Result<Episode, AppError> {
        // Get current episode to preserve existing values
        let current_episode = sqlx::query_as::<_, Episode>(
            "SELECT id, season_id, episode_number, title, description, encrypted_url, image_url, created_at FROM episodes WHERE id = ?"
        )
        .bind(episode_id)
        .fetch_one(&self.db)
        .await?;

        // Use provided values or keep existing ones
        let title = request.title.unwrap_or(current_episode.title);
        let description = request.description.or(current_episode.description);
        let image_url = request.image_url.or(current_episode.image_url);

        // Handle stream URL - NO ENCRYPTION for development
        let encrypted_url = if let Some(stream_url) = request.stream_url {
            stream_url  // Store URL directly without encryption
        } else {
            current_episode.encrypted_url
        };

        // Update the episode
        let updated_episode = sqlx::query_as::<_, Episode>(
            r#"
            UPDATE episodes
            SET title = ?, description = ?, encrypted_url = ?, image_url = ?
            WHERE id = ?
            RETURNING id, season_id, episode_number, title, description, encrypted_url, image_url, created_at
            "#
        )
        .bind(&title)
        .bind(&description)
        .bind(&encrypted_url)
        .bind(&image_url)
        .bind(episode_id)
        .fetch_one(&self.db)
        .await?;

        Ok(updated_episode)
    }

    // Get episode with decrypted URL for delivery
    pub async fn get_episode_for_delivery(&self, episode_id: i64) -> Result<Option<EpisodeWithDecryptedUrl>, AppError> {
        let episode = sqlx::query_as::<_, Episode>(
            "SELECT id, season_id, episode_number, title, description, encrypted_url, image_url, created_at FROM episodes WHERE id = ?"
        )
        .bind(episode_id)
        .fetch_optional(&self.db)
        .await?;

        match episode {
            Some(episode) => {
                // URLs are now direct (no decryption needed)
                let decrypted_url = episode.encrypted_url.clone();

                Ok(Some(EpisodeWithDecryptedUrl {
                    id: episode.id,
                    season_id: episode.season_id,
                    episode_number: episode.episode_number,
                    title: episode.title,
                    description: episode.description,
                    stream_url: decrypted_url,
                    image_url: episode.image_url,
                    created_at: episode.created_at,
                }))
            },
            None => Ok(None),
        }
    }

    // Search series by title or TMDB ID (Req 6.1)
    pub async fn search_series(&self, query: &str) -> Result<Vec<SeriesWithDetails>, AppError> {
        let search_pattern = format!("%{}%", query);
        let tmdb_id = query.parse::<i64>().unwrap_or(-1);

        // Search by title or TMDB ID
        let series_data = sqlx::query!(
            r#"
            SELECT s.id, s.content_id, s.total_seasons, s.created_at as series_created_at,
                   c.category_id, c.title, c.description, c.image_url,
                   c.encrypted_stream_url, c.tmdb_id, c.year, c.created_at as content_created_at
            FROM series s
            JOIN content c ON s.content_id = c.id
            WHERE c.title LIKE ? OR c.description LIKE ? OR c.tmdb_id = ?
            ORDER BY c.title
            LIMIT 20
            "#,
            search_pattern,
            search_pattern,
            tmdb_id
        )
        .fetch_all(&self.db)
        .await?;

        let mut results = Vec::new();
        for data in series_data {
            let content = Content {
                id: data.content_id.unwrap_or(0),
                category_id: data.category_id.unwrap_or(0),
                title: data.title,
                description: data.description,
                image_url: data.image_url,
                backdrop_url: None, // Not available in this query
                encrypted_stream_url: data.encrypted_stream_url.unwrap_or_default(),
                tmdb_id: data.tmdb_id.map(|x| x as i64),
                year: data.year.map(|x| x as i32),
                created_at: chrono::DateTime::from_naive_utc_and_offset(data.content_created_at.unwrap_or_default(), chrono::Utc),
                provider: None,
                country: None,
                format: None,
                channel_number: None,
                active: true,
                is_live: None,
                drm_keys: None,
            };

            let seasons = self.get_seasons_with_episodes(data.id).await?;

            results.push(SeriesWithDetails {
                id: data.id,
                content,
                total_seasons: data.total_seasons.unwrap_or(0) as i32,
                seasons,
                created_at: chrono::DateTime::from_naive_utc_and_offset(data.series_created_at.unwrap_or_default(), chrono::Utc),
            });
        }

        Ok(results)
    }

    // Get all series with pagination
    pub async fn get_all_series(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<SeriesWithDetails>, AppError> {
        let page = page.unwrap_or(1).max(1);
        let limit = limit.unwrap_or(10).min(50).max(1);
        let offset = (page - 1) * limit;

        let series_data = sqlx::query!(
            r#"
            SELECT s.id, s.content_id, s.total_seasons, s.created_at as series_created_at,
                   c.category_id, c.title, c.description, c.image_url,
                   c.encrypted_stream_url, c.tmdb_id, c.year, c.created_at as content_created_at
            FROM series s
            JOIN content c ON s.content_id = c.id
            ORDER BY c.title
            LIMIT ? OFFSET ?
            "#,
            limit,
            offset
        )
        .fetch_all(&self.db)
        .await?;

        let mut results = Vec::new();
        for data in series_data {
            let content = Content {
                id: data.content_id.unwrap_or(0),
                category_id: data.category_id.unwrap_or(0),
                title: data.title,
                description: data.description,
                image_url: data.image_url,
                backdrop_url: None, // Not available in this query
                encrypted_stream_url: data.encrypted_stream_url.unwrap_or_default(),
                tmdb_id: data.tmdb_id.map(|x| x as i64),
                year: data.year.map(|x| x as i32),
                created_at: chrono::DateTime::from_naive_utc_and_offset(data.content_created_at.unwrap_or_default(), chrono::Utc),
                provider: None,
                country: None,
                format: None,
                channel_number: None,
                is_live: None,
                active: true,
                drm_keys: None,
            };

            let seasons = self.get_seasons_with_episodes(data.id).await?;

            results.push(SeriesWithDetails {
                id: data.id,
                content,
                total_seasons: data.total_seasons.unwrap_or(0) as i32,
                seasons,
                created_at: chrono::DateTime::from_naive_utc_and_offset(data.series_created_at.unwrap_or_default(), chrono::Utc),
            });
        }

        Ok(results)
    }

    // Specialized content management methods (Req 7.1, 7.2, 7.3, 7.4)

    // XXX content management (Req 7.1) - Simple name, URL, image
    pub async fn create_xxx_content(&self, request: CreateXxxContentRequest) -> Result<Content, AppError> {
        // Get XXX category
        let xxx_category = self.get_category_by_type("XXX").await?
            .ok_or_else(|| AppError::BadRequest("XXX category not found".to_string()))?;

        // Create content request for XXX content (simple: name, URL, image)
        let content_request = CreateContentRequest {
            category_id: xxx_category.id,
            title: request.name,
            description: None, // XXX content only requires name, URL, image
            image_url: request.image_url,
            backdrop_url: None, // XXX content doesn't use backdrop
            stream_url: request.stream_url,
            tmdb_id: None, // XXX content doesn't use TMDB
            year: None,
            provider: None,
            country: None,
            format: None,
            channel_number: None,
            is_live: None,
            drm_keys: None,
        };

        self.create_content_manual(content_request).await
    }

    // Get XXX content with pagination
    pub async fn get_xxx_content(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<Content>, AppError> {
        let xxx_category = self.get_category_by_type("XXX").await?
            .ok_or_else(|| AppError::BadRequest("XXX category not found".to_string()))?;

        let page = page.unwrap_or(1).max(1);
        let limit = limit.unwrap_or(20).min(100).max(1);
        let offset = (page - 1) * limit;

        let content = sqlx::query_as::<_, Content>(
            r#"
            SELECT id, category_id, title, description, image_url,
                   encrypted_stream_url, tmdb_id, year, active, created_at,
                   provider, country, format, channel_number, is_live
            FROM content
            WHERE category_id = ?
            ORDER BY title
            LIMIT ? OFFSET ?
            "#
        )
        .bind(xxx_category.id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.db)
        .await?;

        Ok(content)
    }

    // Kids content management (Req 7.4) - With TMDB integration when available
    pub async fn create_kids_content(&self, request: CreateKidsContentRequest) -> Result<Content, AppError> {
        // Get Kids category
        let kids_category = self.get_category_by_type("Kids").await?
            .ok_or_else(|| AppError::BadRequest("Kids category not found".to_string()))?;

        // Try to use TMDB if available and tmdb_id is provided
        if let (Some(tmdb_id), Some(tmdb_service)) = (request.tmdb_id, &self.tmdb_service) {
            println!("DEBUG: Kids content - TMDB ID provided: {}", tmdb_id);
            if tmdb_service.is_available().await.unwrap_or(false) {
                println!("DEBUG: Kids content - TMDB service available");
                // Try to get details from TMDB
                if let Ok(movie) = tmdb_service.get_movie_details(tmdb_id).await {
                    let year = movie.release_date.as_ref().and_then(|date| TmdbService::extract_year(date));
                    let poster_url = movie.poster_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w500"));

                    let backdrop_url = movie.backdrop_path.as_ref().map(|path| tmdb_service.build_image_url(path, "w1280"));

                    println!("DEBUG: Kids content - TMDB backdrop_path={:?}, backdrop_url={:?}", movie.backdrop_path, backdrop_url);

                    let content_request = CreateContentRequest {
                        category_id: kids_category.id,
                        title: movie.title,
                        description: movie.overview,
                        image_url: poster_url,
                        backdrop_url, // Include backdrop for Kids content
                        stream_url: request.stream_url,
                        tmdb_id: Some(tmdb_id),
                        year,
                        provider: None,
                        country: None,
                        format: None,
                        channel_number: None,
                        is_live: None,
                        drm_keys: None,
                    };

                    println!("DEBUG: Kids content - About to create with backdrop_url={:?}", content_request.backdrop_url);
                    return self.create_content_manual(content_request).await;
                }
            }
        }

        // Fallback to manual creation
        let content_request = CreateContentRequest {
            category_id: kids_category.id,
            title: request.title,
            description: request.description,
            image_url: request.image_url,
            backdrop_url: None, // Manual entry doesn't have backdrop
            stream_url: request.stream_url,
            tmdb_id: request.tmdb_id,
            year: request.year,
            provider: None,
            country: None,
            format: None,
            channel_number: None,
            is_live: None,
            drm_keys: None,
        };

        self.create_content_manual(content_request).await
    }

    // Anime content management (Req 7.4) - With TMDB integration when available
    pub async fn create_anime_content(&self, request: CreateAnimeContentRequest) -> Result<Content, AppError> {
        // Get Anime category
        let anime_category = self.get_category_by_type("Anime").await?
            .ok_or_else(|| AppError::BadRequest("Anime category not found".to_string()))?;

        // Try to use TMDB if available and tmdb_id is provided
        if let (Some(tmdb_id), Some(tmdb_service)) = (request.tmdb_id, &self.tmdb_service) {
            if tmdb_service.is_available().await.unwrap_or(false) {
                // Try to get details from TMDB (could be movie or TV show)
                if let Ok(tv) = tmdb_service.get_tv_details(tmdb_id).await {
                    let year = tv.first_air_date.as_ref().and_then(|date| TmdbService::extract_year(date));
                    let poster_url = tv.poster_path.map(|path| tmdb_service.build_image_url(&path, "w500"));

                    let content_request = CreateContentRequest {
                        category_id: anime_category.id,
                        title: tv.name,
                        description: tv.overview,
                        image_url: poster_url,
                        backdrop_url: None, // Anime content doesn't use backdrop
                        stream_url: request.stream_url,
                        tmdb_id: Some(tmdb_id),
                        year,
                        provider: None,
                        country: None,
                        format: None,
                        channel_number: None,
                        is_live: None,
                        drm_keys: None,
                    };

                    return self.create_content_manual(content_request).await;
                } else if let Ok(movie) = tmdb_service.get_movie_details(tmdb_id).await {
                    let year = movie.release_date.as_ref().and_then(|date| TmdbService::extract_year(date));
                    let poster_url = movie.poster_path.map(|path| tmdb_service.build_image_url(&path, "w500"));

                    let backdrop_url = movie.backdrop_path.map(|path| tmdb_service.build_image_url(&path, "w1280"));

                    let content_request = CreateContentRequest {
                        category_id: anime_category.id,
                        title: movie.title,
                        description: movie.overview,
                        image_url: poster_url,
                        backdrop_url, // Include backdrop for Anime content
                        stream_url: request.stream_url,
                        tmdb_id: Some(tmdb_id),
                        year,
                        provider: None,
                        country: None,
                        format: None,
                        channel_number: None,
                        is_live: None,
                        drm_keys: None,
                    };

                    return self.create_content_manual(content_request).await;
                }
            }
        }

        // Fallback to manual creation
        let content_request = CreateContentRequest {
            category_id: anime_category.id,
            title: request.title,
            description: request.description,
            image_url: request.image_url,
            backdrop_url: None, // Manual entry doesn't have backdrop
            stream_url: request.stream_url,
            tmdb_id: request.tmdb_id,
            year: request.year,
            provider: None,
            country: None,
            format: None,
            channel_number: None,
            is_live: None,
            drm_keys: None,
        };

        self.create_content_manual(content_request).await
    }

    // Get Kids content with pagination
    pub async fn get_kids_content(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<Content>, AppError> {
        let kids_category = self.get_category_by_type("Kids").await?
            .ok_or_else(|| AppError::BadRequest("Kids category not found".to_string()))?;

        let page = page.unwrap_or(1).max(1);
        let limit = limit.unwrap_or(20).min(100).max(1);
        let offset = (page - 1) * limit;

        let content = sqlx::query_as::<_, Content>(
            r#"
            SELECT id, category_id, title, description, image_url, backdrop_url,
                   encrypted_stream_url, tmdb_id, year, active, created_at,
                   provider, country, format, channel_number, is_live, drm_keys
            FROM content
            WHERE category_id = ?
            ORDER BY title
            LIMIT ? OFFSET ?
            "#
        )
        .bind(kids_category.id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.db)
        .await?;

        Ok(content)
    }

    // Get Anime content with pagination
    pub async fn get_anime_content(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<Content>, AppError> {
        let anime_category = self.get_category_by_type("Anime").await?
            .ok_or_else(|| AppError::BadRequest("Anime category not found".to_string()))?;

        let page = page.unwrap_or(1).max(1);
        let limit = limit.unwrap_or(20).min(100).max(1);
        let offset = (page - 1) * limit;

        let content = sqlx::query_as::<_, Content>(
            r#"
            SELECT id, category_id, title, description, image_url, backdrop_url,
                   encrypted_stream_url, tmdb_id, year, active, created_at,
                   provider, country, format, channel_number, is_live, drm_keys
            FROM content
            WHERE category_id = ?
            ORDER BY title
            LIMIT ? OFFSET ?
            "#
        )
        .bind(anime_category.id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.db)
        .await?;

        Ok(content)
    }

    // Create novela from TMDB with seasons and episodes (CLONE of series system)
    pub async fn create_novela_from_tmdb(&self, request: CreateNovelaFromTmdbRequest) -> Result<NovelaWithChapters, AppError> {
        // Get Novelas category
        let novelas_category = self.get_category_by_type("Novelas").await?
            .ok_or_else(|| AppError::BadRequest("Novelas category not found".to_string()))?;

        // Get TMDB service
        let tmdb_service = self.tmdb_service.as_ref()
            .ok_or_else(|| AppError::BadRequest("TMDB service not available".to_string()))?;

        // Fetch TV show details from TMDB
        let tv_details = tmdb_service.get_tv_details(request.tmdb_id).await?;
        let year = tv_details.first_air_date.as_ref().and_then(|date| TmdbService::extract_year(date));
        let poster_url = tv_details.poster_path.map(|path| tmdb_service.build_image_url(&path, "w500"));
        let backdrop_url = tv_details.backdrop_path.map(|path| tmdb_service.build_image_url(&path, "w1280"));

        // Create content entry for the novela in Novelas category
        let content = sqlx::query_as::<_, Content>(
            r#"
            INSERT INTO content (category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, provider, country, format, channel_number, is_live, drm_keys)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys
            "#
        )
        .bind(novelas_category.id)
        .bind(&tv_details.name)
        .bind(&tv_details.overview)
        .bind(&poster_url)
        .bind(&backdrop_url)
        .bind("") // encrypted_stream_url - empty for series
        .bind(request.tmdb_id)
        .bind(year)
        .bind(true) // active
        .bind(None::<String>) // provider
        .bind(None::<String>) // country
        .bind(None::<String>) // format
        .bind(None::<i32>) // channel_number
        .bind(None::<bool>) // is_live
        .bind(None::<String>) // drm_keys
        .fetch_one(&self.db)
        .await?;

        // Create novela entry (using novelas table)
        let novela = sqlx::query_as::<_, Novela>(
            r#"
            INSERT INTO novelas (content_id, title, description, image_url, total_chapters)
            VALUES (?, ?, ?, ?, ?)
            RETURNING id, content_id, title, description, image_url, total_chapters, created_at
            "#
        )
        .bind(content.id)
        .bind(&tv_details.name)
        .bind(&tv_details.overview)
        .bind(&poster_url)
        .bind(tv_details.number_of_seasons) // Use number of seasons as total_chapters for now
        .fetch_one(&self.db)
        .await?;

        // Create seasons and episodes (CLONE of series system)
        let chapters = Vec::new(); // Keep for compatibility, but will be empty

        for season_number in 1..=tv_details.number_of_seasons {
            // Get season details from TMDB
            let season_details = tmdb_service.get_season_details(request.tmdb_id, season_number).await?;

            // Create novela season
            let season_title = season_details.name;
            let season_description = season_details.overview.unwrap_or_else(|| format!("Temporada {} de {}", season_number, tv_details.name));
            let season_poster = season_details.poster_path.map(|path| tmdb_service.build_image_url(&path, "w500"));

            let created_season = sqlx::query_as::<_, NovelaSeason>(
                r#"
                INSERT INTO novela_seasons (novela_id, season_number, title, description, poster_url)
                VALUES (?, ?, ?, ?, ?)
                RETURNING id, novela_id, season_number, title, description, poster_url, created_at
                "#
            )
            .bind(novela.id)
            .bind(season_number)
            .bind(&season_title)
            .bind(&season_description)
            .bind(&season_poster)
            .fetch_one(&self.db)
            .await?;

            // Create episodes for this season
            for episode in season_details.episodes {
                let episode_title = episode.name;
                let episode_description = episode.overview.unwrap_or_else(|| format!("Temporada {} - Episodio {}", season_number, episode.episode_number));
                let episode_image = episode.still_path.map(|path| tmdb_service.build_image_url(&path, "w500"));

                // Check if we have a URL for this episode
                let episode_key = format!("{}-{}", season_number, episode.episode_number);
                let encrypted_url = if let Some(ref urls) = request.episode_urls {
                    if let Some(url) = urls.get(&episode_key) {
                        if !url.is_empty() {
                            url.clone() // Store URL directly (no encryption)
                        } else {
                            String::new()
                        }
                    } else {
                        String::new()
                    }
                } else {
                    String::new()
                };

                let _created_episode = sqlx::query_as::<_, NovelaEpisode>(
                    r#"
                    INSERT INTO novela_episodes (novela_season_id, episode_number, title, description, encrypted_url, image_url, runtime)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    RETURNING id, novela_season_id, episode_number, title, description, encrypted_url, image_url, runtime, created_at
                    "#
                )
                .bind(created_season.id)
                .bind(episode.episode_number)
                .bind(&episode_title)
                .bind(&episode_description)
                .bind(&encrypted_url)
                .bind(&episode_image)
                .bind(episode.runtime)
                .fetch_one(&self.db)
                .await?;
            }
        }

        Ok(NovelaWithChapters {
            id: novela.id,
            content,
            total_chapters: novela.total_chapters,
            chapters,
            created_at: novela.created_at,
        })
    }

    // Novelas management with chapters (Req 7.2)
    pub async fn create_novela(&self, request: CreateNovelaRequest) -> Result<NovelaWithChapters, AppError> {
        // Get Novelas category
        let novelas_category = self.get_category_by_type("Novelas").await?
            .ok_or_else(|| AppError::BadRequest("Novelas category not found".to_string()))?;

        // Create content entry for the novela
        let content_request = CreateContentRequest {
            category_id: novelas_category.id,
            title: request.title,
            description: request.description,
            image_url: request.image_url,
            backdrop_url: None, // Novelas don't use backdrop
            stream_url: "".to_string(), // Novelas don't have direct stream URLs
            tmdb_id: None, // Novelas typically don't use TMDB
            year: request.year,
            provider: None,
            country: None,
            format: None,
            channel_number: None,
            is_live: None,
            drm_keys: None,
        };

        let content = self.create_content_manual(content_request).await?;

        // Create novela entry
        let novela = sqlx::query_as::<_, Novela>(
            r#"
            INSERT INTO novelas (content_id, total_chapters)
            VALUES (?, 0)
            RETURNING id, content_id, total_chapters, created_at
            "#
        )
        .bind(content.id)
        .fetch_one(&self.db)
        .await?;

        Ok(NovelaWithChapters {
            id: novela.id,
            content,
            total_chapters: novela.total_chapters,
            chapters: Vec::new(),
            created_at: novela.created_at,
        })
    }

    // Create single chapter for novela
    pub async fn create_chapter(&self, request: CreateChapterRequest) -> Result<NovelaChapter, AppError> {
        // Verify novela exists
        let _novela = sqlx::query_as::<_, Novela>(
            "SELECT id, content_id, total_chapters, created_at FROM novelas WHERE id = ?"
        )
        .bind(request.novela_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or_else(|| AppError::NotFound)?;

        // Check if chapter already exists
        let existing = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM novela_chapters WHERE novela_id = ? AND chapter_number = ?"
        )
        .bind(request.novela_id)
        .bind(request.chapter_number)
        .fetch_one(&self.db)
        .await?;

        if existing > 0 {
            return Err(AppError::BadRequest("Chapter already exists".to_string()));
        }

        // Store stream URL directly (no encryption)
        let encrypted_url = request.stream_url;

        // Create chapter
        let chapter = sqlx::query_as::<_, NovelaChapter>(
            r#"
            INSERT INTO novela_chapters (novela_id, chapter_number, title, description, encrypted_url, image_url)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING id, novela_id, chapter_number, title, description, encrypted_url, image_url, created_at
            "#
        )
        .bind(request.novela_id)
        .bind(request.chapter_number)
        .bind(&request.title)
        .bind(&request.description)
        .bind(&encrypted_url)
        .bind(&request.image_url)
        .fetch_one(&self.db)
        .await?;

        // Update novela's total chapters count
        sqlx::query(
            "UPDATE novelas SET total_chapters = (SELECT COUNT(*) FROM novela_chapters WHERE novela_id = ?) WHERE id = ?"
        )
        .bind(request.novela_id)
        .bind(request.novela_id)
        .execute(&self.db)
        .await?;

        Ok(chapter)
    }

    // Create multiple chapters for rapid insertion
    pub async fn create_multiple_chapters(&self, request: CreateMultipleChaptersRequest) -> Result<Vec<NovelaChapter>, AppError> {
        // Verify novela exists
        let _novela = sqlx::query_as::<_, Novela>(
            "SELECT id, content_id, total_chapters, created_at FROM novelas WHERE id = ?"
        )
        .bind(request.novela_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or_else(|| AppError::NotFound)?;

        let mut created_chapters = Vec::new();

        // Create chapters in a transaction for consistency
        let mut tx = self.db.begin().await?;

        for chapter_data in request.chapters {
            // Check if chapter already exists
            let existing = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM novela_chapters WHERE novela_id = ? AND chapter_number = ?"
            )
            .bind(request.novela_id)
            .bind(chapter_data.chapter_number)
            .fetch_one(&mut *tx)
            .await?;

            if existing > 0 {
                return Err(AppError::BadRequest(
                    format!("Chapter {} already exists", chapter_data.chapter_number)
                ));
            }

            // Store stream URL directly (no encryption)
            let encrypted_url = chapter_data.stream_url;

            // Create chapter
            let chapter = sqlx::query_as::<_, NovelaChapter>(
                r#"
                INSERT INTO novela_chapters (novela_id, chapter_number, title, description, encrypted_url, image_url)
                VALUES (?, ?, ?, ?, ?, ?)
                RETURNING id, novela_id, chapter_number, title, description, encrypted_url, image_url, created_at
                "#
            )
            .bind(request.novela_id)
            .bind(chapter_data.chapter_number)
            .bind(&chapter_data.title)
            .bind(&chapter_data.description)
            .bind(&encrypted_url)
            .bind(&chapter_data.image_url)
            .fetch_one(&mut *tx)
            .await?;

            created_chapters.push(chapter);
        }

        // Update novela's total chapters count
        sqlx::query(
            "UPDATE novelas SET total_chapters = (SELECT COUNT(*) FROM novela_chapters WHERE novela_id = ?) WHERE id = ?"
        )
        .bind(request.novela_id)
        .bind(request.novela_id)
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok(created_chapters)
    }

    // Update chapter
    pub async fn update_chapter(&self, chapter_id: i64, request: UpdateChapterRequest) -> Result<NovelaChapter, AppError> {
        // Get existing chapter
        let existing_chapter = sqlx::query_as::<_, NovelaChapter>(
            "SELECT id, novela_id, chapter_number, title, description, encrypted_url, image_url, created_at FROM novela_chapters WHERE id = ?"
        )
        .bind(chapter_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or_else(|| AppError::NotFound)?;

        // Prepare update values
        let title = request.title.unwrap_or(existing_chapter.title);
        let description = request.description.or(existing_chapter.description);
        let encrypted_url = if let Some(stream_url) = request.stream_url {
            stream_url // Store URL directly (no encryption)
        } else {
            existing_chapter.encrypted_url
        };
        let image_url = request.image_url.or(existing_chapter.image_url);

        // Update chapter
        let updated_chapter = sqlx::query_as::<_, NovelaChapter>(
            r#"
            UPDATE novela_chapters
            SET title = ?, description = ?, encrypted_url = ?, image_url = ?
            WHERE id = ?
            RETURNING id, novela_id, chapter_number, title, description, encrypted_url, image_url, created_at
            "#
        )
        .bind(&title)
        .bind(&description)
        .bind(&encrypted_url)
        .bind(&image_url)
        .bind(chapter_id)
        .fetch_one(&self.db)
        .await?;

        Ok(updated_chapter)
    }

    // NOVELA SEASONS METHODS (Clone of series seasons)
    pub async fn get_novela_seasons(&self, novela_id: i64) -> Result<Vec<NovelaSeason>, AppError> {
        let seasons = sqlx::query_as::<_, NovelaSeason>(
            "SELECT id, novela_id, season_number, title, description, poster_url, created_at FROM novela_seasons WHERE novela_id = ? ORDER BY season_number"
        )
        .bind(novela_id)
        .fetch_all(&self.db)
        .await?;

        Ok(seasons)
    }

    pub async fn create_novela_season(&self, novela_id: i64, season_number: i32, title: Option<String>, description: Option<String>, poster_url: Option<String>) -> Result<NovelaSeason, AppError> {
        let season = sqlx::query_as::<_, NovelaSeason>(
            r#"
            INSERT INTO novela_seasons (novela_id, season_number, title, description, poster_url)
            VALUES (?, ?, ?, ?, ?)
            RETURNING id, novela_id, season_number, title, description, poster_url, created_at
            "#
        )
        .bind(novela_id)
        .bind(season_number)
        .bind(title)
        .bind(description)
        .bind(poster_url)
        .fetch_one(&self.db)
        .await?;

        Ok(season)
    }

    pub async fn update_novela_season(&self, season_id: i64, title: Option<String>, description: Option<String>, poster_url: Option<String>) -> Result<NovelaSeason, AppError> {
        let season = sqlx::query_as::<_, NovelaSeason>(
            r#"
            UPDATE novela_seasons
            SET title = ?, description = ?, poster_url = ?
            WHERE id = ?
            RETURNING id, novela_id, season_number, title, description, poster_url, created_at
            "#
        )
        .bind(title)
        .bind(description)
        .bind(poster_url)
        .bind(season_id)
        .fetch_one(&self.db)
        .await?;

        Ok(season)
    }

    pub async fn delete_novela_season(&self, season_id: i64) -> Result<(), AppError> {
        sqlx::query("DELETE FROM novela_seasons WHERE id = ?")
            .bind(season_id)
            .execute(&self.db)
            .await?;

        Ok(())
    }

    // NOVELA EPISODES METHODS (Clone of episodes)
    pub async fn get_novela_season_episodes(&self, season_id: i64) -> Result<Vec<NovelaEpisode>, AppError> {
        let episodes = sqlx::query_as::<_, NovelaEpisode>(
            "SELECT id, novela_season_id, episode_number, title, description, encrypted_url, image_url, runtime, created_at FROM novela_episodes WHERE novela_season_id = ? ORDER BY episode_number"
        )
        .bind(season_id)
        .fetch_all(&self.db)
        .await?;

        Ok(episodes)
    }

    pub async fn create_novela_episode(&self, season_id: i64, episode_number: i32, title: String, description: Option<String>, encrypted_url: String, image_url: Option<String>, runtime: Option<i32>) -> Result<NovelaEpisode, AppError> {
        let episode = sqlx::query_as::<_, NovelaEpisode>(
            r#"
            INSERT INTO novela_episodes (novela_season_id, episode_number, title, description, encrypted_url, image_url, runtime)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            RETURNING id, novela_season_id, episode_number, title, description, encrypted_url, image_url, runtime, created_at
            "#
        )
        .bind(season_id)
        .bind(episode_number)
        .bind(title)
        .bind(description)
        .bind(encrypted_url)
        .bind(image_url)
        .bind(runtime)
        .fetch_one(&self.db)
        .await?;

        Ok(episode)
    }

    pub async fn update_novela_episode(&self, episode_id: i64, title: String, description: Option<String>, encrypted_url: String, image_url: Option<String>, runtime: Option<i32>) -> Result<NovelaEpisode, AppError> {
        let episode = sqlx::query_as::<_, NovelaEpisode>(
            r#"
            UPDATE novela_episodes
            SET title = ?, description = ?, encrypted_url = ?, image_url = ?, runtime = ?
            WHERE id = ?
            RETURNING id, novela_season_id, episode_number, title, description, encrypted_url, image_url, runtime, created_at
            "#
        )
        .bind(title)
        .bind(description)
        .bind(encrypted_url)
        .bind(image_url)
        .bind(runtime)
        .bind(episode_id)
        .fetch_one(&self.db)
        .await?;

        Ok(episode)
    }

    pub async fn delete_novela_episode(&self, episode_id: i64) -> Result<(), AppError> {
        sqlx::query("DELETE FROM novela_episodes WHERE id = ?")
            .bind(episode_id)
            .execute(&self.db)
            .await?;

        Ok(())
    }

    // Get novela episode with decrypted URL for editing
    pub async fn get_novela_episode_for_editing(&self, episode_id: i64) -> Result<Option<NovelaEpisodeWithDecryptedUrl>, AppError> {
        let episode = sqlx::query_as::<_, NovelaEpisode>(
            "SELECT id, novela_season_id, episode_number, title, description, encrypted_url, image_url, runtime, created_at FROM novela_episodes WHERE id = ?"
        )
        .bind(episode_id)
        .fetch_optional(&self.db)
        .await?;

        match episode {
            Some(episode) => {
                // For novelas, we don't use encryption (that's only for Live TV with DRM)
                // So we return the encrypted_url field directly as stream_url
                let stream_url = episode.encrypted_url.clone();

                Ok(Some(NovelaEpisodeWithDecryptedUrl {
                    id: episode.id,
                    novela_season_id: episode.novela_season_id,
                    episode_number: episode.episode_number,
                    title: episode.title,
                    description: episode.description,
                    stream_url,
                    image_url: episode.image_url,
                    runtime: episode.runtime,
                    created_at: episode.created_at,
                }))
            },
            None => Ok(None),
        }
    }

    // Get novela by content ID with all chapters (supports both old and new systems)
    pub async fn get_novela_by_content_id(&self, content_id: i64) -> Result<Option<NovelaWithChapters>, AppError> {
        // First get the novela by content_id
        let novela = sqlx::query_as::<_, Novela>(
            "SELECT id, content_id, title, description, image_url, total_chapters, created_at FROM novelas WHERE content_id = ?"
        )
        .bind(content_id)
        .fetch_optional(&self.db)
        .await?;

        if let Some(novela) = novela {
            // Get the content details
            let content = sqlx::query_as::<_, Content>(
                "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
            )
            .bind(content_id)
            .fetch_one(&self.db)
            .await?;

            // Check if this novela uses the new season/episode system
            let seasons_count = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM novela_seasons WHERE novela_id = ?"
            )
            .bind(novela.id)
            .fetch_one(&self.db)
            .await?;

            let chapters = if seasons_count > 0 {
                // New system: Convert episodes from all seasons to chapters
                let all_episodes = sqlx::query_as::<_, NovelaEpisode>(
                    r#"
                    SELECT ne.id, ne.novela_season_id, ne.episode_number, ne.title, ne.description, ne.encrypted_url, ne.image_url, ne.runtime, ne.created_at
                    FROM novela_episodes ne
                    JOIN novela_seasons ns ON ne.novela_season_id = ns.id
                    WHERE ns.novela_id = ?
                    ORDER BY ns.season_number, ne.episode_number
                    "#
                )
                .bind(novela.id)
                .fetch_all(&self.db)
                .await?;

                // Convert episodes to chapters format
                all_episodes.into_iter().enumerate().map(|(index, episode)| NovelaChapter {
                    id: episode.id,
                    novela_id: novela.id,
                    chapter_number: (index + 1) as i32, // Sequential chapter numbering
                    title: episode.title,
                    description: episode.description,
                    encrypted_url: episode.encrypted_url,
                    image_url: episode.image_url,
                    created_at: episode.created_at,
                }).collect()
            } else {
                // Old system: Get chapters directly
                sqlx::query_as::<_, NovelaChapter>(
                    "SELECT id, novela_id, chapter_number, title, description, encrypted_url, image_url, created_at FROM novela_chapters WHERE novela_id = ? ORDER BY chapter_number"
                )
                .bind(novela.id)
                .fetch_all(&self.db)
                .await?
            };

            Ok(Some(NovelaWithChapters {
                id: novela.id,
                content,
                total_chapters: chapters.len() as i32,
                chapters,
                created_at: novela.created_at,
            }))
        } else {
            Ok(None)
        }
    }

    // Get novela by ID with all chapters
    pub async fn get_novela_by_id(&self, id: i64) -> Result<Option<NovelaWithChapters>, AppError> {
        // Get novela first
        let novela = sqlx::query_as::<_, Novela>(
            "SELECT id, content_id, title, description, image_url, total_chapters, created_at FROM novelas WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.db)
        .await?;

        if let Some(novela) = novela {
            // Get content
            let content = sqlx::query_as::<_, Content>(
                "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
            )
            .bind(novela.content_id)
            .fetch_one(&self.db)
            .await?;

            // Get chapters
            let chapters = sqlx::query_as::<_, NovelaChapter>(
                "SELECT id, novela_id, chapter_number, title, description, encrypted_url, image_url, created_at FROM novela_chapters WHERE novela_id = ? ORDER BY chapter_number"
            )
            .bind(id)
            .fetch_all(&self.db)
            .await?;

            Ok(Some(NovelaWithChapters {
                id: novela.id,
                content,
                total_chapters: novela.total_chapters,
                chapters,
                created_at: novela.created_at,
            }))
        } else {
            Ok(None)
        }
    }

    // Get chapter with decrypted URL for delivery
    pub async fn get_chapter_for_delivery(&self, chapter_id: i64) -> Result<Option<ChapterWithDecryptedUrl>, AppError> {
        let chapter = sqlx::query_as::<_, NovelaChapter>(
            "SELECT id, novela_id, chapter_number, title, description, encrypted_url, image_url, created_at FROM novela_chapters WHERE id = ?"
        )
        .bind(chapter_id)
        .fetch_optional(&self.db)
        .await?;

        match chapter {
            Some(chapter) => {
                // URLs are now direct (no decryption needed)
                let decrypted_url = chapter.encrypted_url.clone();

                Ok(Some(ChapterWithDecryptedUrl {
                    id: chapter.id,
                    novela_id: chapter.novela_id,
                    chapter_number: chapter.chapter_number,
                    title: chapter.title,
                    description: chapter.description,
                    stream_url: decrypted_url,
                    image_url: chapter.image_url,
                    created_at: chapter.created_at,
                }))
            },
            None => Ok(None),
        }
    }

    // Get all novelas with pagination (now gets series from Novelas category)
    pub async fn get_all_novelas(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<NovelaWithChapters>, AppError> {
        // Get Novelas category
        let novelas_category = self.get_category_by_type("Novelas").await?
            .ok_or_else(|| AppError::BadRequest("Novelas category not found".to_string()))?;

        // Get all series and filter by Novelas category
        let all_series = self.get_all_series(page, limit).await?;
        let novela_series: Vec<SeriesWithDetails> = all_series.into_iter()
            .filter(|s| s.content.category_id == novelas_category.id)
            .collect();
        // Convert series to novela format for compatibility
        let mut results = Vec::new();
        for series in novela_series {
            // Get all episodes for this series
            let seasons = self.get_series_seasons(series.id).await?;
            let mut all_episodes = Vec::new();
            for season in seasons {
                let episodes = self.get_season_episodes(season.id).await?;
                all_episodes.extend(episodes);
            }

            // Convert episodes to chapters format
            let chapters: Vec<NovelaChapter> = all_episodes.into_iter().map(|episode| NovelaChapter {
                id: episode.id,
                novela_id: series.id, // Use series ID as novela ID
                chapter_number: episode.episode_number,
                title: episode.title,
                description: episode.description,
                encrypted_url: episode.encrypted_url,
                image_url: episode.image_url,
                created_at: episode.created_at,
            }).collect();

            results.push(NovelaWithChapters {
                id: series.id,
                content: series.content.clone(),
                total_chapters: chapters.len() as i32,
                chapters,
                created_at: series.created_at,
            });
        }

        Ok(results)
    }

    // Events management with daily scheduling (Req 7.3)
    pub async fn create_event(&self, request: CreateEventRequest) -> Result<EventWithSchedules, AppError> {
        // Get Eventos category
        let eventos_category = self.get_category_by_type("Eventos").await?
            .ok_or_else(|| AppError::BadRequest("Eventos category not found".to_string()))?;

        // Validate event type
        if !matches!(request.event_type.as_str(), "live" | "scheduled" | "recurring") {
            return Err(AppError::BadRequest("Invalid event type. Must be 'live', 'scheduled', or 'recurring'".to_string()));
        }

        // Create content entry for the event
        let content_request = CreateContentRequest {
            category_id: eventos_category.id,
            title: request.title,
            description: request.description,
            image_url: request.image_url,
            backdrop_url: None, // Events don't use backdrop
            stream_url: request.stream_url,
            tmdb_id: None, // Events don't use TMDB
            year: None,
            provider: request.provider,
            country: request.country,
            format: request.format,
            channel_number: None,
            is_live: None,
            drm_keys: request.drm_keys,
        };

        let content = self.create_content_manual(content_request).await?;

        // Create event entry
        let event = sqlx::query_as::<_, Event>(
            r#"
            INSERT INTO events (content_id, event_type)
            VALUES (?, ?)
            RETURNING id, content_id, event_type, created_at
            "#
        )
        .bind(content.id)
        .bind(&request.event_type)
        .fetch_one(&self.db)
        .await?;

        Ok(EventWithSchedules {
            id: event.id,
            content,
            event_type: event.event_type,
            schedules: Vec::new(),
            created_at: event.created_at,
        })
    }

    // Create event schedule for daily programming
    pub async fn create_event_schedule(&self, request: CreateEventScheduleRequest) -> Result<EventSchedule, AppError> {
        // Verify event exists
        let _event = sqlx::query_as::<_, Event>(
            "SELECT id, content_id, event_type, created_at FROM events WHERE id = ?"
        )
        .bind(request.event_id)
        .fetch_optional(&self.db)
        .await?
        .ok_or_else(|| AppError::NotFound)?;

        // Check if schedule already exists for this date and time
        let existing = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM event_schedules WHERE event_id = ? AND schedule_date = ? AND start_time = ?"
        )
        .bind(request.event_id)
        .bind(request.schedule_date)
        .bind(request.start_time)
        .fetch_one(&self.db)
        .await?;

        if existing > 0 {
            return Err(AppError::BadRequest("Schedule already exists for this date and time".to_string()));
        }

        // Store stream URL directly (no encryption)
        let encrypted_url = request.stream_url;

        // Create schedule
        let schedule = sqlx::query_as::<_, EventSchedule>(
            r#"
            INSERT INTO event_schedules (event_id, schedule_date, start_time, end_time, encrypted_url, title, description, image_url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id, event_id, schedule_date, start_time, end_time, encrypted_url, title, description, image_url, created_at
            "#
        )
        .bind(request.event_id)
        .bind(request.schedule_date)
        .bind(request.start_time)
        .bind(request.end_time)
        .bind(&encrypted_url)
        .bind(&request.title)
        .bind(&request.description)
        .bind(&request.image_url)
        .fetch_one(&self.db)
        .await?;

        Ok(schedule)
    }

    // Get event by ID with all schedules
    pub async fn get_event_by_id(&self, id: i64) -> Result<Option<EventWithSchedules>, AppError> {
        // Get event first
        let event = sqlx::query_as::<_, Event>(
            "SELECT id, content_id, event_type, created_at FROM events WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.db)
        .await?;

        if let Some(event) = event {
            // Get content
            let content = sqlx::query_as::<_, Content>(
                "SELECT id, category_id, title, description, image_url, backdrop_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
            )
            .bind(event.content_id)
            .fetch_one(&self.db)
            .await?;

            // Get schedules
            let schedules = sqlx::query_as::<_, EventSchedule>(
                "SELECT id, event_id, schedule_date, start_time, end_time, encrypted_url, title, description, image_url, created_at FROM event_schedules WHERE event_id = ? ORDER BY schedule_date, start_time"
            )
            .bind(id)
            .fetch_all(&self.db)
            .await?;

            Ok(Some(EventWithSchedules {
                id: event.id,
                content,
                event_type: event.event_type,
                schedules,
                created_at: event.created_at,
            }))
        } else {
            Ok(None)
        }
    }

    // Get event schedule with decrypted URL for delivery
    pub async fn get_schedule_for_delivery(&self, schedule_id: i64) -> Result<Option<ScheduleWithDecryptedUrl>, AppError> {
        let schedule = sqlx::query_as::<_, EventSchedule>(
            "SELECT id, event_id, schedule_date, start_time, end_time, encrypted_url, title, description, image_url, created_at FROM event_schedules WHERE id = ?"
        )
        .bind(schedule_id)
        .fetch_optional(&self.db)
        .await?;

        match schedule {
            Some(schedule) => {
                // URLs are now direct (no decryption needed)
                let decrypted_url = schedule.encrypted_url.clone();

                Ok(Some(ScheduleWithDecryptedUrl {
                    id: schedule.id,
                    event_id: schedule.event_id,
                    schedule_date: schedule.schedule_date,
                    start_time: schedule.start_time,
                    end_time: schedule.end_time,
                    stream_url: decrypted_url,
                    title: schedule.title,
                    description: schedule.description,
                    image_url: schedule.image_url,
                    created_at: schedule.created_at,
                }))
            },
            None => Ok(None),
        }
    }

    // Get events scheduled for a specific date
    pub async fn get_events_by_date(&self, date: chrono::NaiveDate) -> Result<Vec<EventSchedule>, AppError> {
        let schedules = sqlx::query_as::<_, EventSchedule>(
            "SELECT id, event_id, schedule_date, start_time, end_time, encrypted_url, title, description, image_url, created_at FROM event_schedules WHERE schedule_date = ? ORDER BY start_time"
        )
        .bind(date)
        .fetch_all(&self.db)
        .await?;

        Ok(schedules)
    }

    // Get all events with pagination
    pub async fn get_all_events(&self, page: Option<i32>, limit: Option<i32>) -> Result<Vec<EventWithSchedules>, AppError> {
        let page = page.unwrap_or(1).max(1);
        let limit = limit.unwrap_or(10).min(50).max(1);
        let offset = (page - 1) * limit;

        // Get events with pagination
        let events = sqlx::query_as::<_, Event>(
            "SELECT id, content_id, event_type, created_at FROM events ORDER BY id LIMIT ? OFFSET ?"
        )
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.db)
        .await?;

        let mut results = Vec::new();
        for event in events {
            // Get content for this event
            let content = sqlx::query_as::<_, Content>(
                "SELECT id, category_id, title, description, image_url, encrypted_stream_url, tmdb_id, year, active, created_at, provider, country, format, channel_number, is_live, drm_keys FROM content WHERE id = ?"
            )
            .bind(event.content_id)
            .fetch_one(&self.db)
            .await?;

            // Get schedules for this event
            let schedules = sqlx::query_as::<_, EventSchedule>(
                "SELECT id, event_id, schedule_date, start_time, end_time, encrypted_url, title, description, image_url, created_at FROM event_schedules WHERE event_id = ? ORDER BY schedule_date, start_time"
            )
            .bind(event.id)
            .fetch_all(&self.db)
            .await?;

            results.push(EventWithSchedules {
                id: event.id,
                content,
                event_type: event.event_type,
                schedules,
                created_at: event.created_at,
            });
        }

        Ok(results)
    }

    // Get seasons for a series
    pub async fn get_series_seasons(&self, series_id: i64) -> Result<Vec<Season>, AppError> {
        let seasons = sqlx::query_as::<_, Season>(
            "SELECT id, series_id, season_number, total_episodes, title, description, poster_url, created_at FROM seasons WHERE series_id = ? ORDER BY season_number"
        )
        .bind(series_id)
        .fetch_all(&self.db)
        .await?;

        Ok(seasons)
    }

    // Get episodes for a season
    pub async fn get_season_episodes(&self, season_id: i64) -> Result<Vec<Episode>, AppError> {
        let episodes = sqlx::query_as::<_, Episode>(
            "SELECT id, season_id, episode_number, title, description, encrypted_url, image_url, created_at FROM episodes WHERE season_id = ? ORDER BY episode_number"
        )
        .bind(season_id)
        .fetch_all(&self.db)
        .await?;

        Ok(episodes)
    }

    // Validation helper methods

    // Parental control verification
    pub async fn verify_parental_access(&self, user_id: i64) -> Result<bool, AppError> {
        // Check if user has parental PIN set and session status
        let user = sqlx::query!(
            "SELECT parental_pin, parental_session_expires_at FROM users WHERE id = ?",
            user_id
        )
        .fetch_one(&self.db)
        .await?;

        // Check if user has a parental PIN configured
        match user.parental_pin {
            Some(_) => {
                // User has PIN set - check if they have an active session
                match user.parental_session_expires_at {
                    Some(session_expires_str) => {
                        // Parse the session expiration time
                        match chrono::DateTime::parse_from_rfc3339(&session_expires_str) {
                            Ok(session_expires) => {
                                let now = chrono::Utc::now();
                                if session_expires.with_timezone(&chrono::Utc) > now {
                                    // Session is still valid - allow access
                                    Ok(true)
                                } else {
                                    // Session expired - clear expired session and require PIN verification
                                    let _ = sqlx::query!(
                                        "UPDATE users SET parental_session_expires_at = NULL WHERE id = ?",
                                        user_id
                                    )
                                    .execute(&self.db)
                                    .await;
                                    Err(crate::error::AppError::ParentalControlRequired)
                                }
                            }
                            Err(_) => {
                                // Invalid session format - clear it and require PIN verification
                                let _ = sqlx::query!(
                                    "UPDATE users SET parental_session_expires_at = NULL WHERE id = ?",
                                    user_id
                                )
                                .execute(&self.db)
                                .await;
                                Err(crate::error::AppError::ParentalControlRequired)
                            }
                        }
                    }
                    None => {
                        // No active session - require PIN verification
                        Err(crate::error::AppError::ParentalControlRequired)
                    }
                }
            }
            None => {
                // No PIN set - return specific error to force PIN setup
                Err(crate::error::AppError::ParentalPinNotConfigured)
            }
        }
    }

    pub fn is_valid_category_type(category_type: &str) -> bool {
        matches!(category_type, "VOD" | "TV" | "XXX" | "Novelas" | "Eventos" | "Kids" | "Anime" | "Series")
    }
}